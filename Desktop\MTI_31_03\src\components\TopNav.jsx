import React, { useState, useRef, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { FaEye } from "react-icons/fa";
import { useSelector } from "react-redux";
import Draggable from "react-draggable";
import useClickOutside from "../hooks/useClickOutside";

export const TopNav = ({
  pageCols,
  colsSelectedAll,
  setColsSelectedALL,
  selectAll,
  colVis,
  setColVis,
  setSeq,
  rows,
}) => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const [ columnHideDropDown, setcolumnHideDropDown ] = useState(false);
  const dropdownRef = useRef(null);

  const totalAccounts = useMemo(() => {
    return rows
      ? rows.filter((row) => row.userId && row.broker).length
      : 0;
  }, [ rows ]);

  const loggedAccounts = useMemo(() => {
    return rows ? rows.filter((item) => item.inputDisabled).length : 0;
  }, [ rows ]);

  const totalAccountsStyle = { color: "red" };
  const loggedAccountsStyle = { color: "green" };

  const { positions: netPositions } = useSelector(
    (state) => state.positionReducer
  );
  const { orders: orderBook } = useSelector((state) => state.orderBookReducer);

  const { openPosition, closePosition } = useMemo(() => {
    let openPos = 0,
      closePos = 0;
    netPositions.forEach((position) => {
      if (position.side === "Open") {
        openPos++;
      } else if (position.side === "Close") {
        closePos++;
      }
    });
    return { openPosition: openPos, closePosition: closePos };
  }, [ netPositions ]);

  const buttonRef = useRef(null);

  useClickOutside(dropdownRef, () => setcolumnHideDropDown(false));

  const firstColumn = [
    "userId",
    "broker",
    "availableMargin",
    "utilizedMargin",
    "mobile",
    "apiKey",
    "secretKey",
  ];

  const secondColumn = [
    "qrCode",
    "password",
    "apiUserDetails",
    "name",
    "twoFA",
    "autoLogin",
    "maxProfit",
    "maxLoss",
    "profitLocking",
    "maxLossWait",
  ];

  const thirdColumn = [
    "maxLossPerTrade",
    "marketOrders",
    "maxOpenTrades",
    "qtyMultiplier",
    "exitOrderType",
    "enableCNCSqoff",
    "enableNRMLSqoff",
    "manualExit",
    "mtmAll",
  ];

  const fourthColumn = [
    "qtyOnMaxLossPerTrade",
    "qtyByExposure",
    "commodityMargin",
    "action",
    "historicalApi",
    "net",
    "sqOffTime",
    "tradingAuthorizationReq",
    "email",
  ];

  const columnDisplayNames = {
    action: "Actions",
    userId: "Client ID",
    manualExit: "Manual Exit",
    mtmAll: "MTM (All)",
    availableMargin: "Available Margin",
    name: "Display Name",
    broker: "Broker",
    apiKey: "API Key",
    secretKey: "API Secret Key",
    historicalApi: "Data API",
    qrCode: "QR Code",
    sqOffTime: "Exit Time",
    autoLogin: "Auto Login",
    password: "Password",
    maxProfit: "Max Profit",
    maxLoss: "Max Loss",
    profitLocking: "Profit Locking",
    qtyByExposure: "Qty By Exposure",
    qtyOnMaxLossPerTrade: "Qty on Max Loss/Trade",
    maxLossPerTrade: "Max Loss Per Trade",
    maxOpenTrades: "Max Open Trades",
    qtyMultiplier: "Quantity Multiplier",
    mobile: "Mobile Number",
    email: "Email",
    net: "Net Value",
    marketOrders: "Market Orders",
    enableNRMLSqoff: "Enable NRML SqOff",
    enableCNCSqoff: "Enable CNC SqOff",
    exitOrderType: "Exit Order Type",
    twoFA: "Two-Factor Auth",
    maxLossWait: "Max Loss Wait Time",
    tradingAuthorizationReq: "Trading Auth Required",
    commodityMargin: "Commodity Margin",
    apiUserDetails: "API User Details",
    utilizedMargin: "Utilized Margin",
  };

  const handleSelectAll = (select) => {
    const updatedColVis = {};
    const updatedSeq = [];

    if (select) {
      [
        ...firstColumn,
        ...secondColumn,
        ...thirdColumn,
        ...fourthColumn,
      ].forEach((colName) => {
        updatedColVis[ colName ] = false;
        updatedSeq.push(colName);
      });
    } else {
      [
        ...firstColumn,
        ...secondColumn,
        ...thirdColumn,
        ...fourthColumn,
      ].forEach((colName) => {
        updatedColVis[ colName ] = true;
      });
      updatedSeq.length = 0;
    }

    setColVis(updatedColVis);
    setSeq(updatedSeq);
    setColsSelectedALL(select);
  };

  const handleCheckboxChange = (columnName) => {
    setColVis((prev) => ({
      ...prev,
      [ columnName ]: !prev[ columnName ],
    }));
    setSeq((prev) => {
      if (prev.includes(columnName)) {
        return prev.filter((col) => col !== columnName);
      } else {
        return [ ...prev, columnName ];
      }
    });
  };

  return (
    <div className="second-navbar">
      <div>
        <h2>
          {pathname.includes("UserProfiles") ? (
            <>
              User Profiles(
              <span style={loggedAccountsStyle}>{loggedAccounts}</span>/
              <span style={totalAccountsStyle}>{totalAccounts}</span>)
            </>
          ) : pathname.includes("Strategies") ? (
            "Strategies"
          ) : pathname.includes("F&O") ? (
            "F & O Trading"
          ) : pathname.includes("Positions") ? (
            <>
              Positions(
              <span style={{ color: "green" }}>{openPosition}</span> /
              <span style={{ color: "red" }}>{closePosition}</span>)
            </>
          ) : pathname.includes("Holdings") ? (
            "Holdings"
          ) : pathname.includes("OrderFlow") ? (
            <>
              Order Flow(
              <span style={{ color: "green" }}>{orderBook.length}</span>)
            </>
          ) : pathname.includes("OrderManagement") ? (
            "Order Management"
          ) : pathname.includes("Equity") ? (
            "Equity Trading"
          ) : pathname.includes("Option_Chain") ? (
            "Option Chain"
          ) : pathname.includes("Master_accounts") ? (
            "Master Child"
          ) : null}
        </h2>
      </div>
      <div className="second-potions-div">
        <div id="hide_btn_ref">
          {!pathname.includes("Option_Chain") && (
            <button
              className="hideBtn topNavBtn"
              style={{
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                backgroundColor: columnHideDropDown ? "#4661bd" : "#d8e1ff",
                color: columnHideDropDown ? "white" : "black",
              }}
              ref={buttonRef}
              onClick={() => {
                setcolumnHideDropDown((prev) => !prev);
              }}
            >
              Hide{" "}
              <FaEye
                color={columnHideDropDown ? "white" : "black"}
                className="eye-icon"
              />
            </button>
          )}
        </div>
        {columnHideDropDown && pathname.includes("UserProfiles") && (
          <Draggable>
            <div
              ref={dropdownRef}
              id="dropdown-menu"
              className="dropdown-menu hidedrop-down"
            >
              <div>
                <div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                      backgroundColor: colsSelectedAll ? "#d3f9d8" : "#f9d8d8",
                      padding: "5px",
                      borderRadius: "4px",
                      marginBottom: "5px",
                      marginRight: "4px",
                      marginLeft: "0px",
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={colsSelectedAll}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                    <label>Select All</label>
                  </div>
                </div>
                <label
                  style={{
                    display: "block",
                    marginTop: "5px",
                    fontWeight: "bold",
                  }}
                >
                  User Account & Access
                </label>
                {firstColumn.map((columnName, index) => (
                  <div
                    key={index}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                      backgroundColor: !colVis[ columnName ]
                        ? "#f9d8d8"
                        : "#d3f9d8",
                      padding: "2px",
                      borderRadius: "4px",
                      marginBottom: "5px",
                      marginRight: "4px",
                      marginLeft: "0px",
                    }}
                  >
                    <input
                      id={`checkbox-${columnName}`}
                      type="checkbox"
                      checked={!colVis[ columnName ]}
                      onClick={() => handleCheckboxChange(columnName)}
                    />
                    <label htmlFor={`checkbox-${columnName}`}>
                      <span
                        style={{
                          color: "black",
                          fontWeight: "normal",
                        }}
                      >
                        {columnDisplayNames[ columnName ]}
                      </span>
                    </label>
                  </div>
                ))}
              </div>
              <div>
                {secondColumn.map((columnName, index) => (
                  <React.Fragment key={index}>
                    {columnName === "maxProfit" && (
                      <label
                        style={{
                          display: "block",
                          marginTop: "5px",
                          fontWeight: "bold",
                        }}
                      >
                        Risk Management Options
                      </label>
                    )}
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                        backgroundColor: !colVis[ columnName ]
                          ? "#f9d8d8"
                          : "#d3f9d8",
                        padding: "2px",
                        borderRadius: "4px",
                        marginBottom: "2px",
                        marginRight: "4px",
                      }}
                    >
                      <input
                        id={`checkbox-${columnName}`}
                        type="checkbox"
                        checked={!colVis[ columnName ]}
                        onClick={() => handleCheckboxChange(columnName)}
                      />
                      <label htmlFor={`checkbox-${columnName}`}>
                        <span
                          style={{
                            color: !colVis[ columnName ] ? "black" : "black",
                            fontWeight: !colVis[ columnName ]
                              ? "normal"
                              : "normal",
                          }}
                        >
                          {columnDisplayNames[ columnName ]}
                        </span>
                      </label>
                    </div>
                  </React.Fragment>
                ))}
              </div>
              <div>
                <label
                  style={{
                    display: "block",
                    fontWeight: "bold",
                    textAlign: "center",
                  }}
                >
                  Trading Options
                </label>
                {thirdColumn.map((columnName, index) => (
                  <div
                    key={index}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                      backgroundColor: !colVis[ columnName ]
                        ? "#f9d8d8"
                        : "#d3f9d8",
                      padding: "2px",
                      borderRadius: "4px",
                      marginBottom: "2px",
                      marginRight: "4px",
                    }}
                  >
                    <input
                      id={`checkbox-${columnName}`}
                      type="checkbox"
                      checked={!colVis[ columnName ]}
                      onClick={() => handleCheckboxChange(columnName)}
                    />
                    <label htmlFor={`checkbox-${columnName}`}>
                      <span
                        style={{
                          color: !colVis[ columnName ] ? "black" : "black",
                          fontWeight: !colVis[ columnName ]
                            ? "normal"
                            : "normal",
                        }}
                      >
                        {columnDisplayNames[ columnName ]}
                      </span>
                    </label>
                  </div>
                ))}
              </div>
              <div>
                {fourthColumn.map((columnName, index) => (
                  <React.Fragment key={index}>
                    {columnName === "action" && (
                      <label
                        style={{
                          display: "block",
                          marginTop: "5px",
                          fontWeight: "bold",
                          textAlign: "center",
                        }}
                      >
                        General
                      </label>
                    )}
                    <div
                      key={index}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                        backgroundColor: !colVis[ columnName ]
                          ? "#f9d8d8"
                          : "#d3f9d8",
                        padding: "2px",
                        borderRadius: "4px",
                        marginBottom: "2px",
                        marginRight: "-20px",
                      }}
                    >
                      <input
                        id={`checkbox-${columnName}`}
                        type="checkbox"
                        checked={!colVis[ columnName ]}
                        onClick={() => handleCheckboxChange(columnName)}
                      />
                      <label htmlFor={`checkbox-${columnName}`}>
                        <span
                          style={{
                            color: !colVis[ columnName ] ? "black" : "black",
                            fontWeight: !colVis[ columnName ]
                              ? "normal"
                              : "normal",
                          }}
                        >
                          {columnDisplayNames[ columnName ]}
                        </span>
                      </label>
                    </div>
                  </React.Fragment>
                ))}
              </div>
            </div>
          </Draggable>
        )}
        {columnHideDropDown && !pathname.includes("UserProfiles") && (
          <Draggable>
            <div
              ref={dropdownRef}
              id="dropdown-menu"
              className="dropdown-menu hidedrop-downs"
            >
              <div
                style={{
                  backgroundColor: colsSelectedAll ? "#d3f9d8" : "#f9d8d8",
                }}
              >
                <label>
                  <input
                    checked={colsSelectedAll}
                    type="checkbox"
                    onClick={selectAll}
                  />
                  Select All
                </label>
              </div>

              {Array.from(new Set(pageCols?.map((data) => data)))
                .sort()
                .map((columnName, index) => (
                  <div
                    key={index}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                      backgroundColor: !colVis[ columnName ]
                        ? "#f9d8d8"
                        : "#d3f9d8",
                      padding: "2px",
                      borderRadius: "4px",
                      marginBottom: "2px",
                      marginRight: "4px",
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={!colVis[ columnName ]}
                      onClick={() => {
                        setColVis((prev) => ({
                          ...prev,
                          [ columnName ]: !prev[ columnName ],
                        }));
                        setSeq((prev) => {
                          if (prev.includes(columnName)) {
                            return prev.filter((col) => col !== columnName);
                          } else {
                            return [ ...prev, columnName ];
                          }
                        });
                      }}
                    />
                    <label>
                      <span
                        style={{
                          color: !colVis[ columnName ] ? "black" : "black",
                          fontWeight: !colVis[ columnName ] ? "normal" : "normal",
                        }}
                      >
                        {columnDisplayNames[ columnName ] || columnName}
                      </span>
                    </label>
                  </div>
                ))}
            </div>
          </Draggable>
        )}

        <button className="helpBtn" style={{ margin: "0 10px" }}>
          Help
        </button>
        <ul
          style={{ display: "flex", listStyle: "none", padding: 0, width: "100%" }}
        >
          <li className="topNavBtn">
            <button
              className={
                "topbtn" + (pathname === "/Positions" ? " selected" : "")
              }
              style={{
                padding: "11px 15px",
                border: "none",
                backgroundColor:
                  pathname === "/Positions" ? "#4661bd" : "transparent",
                transition: "background-color 0.3s",
              }}
              onClick={() => {
                navigate("/Positions");
              }}
            >
              Positions
            </button>
            <span className="tooltip">Positions</span>
          </li>
          <li className="topNavBtn">
            <button
              className={
                "topbtn" + (pathname === "/Holdings" ? " selected" : "")
              }
              style={{
                padding: "11px 15px",
                border: "none",
                backgroundColor:
                  pathname === "/Holdings" ? "#4661bd" : "transparent",
                transition: "background-color 0.3s",
              }}
              onClick={() => {
                navigate("/Holdings");
              }}
            >
              Holdings
            </button>
            <span className="tooltip">Holdings</span>
          </li>
          <li className="topNavBtn">
            <button
              className={
                "topbtn" + (pathname === "/OrderFlow" ? " selected" : "")
              }
              style={{
                padding: "11px 10px",
                border: "none",
                backgroundColor:
                  pathname === "/OrderFlow" ? "#4661bd" : "transparent",
                transition: "background-color 0.3s",
              }}
              onClick={() => {
                navigate("/OrderFlow");
              }}
            >
              Order Flow
            </button>
            <span className="tooltip">Order Flow</span>
          </li>
          <li className="topNavBtn">
            <button
              className={
                "topbtn" + (pathname === "/OrderManagement" ? " selected" : "")
              }
              style={{
                padding: "11px 10px",
                border: "none",
                backgroundColor:
                  pathname === "/OrderManagement" ? "#4661bd" : "transparent",
                transition: "background-color 0.3s",
              }}
              onClick={() => {
                navigate("/OrderManagement");
              }}
            >
              Order Management
            </button>
            <span className="tooltip">Order Management</span>
          </li>
        </ul>
      </div>
    </div>
  );
};