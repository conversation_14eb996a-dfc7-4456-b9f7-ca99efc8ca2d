import React, { useState, useRef, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import {
  OptimizedMarketIndex,
  OptimizedLeftNav,
  OptimizedRightNav,
  OptimizedTopNav,
} from "./Layout/OptimizedComponenets";
import "./Option_Chain.css";
import refreshImg from "../assets/refresh.png";
import { RotatingLines } from "react-loader-spinner";
import { fetchWithAuth } from "../utils/api";
import Modal from "react-modal";

const modalStyles = {
  overlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    zIndex: 1000,
  },
  content: {
    width: "350px",
    height: "180px",
    margin: "auto",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    background: "white",
    borderRadius: "10px",
    boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
    padding: "20px",
    top: "50%",
    left: "50%",
    right: "auto",
    bottom: "auto",
    transform: "translate(-50%, -50%)",
    border: "none",
  },
};

function Option_Chain() {
  const [ optionChainParams, setoptionChainParams ] = useState({
    symbol: "",
    underlying: "FUT",
    strike_step: "",
    depth: 10,
    sec: 30,
    expiry_date: "",
    expiry_type: "Weekly",
  });

  const [ optionChain, setoptionChain ] = useState([]);

  const [ formattedDate, setFormattedDate ] = useState("");
  const marketData = useSelector((state) => state.marketReducer);

  const [ loading, setLoading ] = useState(false);
  const [ showLoginPopup, setShowLoginPopup ] = useState(false);
  const [ isBrokerLogin, setIsBrokerLogin ] = useState(false);

  const { brokers } = useSelector((state) => state.brokerReducer);
  const memoizedBrokerLoginStatus = useMemo(() => {
    const brokerLoginStatus = brokers
      ? brokers.filter((row) => row.inputDisabled).length > 0
      : false;
    return brokerLoginStatus;
  }, [ brokers ]);

  useEffect(() => {
    setIsBrokerLogin(memoizedBrokerLoginStatus);
  }, [ memoizedBrokerLoginStatus ]);

  const getoptionChain = async () => {
    setLoading(true);
    try {
      const responseExpiries = await fetchWithAuth(`/api/get_option_chain/`, {
        method: "POST",
        body: JSON.stringify(optionChainParams),
      });

      if (!responseExpiries.ok) {
        const errorData = await responseExpiries.json();
        throw {
          message:
            errorData.message || "Something bad happened. Please try again",
        };
      }

      const currentDate = new Date();
      const formattedDate = currentDate.toLocaleString("en-US", {
        day: "2-digit",
        month: "short",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
        timeZone: "Asia/Kolkata",
      });
      setFormattedDate(formattedDate);
      setLoading(false);

      const optionchain = await responseExpiries.json();

      const nifty50Value = parseFloat(marketData?.marketData?.nifty50?.c);
      const roundedValue = Math.round(nifty50Value / 50) * 50;

      const niftybankValue = parseFloat(marketData?.marketData?.niftybank?.c);
      const finniftyValue = parseFloat(marketData?.marketData?.finnifty?.c);

      const roundedNiftybankValue = Math.round(niftybankValue / 100) * 100;
      const roundedFinniftyValue = Math.round(finniftyValue / 50) * 50;
      let index;
      if (optionChainParams.symbol === "NIFTY") {
        index = optionchain[ "Option Chain Data" ].findIndex(
          (option) => option.strikePrice === roundedValue
        );
      } else if (optionChainParams.symbol === "FINNIFTY") {
        index = optionchain[ "Option Chain Data" ].findIndex(
          (option) => option.strikePrice === roundedFinniftyValue
        );
      } else {
        index = optionchain[ "Option Chain Data" ].findIndex(
          (option) => option.strikePrice === roundedNiftybankValue
        );
      }
      const ul =
        Number(index) - optionChainParams.depth < 0
          ? 0
          : Number(index) - optionChainParams.depth;
      const ll =
        Number(index) + optionChainParams.depth >
          optionchain[ "Option Chain Data" ].length
          ? optionchain[ "Option Chain Data" ].length
          : Number(index) + optionChainParams.depth;
      setoptionChain(optionchain[ "Option Chain Data" ].slice(ul, ll + 1));
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const { NIFTY, FINNIFTY, BANKNIFTY } = useSelector(
    (state) => state.expiryReducer
  );

  useEffect(() => {
    const formateDate = (inputDateStr) => {
      const formattedDate = `${inputDateStr.substr(0, 2)}-${inputDateStr.substr(2, 3).charAt(0).toUpperCase() +
        inputDateStr.substr(2, 3).slice(1).toLowerCase()
        }-${inputDateStr.substr(5, 4)}`;
      return formattedDate;
    };

    const expiries =
      optionChainParams.symbol === "NIFTY"
        ? NIFTY
        : optionChainParams.symbol === "FINNIFTY"
          ? FINNIFTY
          : optionChainParams.symbol === "BANKNIFTY"
            ? BANKNIFTY
            : null;
    if (
      optionChainParams.expiry_type !== "" &&
      optionChainParams.symbol !== "" &&
      expiries.length !== 0
    ) {
      const month = expiries[ 0 ].substr(2, 3);
      const monthExpiries = expiries.filter((expiry) => expiry.includes(month));
      if (optionChainParams.expiry_type === "Weekly") {
        setoptionChainParams((prev) => ({
          ...prev,
          expiry_date: formateDate(monthExpiries[ 0 ]),
        }));
      } else {
        setoptionChainParams((prev) => ({
          ...prev,
          expiry_date: formateDate(monthExpiries[ monthExpiries.length - 1 ]),
        }));
      }
    }
  }, [
    NIFTY,
    FINNIFTY,
    BANKNIFTY,
    optionChainParams.expiry_type,
    optionChainParams.symbol,
  ]);

  const tableRef = useRef(null);

  const [ optionChainvis, setOptionChainvis ] = useState({
    OI: true,
    "CHNG IN OI": true,
    VOLUME: true,
    IV: true,
    LTP: true,
    Strike: true,
  });
  const [ optionChainvis1, setOptionChainvis1 ] = useState({
    LTP: true,
    IV: true,
    VOLUME: true,
    "CHNG IN OI": true,
    OI: true,
  });

  const optionchainCols = [
    "CALLS",
    "OI",
    "CHNG IN OI",
    "VOLUME",
    "IV",
    "LTP",
    "Strike",
  ];
  const optionchainCols1 = [ "CALLS", "OI", "CHNG IN OI", "VOLUME", "IV", "LTP" ];
  const [ optionchainSeq, setOptionchainSeq ] = useState(optionchainCols);
  const [ optionchainSeq1, setOptionchainSeq1 ] = useState(optionchainCols1);

  const optionchainTH = {
    OI: optionChainvis[ "OI" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>OI</small>
        </div>
      </th>
    ),
    "CHNG IN OI": optionChainvis[ "CHNG IN OI" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>CHNG IN OI</small>
        </div>
      </th>
    ),
    VOLUME: optionChainvis[ "VOLUME" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>VOLUME</small>
        </div>
      </th>
    ),
    IV: optionChainvis[ "IV" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>IV</small>
        </div>
      </th>
    ),
    LTP: optionChainvis[ "LTP" ] && (
      <th
        style={{ width: "100px", marginLeft: "100px" }}
        colSpan={1}
        rowSpan={1}
      >
        <div>
          <small>LTP</small>
        </div>
      </th>
    ),
  };
  const optionchainTH1 = {
    LTP: optionChainvis1[ "LTP" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>LTP</small>
        </div>
      </th>
    ),
    IV: optionChainvis1[ "IV" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>IV</small>
        </div>
      </th>
    ),
    VOLUME: optionChainvis1[ "VOLUME" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>VOLUME</small>
        </div>
      </th>
    ),
    "CHNG IN OI": optionChainvis1[ "CHNG IN OI" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>CHNG IN OI</small>
        </div>
      </th>
    ),
    OI: optionChainvis1[ "OI" ] && (
      <th style={{ width: "100px" }} colSpan={1} rowSpan={1}>
        <div>
          <small>OI</small>
        </div>
      </th>
    ),
  };

  const totalRows = optionChain.length;
  const halfRows = Math.ceil(totalRows / 2);

  return (
    <div>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav />
          <div style={{ display: "flex", border: "black", marginTop: "-15px" }}>
            <div>
              <div
                className="nifty2"
                style={{ marginBottom: "9px", marginLeft: "15px" }}
              >
                Stock Symbol
              </div>
              <select
                className="exchange-dropdown1"
                style={{ cursor: "pointer" }}
                onChange={(e) => {
                  setoptionChainParams((prev) => ({
                    ...prev,
                    symbol: e.target.value,
                    strike_step:
                      e.target.value === "NIFTY" ||
                        e.target.value === "FINNIFTY"
                        ? 50
                        : 100,
                  }));
                }}
              >
                {optionChainParams.symbol === "" && (
                  <option value="" color="black"></option>
                )}
                <option
                  value="NIFTY"
                  color="black"
                  selected={optionChainParams.symbol === "NIFTY"}
                >
                  NIFTY
                </option>
                <option
                  value="BANKNIFTY"
                  color="black"
                  selected={optionChainParams.symbol === "BANKNIFTY"}
                >
                  BANKNIFTY
                </option>
                <option
                  value="FINNIFTY"
                  color="black"
                  selected={optionChainParams.symbol === "FINNIFTY"}
                >
                  FINNIFTY
                </option>
              </select>
            </div>
            <div>
              <div
                className="nifty2"
                style={{ marginBottom: "9px", marginLeft: "10px" }}
              >
                Underlying
              </div>
              <select
                className="under"
                style={{ cursor: "pointer", marginTop: "-10px" }}
                onChange={(e) => {
                  setoptionChainParams((prev) => ({
                    ...prev,
                    underlying: e.target.value,
                  }));
                }}
              >
                <option
                  value="FUT"
                  selected={optionChainParams.underlying === "FUT"}
                >
                  FUT
                </option>
                <option
                  value="SPOT"
                  selected={optionChainParams.underlying === "SPOT"}
                >
                  SPOT
                </option>
              </select>
            </div>
            <div>
              <div className="nifty2" style={{ marginLeft: "17px" }}>
                Strike Step
              </div>
              <input
                type="number strikeStep"
                value={optionChainParams.strike_step}
                style={{
                  marginTop: "2px",
                  marginLeft: "15px",
                  height: "35px",
                  borderRadius: "5px",
                  border: "1px solid black",
                  width: "80px",
                  paddingLeft: "15px",
                }}
                readOnly
              // onChange={(e) => {
              //   const value = parseInt(e.target.value, 10);
              //   setoptionChainParams ((prev) => ({
              //     ...prev,
              //     strike_step: isNaN(value) ? 0 : value,
              //   }));
              // }}
              />
            </div>
            <div>
              <div className="nifty2" style={{ marginLeft: "20px" }}>
                Depth
              </div>
              <input
                type="number"
                step={5}
                value={optionChainParams.depth}
                onChange={(e) => {
                  const value = parseInt(e.target.value, 10);
                  const validValue = Math.min(Math.max(value, 10), 60);
                  setoptionChainParams((prev) => ({
                    ...prev,
                    depth: validValue,
                  }));
                }}
                style={{
                  marginLeft: "15px",
                  height: "37px",
                  borderRadius: "5px",
                  border: "1px solid black",
                  width: "80px",
                  paddingLeft: "15px",
                  cursor: "pointer",
                }}
              />
            </div>

            <div style={{ marginLeft: "5px" }}>
              <div
                className="nifty2"
                style={{ marginLeft: "10px" }}
              >
                Expiry
              </div>

              <input
                value={
                  optionChainParams.symbol === "NIFTY" ? "Weekly" : "Monthly"
                }
                onChange={(e) => {
                  setoptionChainParams((prev) => ({
                    ...prev,
                    expiry_type: e.target.value,
                  }));
                }}
                style={{
                  marginLeft: "10px",
                  height: "37px",
                  borderRadius: "5px",
                  border: "1px solid black",
                  width: "80px",
                  paddingLeft: "15px",
                  cursor: "pointer",
                }}
                readOnly
              />
            </div>
            <div style={{ display: "flex", border: "black", marginTop: "-15px", alignItems: "center" }}>

              <div style={{ width: "200px", marginLeft: "20px" }}>
                {optionChainParams.symbol === "" && (
                  <div style={{ color: "red", fontSize: "16px", marginTop: "20px" }}>
                    Please select a symbol
                  </div>
                )}
                {formattedDate !== "" && (
                  <div className="timeDiv">As on {formattedDate} IST</div>
                )}
              </div>
              <div className="refreshBtn" style={{ marginTop: "21px", marginLeft: "250px" }}>
                <button
                  onClick={() => {
                    if (optionChainParams.expiry_date !== "" && !loading) {
                      if (!isBrokerLogin) {
                        setShowLoginPopup(true);
                      } else {
                        getoptionChain();
                      }
                    }
                  }}
                  style={{
                    width: "140px",
                    backgroundColor: "#d8e1ff",
                    color: "black",
                    border: "none",
                    height: "38px",
                    borderRadius: "5px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "0 10px",
                    opacity: loading ? 0.6 : 1,
                    cursor: loading || optionChainParams.symbol === "" ? "not-allowed" : "pointer",
                  }}
                  disabled={loading || optionChainParams.symbol === ""}
                >
                  <span
                    style={{
                      marginLeft: "5px",
                      verticalAlign: "middle",
                      color: "black",
                      fontFamily: "Roboto-Bold, sans-serif",
                      fontWeight: 600,
                      fontSize: "14px",
                      cursor: loading || optionChainParams.symbol === "" ? "not-allowed" : "pointer",
                    }}
                  >
                    Refresh
                  </span>
                  {loading ? (
                    <div
                      style={{
                        pointerEvents: "none",
                        cursor: loading || optionChainParams.symbol === "" ? "not-allowed" : "pointer",
                      }}
                    >
                      <RotatingLines
                        visible={true}
                        height="30"
                        width="30"
                        color="blue"
                        strokeColor="#4661BD"
                        strokeWidth="5"
                        animationDuration="0.75"
                        ariaLabel="rotating-lines-loading"
                      />
                    </div>
                  ) : (
                    <img
                      src={refreshImg}
                      alt="Refresh Icon"
                      style={{
                        verticalAlign: "middle",
                        marginRight: "5px",
                        width: "30px",
                        cursor: loading || optionChainParams.symbol === "" ? "not-allowed" : "pointer",
                      }}
                    />
                  )}
                </button>
              </div>
            </div>

          </div>

          <div
            className="main-table optionChainTableDiv"
            style={{ overflowY: "auto", marginTop: "-22px", maxWidth: "100%" }}
            ref={tableRef}
          >
            <table
              className="orderflowtable-alt"
              style={{ tableLayout: "fixed", width: "100%" }}
            >
              <thead>
                <tr
                  style={{
                    height: " 40px",
                    position: "sticky",
                  }}
                >
                  <th className="calls" colSpan={5}>
                    <div>
                      <small>CALLS</small>
                    </div>
                  </th>
                  <th
                    className="strike"
                    colSpan={1}
                    rowSpan={2}
                    style={{ width: "10%" }}
                  >
                    <div>
                      <small>STRIKE</small>
                    </div>
                  </th>
                  <th className="puts" colSpan={5}>
                    <div>
                      <small>PUTS</small>
                    </div>
                  </th>
                </tr>
                <tr
                  style={{
                    height: " 40px",
                  }}
                >
                  {optionchainSeq.map(
                    (colName, index) =>
                      optionchainTH[ colName ] && (
                        <th key={index} colSpan={1} rowSpan={1}>
                          <div>
                            <small>{colName}</small>
                          </div>
                        </th>
                      )
                  )}
                  {optionchainSeq1.map(
                    (colName, index) =>
                      optionchainTH1[ colName ] && (
                        <th key={index} colSpan={1} rowSpan={1}>
                          <div>
                            <small>{colName}</small>
                          </div>
                        </th>
                      )
                  )}
                </tr>
              </thead>

              <tbody className="tabletbody" style={{ textAlign: "center" }}>
                {optionChain.map((option, index) => {
                  const optionchainTD = {
                    OI: optionChainvis[ "OI" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index < halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.openInterest_CE !== 0
                          ? Number(option.openInterest_CE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                    "CHNG IN OI": optionChainvis[ "CHNG IN OI" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index < halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.changeinOpenInterest_CE !== 0
                          ? Number(option.changeinOpenInterest_CE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                    VOLUME: optionChainvis[ "VOLUME" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index < halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.totalTradedVolume_CE !== 0
                          ? Number(option.totalTradedVolume_CE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                    IV: optionChainvis[ "IV" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index < halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.impliedVolatility_CE !== 0
                          ? Number(option.impliedVolatility_CE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                    LTP: optionChainvis[ "LTP" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index < halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.lastPrice_CE !== 0
                          ? Number(option.lastPrice_CE).toFixed(2)
                          : "="}
                      </td>
                    ),
                  };
                  const optionchainTD1 = {
                    LTP: optionChainvis1[ "LTP" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index >= halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.lastPrice_PE !== 0
                          ? Number(option.lastPrice_PE).toFixed(2)
                          : "="}
                      </td>
                    ),
                    IV: optionChainvis1[ "IV" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index >= halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.impliedVolatility_PE !== 0
                          ? Number(option.impliedVolatility_PE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                    VOLUME: optionChainvis1[ "VOLUME" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index >= halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.totalTradedVolume_PE !== 0
                          ? Number(option.totalTradedVolume_PE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                    "CHNG IN OI": optionChainvis1[ "CHNG IN OI" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index >= halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.changeinOpenInterest_PE !== 0
                          ? Number(option.changeinOpenInterest_PE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                    OI: optionChainvis1[ "OI" ] && (
                      <td
                        style={{
                          textAlign: "center",
                          background: index >= halfRows ? "#EEE8AA" : "inherit",
                        }}
                      >
                        {option.openInterest_PE !== 0
                          ? Number(option.openInterest_PE).toFixed(2)
                          : "-"}
                      </td>
                    ),
                  };

                  return (
                    <tr key={index}>
                      {optionchainSeq.map((colName, index) => {
                        return (
                          <React.Fragment key={index}>
                            {optionchainTD[ colName ]}
                          </React.Fragment>
                        );
                      })}
                      <td style={{ textAlign: "center" }}>
                        {option.strikePrice}
                      </td>
                      {optionchainSeq1.map((colName, index) => {
                        return (
                          <React.Fragment key={index}>
                            {optionchainTD1[ colName ]}
                          </React.Fragment>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        <OptimizedRightNav />
      </div>

      <Modal
        isOpen={showLoginPopup}
        onRequestClose={() => setShowLoginPopup(false)}
        contentLabel="Broker Login Modal"
        style={modalStyles}
        ariaHideApp={false}
      >
        <div style={{ textAlign: "center" }}>
          <h3
            style={{
              color: "#32406d",
              marginBottom: "20px",
              fontFamily: "Roboto-Bold, sans-serif",
              fontSize: "18px",
            }}
          >
            Broker Login Required
          </h3>
          <p
            style={{
              marginBottom: "25px",
              fontSize: "16px",
              color: "#333",
            }}
          >
            Please confirm broker login to continue
          </p>
          <button
            onClick={() => {
              setShowLoginPopup(false);
            }}
            style={{
              padding: "10px 30px",
              backgroundColor: "#4661bd",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
              fontWeight: "bold",
              fontSize: "16px",
              transition: "background-color 0.3s",
            }}
            onMouseOver={(e) => (e.target.style.backgroundColor = "#32406d")}
            onMouseOut={(e) => (e.target.style.backgroundColor = "#4661bd")}
          >
            OK
          </button>
        </div>
      </Modal>
    </div>
  );
}

export default Option_Chain;
