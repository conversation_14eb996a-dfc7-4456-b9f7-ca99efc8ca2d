import React from "react";

const OptionsPortfolioSection = ({
    defaultStrategy,
    setDefaultStrategy,
    product,
    setProduct,
    combinedTarget,
    setCombinedTarget,
    combinedSL,
    setCombinedSL,
    legTarget,
    setLegTarget,
    legSL,
    setLegSL,
    strikeAdjustment,
    setStrikeAdjustment
}) => (
    <div className="options-portfolio-section" style={{
        padding: "1px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box"
    }}>

        <div style={{ marginBottom: "5px" }}>
            <label style={{ display: "block", fontSize: "15px", marginBottom: "2px", fontWeight: "600" }}>
                Options QT Default (the selected options will populate under QTP options Strategy)
            </label>
            <label style={{
                display: "block",
                fontSize: "17px",
                marginBottom: "8px",
                fontWeight: "600",
                color: "#4661bd"
            }}>
                Default Strategies:
            </label>
            <select
                value={defaultStrategy}
                onChange={(e) => setDefaultStrategy(e.target.value)}
                style={{
                    width: "300px",
                    padding: "8px",
                    borderRadius: "6px",
                    border: "1px solid #ddd",
                    fontSize: "14px",
                    background: "#fff"
                }}
            >
                <option value="NIFTY PUT NIFTY CALL">NIFTY PUT NIFTY CALL</option>
                <option value="NIFTY PUT L NIFTY CALL L">NIFTY PUT L NIFTY CALL L</option>
                <option value="BANKNIFT">BANKNIFT</option>
            </select>
            <p style={{ fontSize: "13px", color: "#666", marginTop: "8px" }}>
                Below settings will be applicable as default if your Multi Leg portfolio orders are placed from Options QTP or from AmiBroker, MT4, Ninja, Excel, TradingView, etc.            </p>
        </div>

        <div style={{ marginBottom: "5px" }}>
            <div style={{
                display: "grid",
                gridTemplateColumns: "repeat(6, 1fr)",
                gap: "5px",
                background: "#f8fafc",
                padding: "15px",
                borderRadius: "8px"
            }}>
                <div>
                    <label style={{ display: "block", fontSize: "14px", marginBottom: "8px", fontWeight: "600" }}>Product</label>
                    <select
                        value={product}
                        onChange={(e) => setProduct(e.target.value)}
                        style={{
                            width: "80%",
                            padding: "8px",
                            borderRadius: "6px",
                            border: "1px solid #ddd",
                            fontSize: "14px",
                            background: "#fff"
                        }}
                    >
                        <option value="MIS">MIS</option>
                        <option value="NRML">NRML</option>
                        <option value="CNC">CNC</option>
                    </select>
                </div>
                <div>
                    <label style={{ display: "block", fontSize: "14px", marginBottom: "8px", fontWeight: "600" }}>Strategy</label>
                    <select
                        value={product}
                        onChange={(e) => setProduct(e.target.value)}
                        style={{
                            width: "80%",
                            padding: "8px",
                            borderRadius: "6px",
                            border: "1px solid #ddd",
                            fontSize: "14px",
                            background: "#fff"
                        }}
                    >
                        <option value="Default">Default</option>
                        <option value="Strategy">Strategy</option>
                    </select>
                </div>
                {[
                    { label: "Combined Target", value: combinedTarget, setter: setCombinedTarget },
                    { label: "Combined SL", value: combinedSL, setter: setCombinedSL },
                    { label: "Leg Target", value: legTarget, setter: setLegTarget },
                    { label: "Leg SL", value: legSL, setter: setLegSL }
                ].map(({ label, value, setter }) => (
                    <div key={label}>
                        <label style={{ display: "block", fontSize: "14px", marginBottom: "8px", fontWeight: "600" }}>{label}</label>
                        <input
                            type="number"
                            value={value}
                            onChange={(e) => setter(e.target.value)}
                            style={{
                                width: "90%",
                                padding: "8px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                background: "#fff"
                            }}
                            min="0"
                            placeholder={`Enter ${label.toLowerCase()}`}
                        />
                    </div>
                ))}
            </div>
        </div>

        <div style={{ marginBottom: "5px" }}>
            <div style={{
                background: "#f8fafc",
                padding: "5px",
                borderRadius: "8px",
                border: "1px solid #e0e0e0"
            }}>
                <label style={{
                    display: "block",
                    fontSize: "17px",
                    marginBottom: "8px",
                    fontWeight: "600",
                    paddingBottom: "5px",
                    color: "#4661bd"
                }}>
                    Lock Profit
                </label>
                <div style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr",
                    gap: "10px",
                    alignItems: "center"
                }}>
                    <div style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "10px"
                    }}>
                        <label style={{
                            fontSize: "14px",
                            color: "#333",
                            fontWeight: "500",
                            whiteSpace: "nowrap"
                        }}>
                            On Profit Reaches
                        </label>
                        <input
                            type="number"
                            placeholder="0"
                            style={{
                                width: "100px",
                                padding: "8px",
                                border: "1px solid #d1d5db",
                                borderRadius: "4px",
                                fontSize: "14px",
                                outline: "none",
                                boxSizing: "border-box",
                                background: "#fff",
                                color: "#333",
                                textAlign: "right"
                            }}
                        />
                    </div>
                    <div style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "10px"
                    }}>
                        <label style={{
                            fontSize: "14px",
                            color: "#333",
                            fontWeight: "500",
                            whiteSpace: "nowrap"
                        }}>
                            Lock MIN Profit
                        </label>
                        <input
                            type="number"
                            placeholder="0"
                            style={{
                                width: "100px",
                                padding: "8px",
                                border: "1px solid #d1d5db",
                                borderRadius: "4px",
                                fontSize: "14px",
                                outline: "none",
                                boxSizing: "border-box",
                                background: "#fff",
                                color: "#333",
                                textAlign: "right"
                            }}
                        />
                    </div>
                </div>
            </div>
        </div>
        <div style={{ marginBottom: "5px" }}>
            <label style={{
                display: "block",
                fontSize: "13px",
                marginBottom: "4px",
                fontWeight: "600",
                paddingBottom: "5px",
            }}>
                This is a very important setting if you want to adjust strikes for IT or OTM. Example in Strangle it selects 1 OTM strikes, but if you wanted to select 2 OTM in place of 1 OTM then you can select adjustment here as 1.
            </label>
            <div style={{ maxWidth: "400px", marginLeft: "10px" }}>
                <label style={{ display: "block", fontSize: "18px", marginBottom: "4px", fontWeight: "600" }}>
                    Strike Adjustments for IT and OTM
                </label>
                <input
                    type="number"
                    value={strikeAdjustment}
                    onChange={(e) => setStrikeAdjustment(e.target.value)}
                    style={{
                        width: "40%",
                        padding: "8px",
                        borderRadius: "6px",
                        border: "1px solid #ddd",
                        fontSize: "14px",
                        background: "#fff"
                    }}
                    min="0"
                    placeholder="Enter adjustment"
                />
            </div>
        </div>

        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "15px",
            marginTop:"-20px"
        }}>
            <button style={{
                padding: "12px 25px",
                background: "#4661bd",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "background 0.3s ease"
            }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Save Settings
            </button>
            <button style={{
                padding: "12px 25px",
                background: "#fff",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "all 0.3s ease"
            }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default OptionsPortfolioSection;