.renew-subscription {
  background-color: #d8e1ff;
  height: 30px;
  text-align: center;
  color: black;
  padding-top: 8px;
  font-weight: bold;
  font-size: 15px;
  position: relative;
}

.close-button {
  position: absolute;
  right: 15px;
  top: 1px;
  width: 23px;
  height: 30px;
  background-color: white;
  border: none;
  border-radius: 50%;
  color: #2e3a5b;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  line-height: 30px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.subscription-header-container {
  padding: 7px 0 0 30px;
}

.subscription-status {
  font-weight: 600;
  font-size: 20px;
  display: inline-block;
  padding-left: 30px;
}

/* Dynamic subscription status colors */
.subscription-status.active {
  color: green;
}
.subscription-status.expired {
  color: red;
}
.subscription-status.free-trial {
  color: orange;
}
.subscription-status.default {
  color: brown;
}

/* Subheader */
.subscription-subheader-container {
  padding-left: 30px;
}
.subscription-subheader-text {
  font-size: 14px;
  font-weight: 500; /* Adjusted from ".5" */
}

/* Info Section */
.subscription-info-container {
  display: flex;
  align-items: flex-start;
  margin-top: 3px;
}
.subscription-info-left {
  flex: 2;
  padding-left: 30px;
}
.register-info {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.register-label {
  font-weight: 500;
  font-size: 16px;
  margin-right: 10px;
  width: 200px;
  padding-left: 35px;
}
.register-input {
  width: 150px;
  height: 20px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-left: -60px;
  padding: 10px;
}
.subscription-validity {
  display: flex;
  align-items: center;
  margin-top: -15px;
}
.validity-label {
  font-weight: 500;
  font-size: 16px;
  width: 200px;
}
.validity-value {
  font-weight: bold;
  font-size: 14px;
  margin-left: -40px;
}
.qr-code-container {
  flex: 1;
  margin-right: 20px;
}
.qr-code-title {
  font-weight: bold;
  font-size: 16px;
  padding-left: 180px;
}

/* Trial Info */
.trial-info-container h4 {
  margin: 0;
}
.trial-info-blue {
  color: blue;
  padding-left: 160px;
}
.trial-info-green {
  padding-left: 30px;
  color: green;
  max-width: 1000px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Renewal Options */
.renewal-options-container {
  padding-left: 30px;
  margin-top: -15px;
}
.renewal-options-radio {
  display: flex;
  align-items: center;
  margin-top: 5px;
  padding-left: 130px;
}
.renew-radio {
  margin-right: 10px;
  transform: scale(1.5);
}
.renew-label {
  padding-top: 9px;
  margin-right: 30px;
}
.add-user-label {
  padding-top: 9px;
}
.add-user-label.disabled {
  color: grey;
  cursor: not-allowed;
}
.user-count-label {
  padding-top: 9px;
  padding-left: 30px;
}
.user-count-input {
  width: 70px;
  height: 30px;
  border-radius: 5px;
  margin-left: 10px;
  padding-left: 10px;
  border: 1px solid #ccc;
}
.renewal-qr-container {
  width: 40%;
  padding: 10px;
  border-radius: 5px;
  box-sizing: border-box;
  padding-top: 0;
  margin-left: 1100px;
  margin-top: -10px;
}

.qr-image {
  width: 200px;
  height: 200px;
  border-radius: 5px;
  border: 5px solid #ccc;
  padding: 10px;
  position: relative;
  top: -120px;
}

/* Payment Selections */
.payment-selection-container {
  display: flex;
  align-items: center;
  margin-top: -210px;
  margin-left: 10px;

}
.renewal-period-label {
  padding-top: 9px;
  margin-right: 20px;
}
.renewal-period-select {
  transform: scale(1.1);
  height: 30px;
  border-radius: 5px;
  margin-right: 15px;
  width: 300px;
  border: 1px solid #ccc;
}
.payment-mode-label {
  padding-top: 9px;
  margin-right: 20px;
  margin-left: 40px;
}
.payment-mode-select {
  transform: scale(1.1);
  height: 30px;
  border-radius: 5px;
  margin-right: 10px;
  width: 250px;
  border: 1px solid #ccc;
}

/* Amount Details */
.amount-details-container {
  display: flex;
  align-items: center;
  margin-top: 7px;
}
.amount-label,
.gst-label,
.payable-label {
  padding-top: 9px;
  margin-right: 5px;
}
.amount-label {
  margin-left: 10px;
}
.gst-label {
  margin-left: 15px;
}
.payable-label {
  padding-left: 25px;
}
.amount-input {
  width: 100px;
  height: 30px;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-weight: bold;
  padding: 10px;
  margin-right: 10px;
}

/* Billing & Payment Instructions */
.billing-and-payment-container {
  display: flex;
  margin-top: 5px;
  margin-left: 10px;
}
.billing-address {
  width: 64%;
  padding: 5px;
  border-radius: 5px;
  border: 1px solid #ccc;

  height: 200px;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}
.billing-address-title {
  font-weight: bold;
  font-size: 16px;
  color: #32406d;
  margin-bottom: 8px;
}
.billing-row {
  display: flex;
}
.billing-column {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.billing-label {
  margin: 0 10px;
}
.billing-input {
  border: 1px solid #ccc;
  padding: 5px;
  width: 100%;
  height: 30px;
  border-radius: 5px;
}
.billing-input.gst-input {
  margin-left: -18px;
}
.billing-textarea {
  border: 1px solid #ccc;
  padding: 5px;
  width: 100%;
  height: 60px;
  border-radius: 5px;
  resize: none;
}
.billing-subrow {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* Payment Instructions */
.payment-instructions {
  width: 34%;
  padding-left: 20px;
  border-radius: 5px;
  margin-right: 10px;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}
.payment-instructions-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 15px;
  color: #32406d;
}
.payment-instructions-section {
  margin-bottom: 15px;
}
.upi-id-title,
.mobile-title {
  font-weight: bold;
  margin-bottom: 5px;
}
.upi-id-value,
.mobile-value {
  font-size: 14px;
  color: #333;
}
.instructions-title {
  font-weight: bold;
}
.instructions-content p {
  margin: 0;
}
.instructions-highlight {
  color: green;
  font-weight: bold;
}

/* Payment Details */
.payment-details {
  width: 64%;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-top: -200px;
  margin-left: 10px;
  box-sizing: border-box;
}

.payment-details legend {
  padding: 0 10px;
  font-weight: bold;
  font-size: 16px;
  color: #32406d;
}

.payment-details-title {
  font-size: 16px;
  margin-bottom: 10px;
  color: #32406d;
  font-weight: bold;
}

.payment-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.payment-label {
  margin-right: 20px;
  width: 210px;
}
.payment-input {
  border: 1px solid #ccc;
  padding: 5px;
  width: 250px;
  height: 30px;
  border-radius: 5px;
  margin-right: 20px;
  margin-left: -45px;
}
.payment-button {
  color: white;
  background-color: #4caf50;
  height: 35px;
  cursor: pointer;
  border: none;
  width: 180px;
  margin-left: 10px;
  border-radius: 5px;
}
.payment-button.success {
  background-color: green;
}
.payment-button.error {
  background-color: red;
}
.payment-button.default {
  background-color: #32406d;
}
.payment-warning {
  margin-left: 200px;
  margin-top: -10px;
}
.payment-warning-text {
  color: red;
  margin: 0;
}
