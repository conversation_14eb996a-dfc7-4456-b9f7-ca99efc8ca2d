import React, { useState, useEffect, useRef } from "react";
import "./StrategySelector.css";

const StrategySelector = ({ predefinedStrategy, setPredefinedStrategy, disabled }) => {
    const [ isOpen, setIsOpen ] = useState(false);
    const [ openCategories, setOpenCategories ] = useState({
        popular: false,
        straddles: false,
        butterfly: false,
        ratio: false,
        spreads: false,
        iron: false,
        calendar: false,
        naked: false,
    });

    const dropdownRef = useRef(null);
    const isFirstRender = useRef(true);

    // Map strategies to their categories
    const strategyCategories = {
        // Popular category
        "Custom": "popular",
        "Short Straddle": "popular", // Also in straddles
        "Short Strangle": "popular", // Also in straddles
        "Iron Condor": "popular", // Also in iron
        "Long Butterfly": "popular", // Also in butterfly
        "Iron Butterfly": "popular", // Also in iron
        "Call Ratio Spread": "popular", // Also in ratio
        "Put Ratio Spread": "popular", // Also in ratio

        // Straddles category
        "Long Straddle": "straddles",
        "Long Strangle": "straddles",

        // Butterfly category
        "Short Butterfly": "butterfly",

        // Ratio category
        "Call Backspread": "ratio",
        "Put Backspread": "ratio",

        // Spreads category
        "Bull Call Spread": "spreads",
        "Bull Put Spread": "spreads",
        "Bear Call Spread": "spreads",
        "Bear Put Spread": "spreads",

        // Iron category
        "Reverse Iron Butterfly": "iron",
        "Reverse Iron Condor": "iron",

        // Calendar category
        "Call Calendar Spread": "calendar",
        "Put Calendar Spread": "calendar",
        "Short Call Calendar": "calendar",
        "Short Put Calendar": "calendar",

        // Naked category
        "Long Call": "naked",
        "Short Put": "naked",
        "Long Put": "naked",
        "Short Call": "naked"
    };

    // Function to get the appropriate category for a strategy
    const getCategoryForStrategy = (strategy) => {
        return strategyCategories[ strategy ] || "popular"; // Default to popular if not found
    };

    const toggleDropdown = () => {
        if (!isOpen) {
            // When opening the dropdown, open the category containing the selected strategy
            const categoryToOpen = getCategoryForStrategy(predefinedStrategy);
            setOpenCategories(prev => ({
                ...prev,
                [ categoryToOpen ]: true
            }));
        }
        setIsOpen(!isOpen);
    };

    const toggleCategory = (category) => {
        setOpenCategories((prev) => ({
            ...prev,
            [ category ]: !prev[ category ],
        }));
    };

    const handleSelect = (strategy) => {
        setPredefinedStrategy(strategy);
        setIsOpen(false);
        // No longer reset all categories when closing
    };

    useEffect(() => {
        if (isFirstRender.current && !predefinedStrategy) {
            setPredefinedStrategy("Custom");
            setOpenCategories(prev => ({ ...prev, popular: true }));
            isFirstRender.current = false;
        }
    }, [ setPredefinedStrategy, predefinedStrategy ]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
                // No longer reset categories when clicking outside
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    // Function to check if an option is the currently selected strategy
    const isSelected = (strategy) => {
        return strategy === predefinedStrategy;
    };

    return (
        <div className="strategy-selector-container" ref={dropdownRef}>
            <div className="strategy-selector">
                <button className="dropdown-button" onClick={toggleDropdown} disabled={disabled}>
                    {predefinedStrategy || "Select Strategy"}
                    <span className="dropdown-arrow">{isOpen ? "▲" : "▼"}</span>
                </button>

                {isOpen && (
                    <div className="dropdown-menu">
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("popular")}
                            >
                                <span className="category-icon">
                                    {openCategories.popular ? "▼" : "▶"}
                                </span>
                                Popular
                            </div>
                            {openCategories.popular && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Custom") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Custom")}
                                    >
                                        Custom
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Straddle") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Straddle")}
                                    >
                                        Short Straddle
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Strangle") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Strangle")}
                                    >
                                        Short Strangle
                                    </div>
                                    <div
                                        className={`option ${isSelected("Iron Condor") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Iron Condor")}
                                    >
                                        Iron Condor
                                    </div>
                                    <div
                                        className={`option ${isSelected("Long Butterfly") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Long Butterfly")}
                                    >
                                        Long Butterfly
                                    </div>
                                    <div
                                        className={`option ${isSelected("Iron Butterfly") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Iron Butterfly")}
                                    >
                                        Iron Butterfly
                                    </div>
                                    <div
                                        className={`option ${isSelected("Call Ratio Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Call Ratio Spread")}
                                    >
                                        Call Ratio Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Put Ratio Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Put Ratio Spread")}
                                    >
                                        Put Ratio Spread
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Straddles Category */}
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("straddles")}
                            >
                                <span className="category-icon">
                                    {openCategories.straddles ? "▼" : "▶"}
                                </span>
                                Straddles
                            </div>
                            {openCategories.straddles && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Long Straddle") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Long Straddle")}
                                    >
                                        Long Straddle
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Straddle") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Straddle")}
                                    >
                                        Short Straddle
                                    </div>
                                    <div
                                        className={`option ${isSelected("Long Strangle") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Long Strangle")}
                                    >
                                        Long Strangle
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Strangle") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Strangle")}
                                    >
                                        Short Strangle
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Butterfly Category */}
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("butterfly")}
                            >
                                <span className="category-icon">
                                    {openCategories.butterfly ? "▼" : "▶"}
                                </span>
                                Butterfly
                            </div>
                            {openCategories.butterfly && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Long Butterfly") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Long Butterfly")}
                                    >
                                        Long Butterfly
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Butterfly") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Butterfly")}
                                    >
                                        Short Butterfly
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Ratio Category */}
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("ratio")}
                            >
                                <span className="category-icon">
                                    {openCategories.ratio ? "▼" : "▶"}
                                </span>
                                Ratio
                            </div>
                            {openCategories.ratio && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Call Ratio Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Call Ratio Spread")}
                                    >
                                        Call Ratio Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Put Ratio Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Put Ratio Spread")}
                                    >
                                        Put Ratio Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Call Backspread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Call Backspread")}
                                    >
                                        Call Backspread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Put Backspread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Put Backspread")}
                                    >
                                        Put Backspread
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Spreads Category */}
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("spreads")}
                            >
                                <span className="category-icon">
                                    {openCategories.spreads ? "▼" : "▶"}
                                </span>
                                Spreads
                            </div>
                            {openCategories.spreads && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Bull Call Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Bull Call Spread")}
                                    >
                                        Bull Call Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Bull Put Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Bull Put Spread")}
                                    >
                                        Bull Put Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Bear Call Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Bear Call Spread")}
                                    >
                                        Bear Call Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Bear Put Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Bear Put Spread")}
                                    >
                                        Bear Put Spread
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Iron Category */}
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("iron")}
                            >
                                <span className="category-icon">
                                    {openCategories.iron ? "▼" : "▶"}
                                </span>
                                Iron
                            </div>
                            {openCategories.iron && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Iron Butterfly") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Iron Butterfly")}
                                    >
                                        Iron Butterfly
                                    </div>
                                    <div
                                        className={`option ${isSelected("Iron Condor") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Iron Condor")}
                                    >
                                        Iron Condor
                                    </div>
                                    <div
                                        className={`option ${isSelected("Reverse Iron Butterfly") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Reverse Iron Butterfly")}
                                    >
                                        Reverse Iron Butterfly
                                    </div>
                                    <div
                                        className={`option ${isSelected("Reverse Iron Condor") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Reverse Iron Condor")}
                                    >
                                        Reverse Iron Condor
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Calendar Category */}
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("calendar")}
                            >
                                <span className="category-icon">
                                    {openCategories.calendar ? "▼" : "▶"}
                                </span>
                                Calendar
                            </div>
                            {openCategories.calendar && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Call Calendar Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Call Calendar Spread")}
                                    >
                                        Call Calendar Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Put Calendar Spread") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Put Calendar Spread")}
                                    >
                                        Put Calendar Spread
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Call Calendar") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Call Calendar")}
                                    >
                                        Short Call Calendar
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Put Calendar") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Put Calendar")}
                                    >
                                        Short Put Calendar
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Naked Category */}
                        <div className="category">
                            <div
                                className="category-header"
                                onClick={() => toggleCategory("naked")}
                            >
                                <span className="category-icon">
                                    {openCategories.naked ? "▼" : "▶"}
                                </span>
                                Naked
                            </div>
                            {openCategories.naked && (
                                <div className="sub-options">
                                    <div
                                        className={`option ${isSelected("Long Call") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Long Call")}
                                    >
                                        Long Call
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Put") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Put")}
                                    >
                                        Short Put
                                    </div>
                                    <div
                                        className={`option ${isSelected("Long Put") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Long Put")}
                                    >
                                        Long Put
                                    </div>
                                    <div
                                        className={`option ${isSelected("Short Call") ? "selected" : ""}`}
                                        onClick={() => handleSelect("Short Call")}
                                    >
                                        Short Call
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default StrategySelector;