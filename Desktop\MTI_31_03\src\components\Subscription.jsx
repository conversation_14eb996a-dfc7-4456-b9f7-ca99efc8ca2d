import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { RotatingLines } from "react-loader-spinner";
import "./Subscription.css";
import image from '../assets/Media.jpeg'; // Import the image
import Cookies from "universal-cookie";
import { fetchWithAuth } from "../utils/api";
const cookies = new Cookies();

function Subscription() {
    const mainUser = cookies.get("USERNAME");

    const [ numberOfUsers, setNumberOfUsers ] = useState(1);
    const [ renew, setRenew ] = useState("renew");
    const [ totalAmount, setTotalAmount ] = useState(750);
    const [ gstAmount, setGstAmount ] = useState(0);
    const [ amountWithGst, setAmountWithGst ] = useState(750);
    const [ paymentMode, setPaymentMode ] = useState('google pay');
    const [ renewPeriod, setRenewPeriod ] = useState("1 month");
    const [ payment, setPayment ] = useState("");
    const [ orderId, setOrderId ] = useState('');
    const [ expiry, setExpiry ] = useState(null);
    const [ subscriptionType, setSubscriptionType ] = useState(null);
    const [ users, setUsers ] = useState(null);
    const [ paymentStatus, setPaymentStatus ] = useState(null);
    const [ loading, setLoading ] = useState(false);

    const navigate = useNavigate();

    const handleRenewPeriodChange = (e) => {
        setRenewPeriod(e.target.value);
    };
    const handleRenew = (e) => {
        const selectedValue = e.target.value;
        setRenew(selectedValue);
    };
    const handleInputChange = (e) => {
        const value = e.target.value;
        if (/^\d{0,12}$/.test(value)) {
            setOrderId(value);
        }
    };

    const handleAmountChange = (e) => {
        const value = e.target.value;
        if (/^\d*\.?\d*$/.test(value)) {
            setPayment(value);
        }
    };

    const handlePaymentModeChange = (e) => {
        setPaymentMode(e.target.value);
    };

    const calculateTotalAmount = (numUsers) => {
        const isSixMonth = renewPeriod === "6 Month";
        if (renew === "renew") {
            let baseAmount = 750;
            if (isSixMonth) {
                baseAmount = 3500 / 6;
            }
            if (numUsers <= 1) {
                return isSixMonth ? 3500 : 750;
            } else {
                const amount = baseAmount + (baseAmount / 2) * (numUsers - 1);
                return amount * (isSixMonth ? 6 : 1);
            }
        } else {
            let amount = 375;
            return amount * numUsers;
        }
    };

    useEffect(() => {
        const amountBeforeGST = calculateTotalAmount(numberOfUsers);
        const gst = 0.18;
        const gstValue = amountBeforeGST * gst;
        const totalAmountWithGST = amountBeforeGST + gstValue;

        setTotalAmount(amountBeforeGST.toFixed(2));
        setGstAmount(gstValue.toFixed(2));
        setAmountWithGst(totalAmountWithGST.toFixed(2));
    }, [ numberOfUsers, renew, renewPeriod ]);

    const payments = async () => {
        setLoading(true);
        const requestBody = {
            username: mainUser,
            num_of_users: String(numberOfUsers),
            payment_order_id: orderId,
            payment_amount: payment,
            payment_mode: paymentMode,
            renewal_period: renewPeriod,
            payment_type: renew === "renew" ? "RENEW" : "ADD USER",
        };
        try {
            const response = await fetchWithAuth(`api/make_payment`, {
                method: "POST",
                body: JSON.stringify(requestBody),
            });

            if (response.ok) {
                const responseData = await response.json();
                const subscriptionType = responseData?.subscription_type || "";
                cookies.set('subscription_type', subscriptionType);
                const number_user = responseData?.num_of_users || "";
                cookies.set('number_users', number_user);

                const subscriptionEndDate = new Date(responseData?.subscription_end_date);
                const formattedExpiryDate = subscriptionEndDate.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
                cookies.set('expiry', formattedExpiryDate);

                setExpiry(formattedExpiryDate);
                setSubscriptionType(subscriptionType);
                setUsers(number_user);
                setPaymentStatus("success");

                if (subscriptionType === "Active" || subscriptionType === "Free Trial") {
                    navigate("/UserProfiles");
                } else {
                    navigate("/");
                }
            } else {
                setPaymentStatus("error");
                console.log(`Payment failed with status: ${response.status}`, "response");
            }
        } catch (err) {
            setPaymentStatus("error");
            console.error(err, "Error during payment");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const savedExpiry = new Date(cookies.get('expiry'));
        const formattedExpiryDate = savedExpiry?.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });

        const savedSubscriptionType = cookies.get('subscription_type');
        const no_users = cookies.get("number_users");
        setExpiry(formattedExpiryDate);
        setSubscriptionType(savedSubscriptionType);
        setUsers(no_users);
    }, [ expiry ]);

    if (loading) return <div>Loading...</div>;

    const getButtonContent = () => {
        if (loading) return <RotatingLines width="24" strokeColor="white" />;
        if (paymentStatus === "success") return <span>✔ Payment Successful</span>;
        if (paymentStatus === "error") return <span>✘ Payment Failed</span>;
        return "Confirm Payment";
    };

    const handleGoBackClick = () => {
        if (subscriptionType === "Active" || subscriptionType === "Free Trial") {
            navigate("/UserProfiles");
        } else {
            navigate("/");
        }
    };

    const getSubscriptionStatusClass = (subscriptionType) => {
        switch (subscriptionType) {
            case 'Active': return 'active';
            case 'Expired': return 'expired';
            case 'Free_Trial': return 'free-trial';
            default: return 'default';
        }
    };

    const isButtonDisabled = loading || !payment || !orderId || orderId.length !== 12;

    return (
        <div>
            <div className="renew-subscription">
                Renew Subscription
                <button
                    className="close-button"
                    onClick={handleGoBackClick}
                    aria-label="Close"
                >
                    ×
                </button>
            </div>
            <div className="subscription-container">
                <div className="subscription-header-container">
                    <h3 className={`subscription-status ${getSubscriptionStatusClass(subscriptionType)}`}>
                        {subscriptionType === 'Active' &&
                            `Your subscription is Active, max allowed Demat accounts ${users}`}
                        {subscriptionType === 'Free_Trial' &&
                            `You are on a Free Trial, Please upgrade, max allowed Demat accounts 1`}
                        {subscriptionType === 'Expired' &&
                            `MTI Bridge Expired, Please Renew`}
                    </h3>
                </div>
                <div className="subscription-subheader-container">
                    <div className="subscription-subheader-text">
                        If Renewing in Advance Do not Worry, Your Subscription will be extended after the Current Expiry Period.
                    </div>
                </div>

                <div className="subscription-info-container">
                    <div className="subscription-info-left">
                        <div className="register-info">
                            <label htmlFor="registerUserId" className="register-label">Register User Id:</label>
                            <span
                                id="registerUserId"
                                style={{
                                    fontSize: "15px",
                                    fontWeight: "bold",
                                    color: "#2e3a5b",
                                    marginLeft: "-50px",
                                }}
                            >
                                {mainUser}
                            </span>
                        </div>
                        <div className="subscription-validity">
                            <label htmlFor="subscriptionValidity" className="validity-label">Subscription Validity:</label>
                            <span id="subscriptionValidity" className="validity-value">{expiry}</span>
                        </div>
                    </div>
                    <div className="qr-code-container">
                        <div className="qr-code-title">QR CODE</div>
                    </div>
                </div>

                <div className="trial-info-container">
                    <h4 className="trial-info-blue">
                        WE OFFER 7 DAYS FULL FUNCTIONAL TRIAL, NO REFUND REQUEST WILL BE ENTERTAINED ONCE PAID
                    </h4>
                    <h4 className="trial-info-green">
                        FOR INSTANT AND AUTOMATIC RENEWAL, PAY USING UPI, GPAY, PHONEPE WITH QR CODE DISPLAYED AND FILL THE 12 DIGIT UPI / UTR NUMBER
                    </h4>
                </div>

                <div className="renewal-options-container">
                    <div className="renewal-options-left">
                        <div className="renewal-options-radio">
                            <input
                                type="radio"
                                onChange={handleRenew}
                                id="renew"
                                checked={renew === "renew"}
                                name="subscription"
                                value="renew"
                                className="renew-radio"
                            />
                            <label htmlFor="renew" className="renew-label">RENEW</label>

                            <input
                                type="radio"
                                id="AddUser"
                                onChange={handleRenew}
                                checked={renew === "Add user"}
                                name="subscription"
                                value="Add user"
                                disabled={subscriptionType === 'Expired' || subscriptionType === 'Free_Trial'}
                                className="renew-radio"
                            />
                            <label
                                htmlFor="cancel"
                                className={`add-user-label ${subscriptionType === 'Expired' || subscriptionType === 'Free Trial'
                                    ? 'disabled'
                                    : ''
                                    }`}
                            >
                                ADD USER
                            </label>

                            <label className="user-count-label">Number of User Req :</label>
                            <input
                                type="number"
                                id="user-count"
                                name="user-count"
                                min="1"
                                max="100"
                                value={numberOfUsers}
                                onChange={(e) => setNumberOfUsers(parseInt(e.target.value))}
                                className="user-count-input"
                            />
                        </div>
                    </div>
                    <div className="renewal-qr-container">
                        <img src={image} alt="VPS Image" className="qr-image" />
                    </div>
                </div>

                <div className="payment-selection-container">
                    <label htmlFor="renewalPeriod" className="renewal-period-label">Renewal Period:</label>
                    <select
                        id="renewalPeriod"
                        onChange={handleRenewPeriodChange}
                        name="renewalPeriod"
                        className="renewal-period-select"
                    >
                        <option value="1 Month">1 Month @ 750/-</option>
                        <option value="6 Month">6 Month @ 3500/-</option>
                    </select>

                    <label htmlFor="paymentMode" className="payment-mode-label">Payment Mode:</label>
                    <select
                        id="paymentMode"
                        onChange={handlePaymentModeChange}
                        name="paymentMode"
                        className="payment-mode-select"
                    >
                        <option value="UPI">UPI</option>
                        <option value="GPAY">GPay</option>
                        <option value="PhonePe">PhonePe</option>
                    </select>
                </div>

                <div className="amount-details-container">
                    <label htmlFor="renew" className="amount-label">Total Amount:</label>
                    <input type="text" value={totalAmount} readOnly className="amount-input" />

                    <label htmlFor="cancel" className="gst-label">GST(18%):</label>
                    <input type="text" value={gstAmount} readOnly className="amount-input" />

                    <label className="payable-label">Amount Payable:</label>
                    <input type="text" value={amountWithGst} className="amount-input" />
                </div>

                <div className="billing-and-payment-container">
                    <fieldset className="billing-address">
                        <legend className="billing-address-title">Billing Address</legend>
                        <div className="billing-row">
                            <div className="billing-column">
                                <label htmlFor="name" className="billing-label">Name:</label>
                                <input type="text" id="name" className="billing-input" />
                            </div>
                            <div className="billing-column">
                                <label htmlFor="gst" className="billing-label">GST (Optional):</label>
                                <input type="text" id="gst" className="billing-input gst-input" />
                            </div>
                        </div>
                        <div className="billing-row">
                            <div className="billing-column">
                                <label htmlFor="address" className="billing-label">Address:</label>
                                <textarea id="address" className="billing-textarea"></textarea>
                            </div>
                            <div className="billing-column">
                                <div className="billing-subrow">
                                    <label htmlFor="email" className="billing-label">Email:</label>
                                    <input type="email" id="email" className="billing-input" />
                                </div>
                                <div className="billing-subrow">
                                    <label htmlFor="phone" className="billing-label">Mobile:</label>
                                    <input type="text" id="phone" className="billing-input" />
                                </div>
                            </div>
                        </div>
                        <div className="billing-row">
                            <div className="billing-column">
                                <label htmlFor="pincode" className="billing-label">Pincode:</label>
                                <input type="text" id="pincode" className="billing-input" />
                            </div>
                            <div className="billing-column">
                                <label htmlFor="city" className="billing-label">City:</label>
                                <input type="text" id="city" className="billing-input" />
                            </div>
                            <div className="billing-column">
                                <label htmlFor="state" className="billing-label">State:</label>
                                <input type="text" id="state" className="billing-input" />
                            </div>
                        </div>
                    </fieldset>

                    <div className="payment-instructions">
                        <div className="payment-instructions-title">Payment Instructions</div>
                        <div className="payment-instructions-section">
                            <div className="upi-id-title">UPI ID (Only If above QR Code fails):</div>
                            <div className="upi-id-value">**********@hdfcbank</div>
                        </div>
                        <div className="payment-instructions-section">
                            <div className="mobile-title">Mobile:</div>
                            <div className="mobile-value">**********</div>
                        </div>
                        <div className="payment-instructions-section">
                            <div className="instructions-title">Instructions:</div>
                            <div className="instructions-content">
                                <p className="instructions-highlight">
                                    PAY USING QR & FILL ACCURATE DETAILS FOR AN INSTANT/FAST RENEWAL
                                </p>
                                <p>
                                    Now you can pay the above-mentioned amount using any UPI-supported app (e.g., PayTM,
                                    PhonePe, Google Pay) or directly from your bank app. Just scan the QR code and pay.
                                </p>
                                <p>
                                    After successful payment, please fill in the Transaction ID below and submit for renewal.
                                    Renewal will be processed only for successful transactions, and it may take a few minutes
                                    to reflect the renewal.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <fieldset className="payment-details">
                    <legend className="payment-details-title">Payment Details</legend>
                    <div className="payment-row">
                        <label htmlFor="name" className="payment-label">Payment Txn No/Order Id:</label>
                        <input
                            type="number"
                            value={orderId}
                            onChange={handleInputChange}
                            className="payment-input"
                        />
                        <button
                            onClick={payments}
                            disabled={isButtonDisabled}
                            className={`payment-button ${paymentStatus}`}
                            style={{
                                cursor: isButtonDisabled ? "not-allowed" : "pointer",
                                opacity: isButtonDisabled ? 0.5 : 1,
                            }}
                        >
                            {getButtonContent()}
                        </button>
                    </div>
                    <div className="payment-warning">
                        <h5 className="payment-warning-text">Enter 12 Digit UPI/UTR Transaction ID</h5>
                    </div>
                    <div className="payment-row">
                        <label htmlFor="pincode" className="payment-label">Amount Paid:</label>
                        <input
                            type="text"
                            id="pincode"
                            value={payment}
                            onChange={handleAmountChange}
                            className="payment-input"
                        />
                    </div>
                    <div className="payment-warning">
                        <h5 className="payment-warning-text">
                            Submitting Invalid/Fraud Transaction can Disable the User ID permanently
                        </h5>
                    </div>
                </fieldset>
            </div>
        </div>
    );
}

export default Subscription;