import React, { useState, useEffect, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Typography,
  Checkbox,
  TextField,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Autocomplete,
  CircularProgress,
  Paper,
  Snackbar,
  Alert,
} from "@mui/material";
import { styled } from "@mui/system";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import CheckCircle from "@mui/icons-material/CheckCircle";
import { setmasterChildAccounts } from "../store/slices/master_child";
import Cookies from "universal-cookie";
import { fetchWithAuth } from "../utils/api";
import Draggable from "react-draggable";

const cookies = new Cookies();


function DraggablePaper(props) {
  return (
    <Draggable
      handle=".draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}

const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    width: "90%",
    maxWidth: 1050,
    borderRadius: 8,
    boxShadow: "0 8px 16px rgba(0, 0, 0, 0.1)",
    maxHeight: "95vh",
    overflowY: "auto",
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  padding: "2px 2px",
  textAlign: "center",
  borderBottom: "1px solid #eee",
  fontSize: "15px",
}));

const StyledButton = styled(Button)(({ theme }) => ({
  backgroundColor: "#4661bd",
  color: "white",
  padding: "4px 12px",
  fontSize: "13px",
  "&:hover": { backgroundColor: "#1e88e5" },
  "&:disabled": { backgroundColor: "#90caf9", color: "white" },
}));

const SaveButton = styled(Button)(({ theme }) => ({
  backgroundColor: "#4caf50",
  color: "#fff",
  padding: "6px 16px",
  "&:hover": { backgroundColor: "#388e3c" },
}));

const CancelButton = styled(Button)(({ theme }) => ({
  backgroundColor: "#ef5350",
  color: "#fff",
  padding: "6px 16px",
  "&:hover": { backgroundColor: "#d81b60" },
}));

const AccountName = styled(Typography)({
  fontWeight: "500",
  color: "#424242",
  fontSize: "15px",
});

const AccountRow = ({ children, isSelected, onRemove }) => (
  <TableRow
    hover
    sx={{
      backgroundColor: isSelected ? "rgba(66, 165, 245, 0.05)" : "transparent",
    }}
  >
    {onRemove && (
      <StyledTableCell>
        <IconButton onClick={onRemove} size="small">
          <RemoveIcon sx={{ color: "#ef5350", fontSize: 20 }} />
        </IconButton>
      </StyledTableCell>
    )}
    {children}
  </TableRow>
);

const AccountTable = ({ headerLabels, children, maxHeight = 180 }) => (
  <TableContainer component={Paper} sx={{ maxHeight, borderRadius: 6, boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)" }}>
    <Table stickyHeader>
      <TableHead>
        <TableRow sx={{ bgcolor: "#f5f5f5", }}>
          {headerLabels.map((label) => (
            <StyledTableCell key={label} sx={{ fontWeight: "600", bgcolor: "#D8E1FF", color: "#616161", fontSize: "17px" }}>
              {label}
            </StyledTableCell>
          ))}
        </TableRow>
      </TableHead>
      <TableBody>{children}</TableBody>
    </Table>
  </TableContainer>
);

const MasterChild = ({
  open,
  onClose,
  selectedItems,
  selectedMasterAccount,
  mode,
}) => {
  const { brokers: rows } = useSelector((state) => state.brokerReducer);
  const dispatch = useDispatch();
  const [ mainUser, setMainUser ] = useState(cookies.get("USERNAME"));
  const [ cookieError, setCookieError ] = useState(false);

  // Handle cookie access with retry mechanism
  useEffect(() => {
    // If mainUser is already defined, no need to do anything
    if (mainUser) return;

    // Try to get the cookie again
    const username = cookies.get("USERNAME");
    if (username) {
      setMainUser(username);
      setCookieError(false);
    } else {
      // Set up a retry mechanism
      const retryInterval = setInterval(() => {
        const retryUsername = cookies.get("USERNAME");
        if (retryUsername) {
          setMainUser(retryUsername);
          setCookieError(false);
          clearInterval(retryInterval);
        }
      }, 1000); // Try every second

      // Clear the interval after 10 seconds to prevent infinite retries
      setTimeout(() => {
        clearInterval(retryInterval);
        if (!mainUser) {
          setCookieError(true);
          console.error("Failed to retrieve USERNAME cookie after multiple attempts");
        }
      }, 10000);

      // Clean up the interval on component unmount
      return () => clearInterval(retryInterval);
    }
  }, [ mainUser ]);

  const { masterChildAccounts: allMasterChildAccounts } = useSelector((state) => state.masterChildAccountsReducer);

  const [ unselectedRows, setUnselectedRows ] = useState(() => {
    const excludedUserIds = new Set([ selectedItems?.[ 0 ]?.broker_user_id ?? selectedMasterAccount?.userId ]);

    selectedItems?.[ 0 ]?.child_accounts?.forEach((acc) => excludedUserIds.add(acc.userId || acc.broker_user_id));

    allMasterChildAccounts.forEach(masterChild => {
      if (mode === 'edit' && masterChild.broker_user_id === (selectedItems?.[ 0 ]?.broker_user_id ?? selectedMasterAccount?.userId)) {
        return;
      }

      excludedUserIds.add(masterChild.broker_user_id);

      masterChild.child_accounts.forEach(childAcc => {
        excludedUserIds.add(childAcc.broker_user_id || childAcc.userId);
      });
    });

    const filteredRows = rows.filter((row) => !excludedUserIds.has(row.userId) && row.inputDisabled);

    return filteredRows.sort((a, b) => a.userId.localeCompare(b.userId));
  });

  const [ selectedChildAccounts, setSelectedChildAccounts ] = useState(() => {
    const childAccounts = selectedItems?.[ 0 ]?.child_accounts || [];
    return [ ...childAccounts ].sort((a, b) => a.id - b.id);
  });
  const [ multipliers, setMultipliers ] = useState(() => {
    const initialMultipliers = {};
    selectedItems?.[ 0 ]?.child_accounts?.forEach((acc) => {
      initialMultipliers[ acc.userId || acc.broker_user_id ] = acc.multiplier || 1;
    });
    return initialMultipliers;
  });

  const getBooleanValue = (value) => {
    return value === false ? false : true;
  };

  const [ copyPlacement, setCopyPlacement ] = useState(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      return getBooleanValue(selectedItems[ 0 ]?.copy_placement);
    }
    return true;
  });

  const [ copyCancellation, setCopyCancellation ] = useState(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      return getBooleanValue(selectedItems[ 0 ]?.copy_cancellation);
    }
    return true;
  });

  const [ copyModification, setCopyModification ] = useState(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      return getBooleanValue(selectedItems[ 0 ]?.copy_modification);
    }
    return true;
  });

  const [ parallelOrderExecution, setParallelOrderExecution ] = useState(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      return getBooleanValue(selectedItems[ 0 ]?.parallel_order_execution);
    }
    return true;
  });

  const [ autoSplitFrozenQty, setAutoSplitFrozenQty ] = useState(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      return getBooleanValue(selectedItems[ 0 ]?.auto_split_frozen_qty);
    }
    return true;
  });
  const [ copyStartTime, setCopyStartTime ] = useState(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      return selectedItems[ 0 ]?.copy_start_time?.slice(0, 5) || "08:00";
    }
    return "08:00";
  });

  const [ copyEndTime, setCopyEndTime ] = useState(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      return selectedItems[ 0 ]?.copy_end_time?.slice(0, 5) || "22:00";
    }
    return "22:00";
  });
  const [ isSaving, setIsSaving ] = useState(false);
  const [ popupOpen, setPopupOpen ] = useState(false);
  const [ popupData, setPopupData ] = useState(null);
  const [ childAddedCount, setChildAddedCount ] = useState(0);
  const [ childDeletedCount, setChildDeletedCount ] = useState(0);
  const [ error, setError ] = useState("");
  const [ successMessage, setSuccessMessage ] = useState("");

  const timeOptions = useMemo(() => {
    const times = [];
    for (let hour = 8; hour <= 24; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        times.push(`${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`);
      }
    }
    return times;
  }, []);

  const manageId = "userId";

  const handleMultiplierChange = (userId, value) => {
    const numValue = Number(value);
    if (numValue >= 1) {
      setMultipliers((prev) => {
        const updated = { ...prev, [ userId ]: numValue };
        return updated;
      });
      setSelectedChildAccounts((prev) => {
        const updated = prev.map((acc) =>
          (acc.userId || acc.broker_user_id) === userId ? { ...acc, multiplier: numValue } : acc
        );
        return updated;
      });
    }
  };
  const handleAddToChildAccounts = (row) => {
    const rowId = row.userId || row.broker_user_id;

    if (!selectedChildAccounts.some((acc) => (acc.userId || acc.broker_user_id) === rowId)) {
      console.log("Adding account to child accounts:", rowId);

      const newAccount = {
        ...row,
        multiplier: 1,
        userId: rowId,
        broker_user_id: rowId,
        inputDisabled: true,
        live: row.live !== undefined ? row.live : true
      };

      setSelectedChildAccounts((prev) => {
        const updatedAccounts = [ ...prev, newAccount ];
        return updatedAccounts.sort((a, b) => {
          if (a.id && b.id) return a.id - b.id;
          if (a.id) return -1;
          if (b.id) return 1;
          return 0;
        });
      });

      setUnselectedRows((prev) =>
        prev.filter((acc) => {
          const accId = acc.userId || acc.broker_user_id;
          return accId !== rowId;
        })
      );

      setMultipliers((prev) => ({ ...prev, [ rowId ]: 1 }));

      setChildAddedCount((prev) => prev + 1);
      setChildDeletedCount((prev) => Math.max(0, prev - 1));
    }
  };

  const handleRemoveFromChildAccounts = (row) => {
    const rowId = row.userId || row.broker_user_id;
    console.log("Removing row with ID:", rowId);

    setSelectedChildAccounts((prev) => {
      const updated = prev.filter((acc) => {
        const accId = acc.userId || acc.broker_user_id;
        return accId !== rowId;
      });
      return updated.sort((a, b) => {
        if (a.id && b.id) return a.id - b.id;
        if (a.id) return -1;
        if (b.id) return 1;
        return 0;
      });
    });

    setUnselectedRows((prev) => {
      const alreadyExists = prev.some(acc => {
        const accId = acc.userId || acc.broker_user_id;
        return accId === rowId;
      });

      if (alreadyExists) {
        return prev;
      } else {
        const standardizedRow = {
          ...row,
          userId: rowId,
          broker_user_id: rowId,
          inputDisabled: true,
          live: row.live !== undefined ? row.live : true
        };

        const originalRow = rows.find(r => (r.userId || r.broker_user_id) === rowId);

        const mergedRow = originalRow ? { ...originalRow, ...standardizedRow } : standardizedRow;

        const updatedRows = [ ...prev, mergedRow ];
        return updatedRows.sort((a, b) => a.userId.localeCompare(b.userId));
      }
    });

    setChildDeletedCount((prev) => prev + 1);
    setChildAddedCount((prev) => Math.max(0, prev - 1));

    if (mode === "edit" && selectedItems?.length > 0 && mainUser) {
      fetchWithAuth(`/api/delete_child_account/${mainUser}/${rowId}`, {
        method: "DELETE",
      }).catch((error) => console.error("Error deleting child account:", error));
    }
  };

  const fetchMasterAccounts = async () => {
    if (!mainUser) {
      console.error("Cannot fetch master accounts: mainUser is undefined");
      setError("User information not available. Please try refreshing the page.");
      return;
    }

    try {
      const response = await fetchWithAuth(`/api/fetch_master_child_accounts/${mainUser}`, {
        method: "GET",
      });
      if (response.ok) {
        const masterChildAccounts = await response.json();
        dispatch(setmasterChildAccounts({ masterChildAccounts }));
      }
    } catch (error) {
      console.error("Error fetching master accounts:", error);
      setError("Failed to fetch master accounts.");
    }
  };

  const handleSave = async () => {
    if (selectedChildAccounts.length === 0) {
      setError(mode === "create" ? "No child account selected" : "At least one child account required");
      return;
    }

    setIsSaving(true);
    setError("");
    setSuccessMessage("");
    const payload = {
      masterAccount: {
        name: selectedMasterAccount?.name || selectedItems?.[ 0 ]?.name,
        broker_user_id: selectedMasterAccount?.userId || selectedItems?.[ 0 ]?.broker_user_id,
        broker: selectedMasterAccount?.broker || selectedItems?.[ 0 ]?.broker,
        copyPlacement: copyPlacement,
        copyCancellation: copyCancellation,
        copyModification: copyModification,
        parallelOrderExecution: parallelOrderExecution,
        autoSplitFrozenQty: autoSplitFrozenQty,
        copyStartTime: copyStartTime,
        copyEndTime: copyEndTime,
      },
      childAccounts: selectedChildAccounts.map((acc) => ({
        name: acc.name,
        multiplier: acc.multiplier || 1,
        broker_user_id: acc.userId || acc.broker_user_id,
        broker: acc.broker,
        live: acc.live,
      })),
    };

    try {
      if (!mainUser) {
        throw new Error("User information not available. Please try refreshing the page.");
      }

      const response = await fetchWithAuth(`/api/create_master_child_accounts/${mainUser}`, {
        method: "POST",
        body: JSON.stringify(payload),
      });
      if (response.ok) {
        await fetchMasterAccounts();
        setPopupData({
          masterName: payload.masterAccount.name,
          masterLoginId: payload.masterAccount.broker_user_id,
          childAdded: Math.max(childAddedCount, 0),
          childUpdated: childAddedCount + childDeletedCount,
          childDeleted: Math.max(childDeletedCount, 0),
        });
        setPopupOpen(true);
        setSuccessMessage("Master account saved successfully!");
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save");
      }
    } catch (error) {
      console.error("Save error:", error);
      setError(error.message || "An error occurred while saving.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleClosePopup = () => {
    setPopupOpen(false);
    onClose();
  };

  useEffect(() => {
    if (open) {
      setError("");
      setSuccessMessage("");
    }
  }, [ open ]);

  useEffect(() => {
    if (mode === 'edit' && selectedItems?.length > 0) {
      setCopyPlacement(getBooleanValue(selectedItems[ 0 ]?.copy_placement));
      setCopyCancellation(getBooleanValue(selectedItems[ 0 ]?.copy_cancellation));
      setCopyModification(getBooleanValue(selectedItems[ 0 ]?.copy_modification));
      setParallelOrderExecution(getBooleanValue(selectedItems[ 0 ]?.parallel_order_execution));
      setAutoSplitFrozenQty(getBooleanValue(selectedItems[ 0 ]?.auto_split_frozen_qty));

      setCopyStartTime(selectedItems[ 0 ]?.copy_start_time?.slice(0, 5) || "08:00");
      setCopyEndTime(selectedItems[ 0 ]?.copy_end_time?.slice(0, 5) || "22:00");
    }
  }, [ mode, selectedItems ]);

  useEffect(() => {
    if (selectedMasterAccount || selectedItems?.length > 0) {
      const excludedUserIds = new Set([ selectedItems?.[ 0 ]?.broker_user_id ?? selectedMasterAccount?.userId ]);

      selectedChildAccounts.forEach(acc => {
        const accId = acc.userId || acc.broker_user_id;
        excludedUserIds.add(accId);
      });

      allMasterChildAccounts.forEach(masterChild => {
        if (mode === 'edit' && masterChild.broker_user_id === (selectedItems?.[ 0 ]?.broker_user_id ?? selectedMasterAccount?.userId)) {
          return;
        }

        excludedUserIds.add(masterChild.broker_user_id);

        masterChild.child_accounts.forEach(childAcc => {
          excludedUserIds.add(childAcc.broker_user_id || childAcc.userId);
        });
      });

      const availableAccounts = rows
        .filter(row => {
          const rowId = row.userId || row.broker_user_id;
          return !excludedUserIds.has(rowId) && row.inputDisabled;
        })
        .map(row => {
          const rowId = row.userId || row.broker_user_id;
          return {
            ...row,
            userId: rowId,
            broker_user_id: rowId,
            inputDisabled: true,
            live: row.live !== undefined ? row.live : true
          };
        });

      const sortedAccounts = availableAccounts.sort((a, b) => {
        return a.userId.localeCompare(b.userId);
      });

      setUnselectedRows(sortedAccounts);
    }
  }, [ selectedMasterAccount, selectedItems, rows, allMasterChildAccounts, mode, selectedChildAccounts ]);

  // Show an error message if we can't get the username cookie
  if (cookieError) {
    return (
      <Dialog open={open} onClose={onClose}>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Typography color="error">
            Unable to retrieve user information. Please try refreshing the page or logging in again.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <StyledDialog
      open={open}
      onClose={onClose}
      PaperComponent={DraggablePaper}
      aria-labelledby="draggable-dialog-title"
    >
      <DialogTitle
        className="draggable-dialog-title"
        style={{ cursor: 'move' }}
        sx={{
          bgcolor: "#D8E1FF", color: "#4661bd"
          , py: 1, fontSize: "22px", fontWeight: "650"
        }}
        id="draggable-dialog-title"
      >
        {mode === "edit" ? "Edit Master Account" : "Create Master Account"}
      </DialogTitle>
      <DialogContent sx={{ p: 2.5, bgcolor: "#fafafa" }}>
        <Grid container spacing={2.5}>
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ color: "#4661bd", fontSize: "18px", fontWeight: "600" }}>
              Master Account
            </Typography>
            <Grid container spacing={1}>
              <Grid item xs={3}>
                <TextField
                  label="Name"
                  value={selectedMasterAccount?.name || selectedItems?.[ 0 ]?.name || ""}
                  fullWidth
                  InputProps={{ readOnly: true }}
                  variant="outlined"
                  size="small"
                />
              </Grid>
              <Grid item xs={3}>
                <TextField
                  label="Login ID"
                  value={selectedMasterAccount?.userId || selectedItems?.[ 0 ]?.broker_user_id || ""}
                  fullWidth
                  InputProps={{ readOnly: true }}
                  variant="outlined"
                  size="small"
                />
              </Grid>
              <Grid item xs={3}>
                <TextField
                  label="Broker"
                  value={selectedMasterAccount?.broker || selectedItems?.[ 0 ]?.broker || ""}
                  fullWidth
                  InputProps={{ readOnly: true }}
                  variant="outlined"
                  size="small"
                />
              </Grid>
              <Grid item xs={20}>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  <FormControlLabel
                    control={<Checkbox checked={copyPlacement} onChange={(e) => setCopyPlacement(e.target.checked)} size="small" />}
                    label="Copy Placement"
                    sx={{ m: 0 }}
                  />
                  <FormControlLabel
                    control={<Checkbox checked={copyCancellation} onChange={(e) => setCopyCancellation(e.target.checked)} size="small" />}
                    label="Copy Cancellation"
                    sx={{ m: 0 }}
                  />
                  <FormControlLabel
                    control={<Checkbox checked={copyModification} onChange={(e) => setCopyModification(e.target.checked)} size="small" />}
                    label="Copy Modification"
                    sx={{ m: 0 }}
                  />
                  <FormControlLabel
                    control={<Checkbox checked={parallelOrderExecution} onChange={(e) => setParallelOrderExecution(e.target.checked)} size="small" />}
                    label="Parallel Order Execution"
                    sx={{ m: 0 }}
                  />
                  <FormControlLabel
                    control={<Checkbox checked={autoSplitFrozenQty} onChange={(e) => setAutoSplitFrozenQty(e.target.checked)} size="small" />}
                    label="Auto Split Frozen Qty"
                    sx={{ m: 0 }}
                  />
                </Box>
              </Grid>
              <Grid item xs={3}>
                <Autocomplete
                  options={timeOptions}
                  value={copyStartTime}
                  onChange={(_, value) => setCopyStartTime(value || "08:00")}
                  renderInput={(params) => <TextField {...params} label="Start Time" variant="outlined" size="small" />}
                />
              </Grid>
              <Grid item xs={3}>
                <Autocomplete
                  options={timeOptions}
                  value={copyEndTime}
                  onChange={(_, value) => setCopyEndTime(value || "22:00")}
                  renderInput={(params) => <TextField {...params} label="End Time" variant="outlined" size="small" />}
                />
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h6" sx={{ color: "#4661bd", fontSize: "16px", fontWeight: "600", mt: 1 }}>
              Child Accounts
            </Typography>
            <AccountTable headerLabels={[ "Remove", "Name", "Multiplier", "Login", "Broker", "Live" ]}>
              {selectedChildAccounts.length > 0 ? (
                selectedChildAccounts.map((row, index) => {
                  const rowId = row.userId || row.broker_user_id || `fallback-${index}`;
                  return (
                    <AccountRow
                      key={rowId}
                      isSelected={true}
                      onRemove={() => handleRemoveFromChildAccounts(row)}
                      rowId={rowId}
                    >
                      <StyledTableCell><AccountName>{row.name}</AccountName></StyledTableCell>
                      <StyledTableCell>
                        <TextField
                          type="number"
                          value={multipliers[ rowId ] ?? row.multiplier ?? 1}
                          onChange={(e) => handleMultiplierChange(rowId, e.target.value)}
                          inputProps={{ min: 1 }}
                          variant="outlined"
                          size="small"
                          sx={{
                            width: 60,
                            '& .MuiInputBase-root': {
                              height: 28,
                              fontSize: '0.875rem',
                            },
                          }}
                        />
                      </StyledTableCell>
                      <StyledTableCell>{row.userId || row.broker_user_id}</StyledTableCell>
                      <StyledTableCell>{row.broker}</StyledTableCell>
                      <StyledTableCell>{row.live ? "Yes" : "No"}</StyledTableCell>
                    </AccountRow>
                  );
                })
              ) : (
                <TableRow>
                  <StyledTableCell colSpan={6} sx={{ color: "#ef5350", fontStyle: "italic" }}>
                    No Child Accounts Selected
                  </StyledTableCell>
                </TableRow>
              )}
            </AccountTable>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h6" sx={{ color: "#4661bd", fontSize: "18px", fontWeight: "600", mt: 1 }}>
              Available Accounts
            </Typography>
            <AccountTable headerLabels={[ "Add", "Name", "Multiplier", "Login", "Broker", "Live" ]}>
              {unselectedRows.length > 0 ? (
                unselectedRows.map((row) => (
                  <AccountRow key={row[ manageId ]} isSelected={false}>
                    <StyledTableCell>
                      <StyledButton variant="contained" size="small" onClick={() => handleAddToChildAccounts(row)}>
                        <AddIcon fontSize="small" bgcolor="#4661bd" />
                      </StyledButton>
                    </StyledTableCell>
                    <StyledTableCell><AccountName>{row.name}</AccountName></StyledTableCell>
                    <StyledTableCell>1</StyledTableCell>
                    <StyledTableCell>{row.userId || row.broker_user_id}</StyledTableCell>
                    <StyledTableCell>{row.broker}</StyledTableCell>
                    <StyledTableCell>{row.inputDisabled ? "Yes" : "No"}</StyledTableCell>
                  </AccountRow>
                ))
              ) : (
                <TableRow>
                  <StyledTableCell colSpan={6} sx={{ color: "#ef5350", fontStyle: "italic" }}>
                    No Available Accounts
                  </StyledTableCell>
                </TableRow>
              )}
            </AccountTable>
          </Grid>

          {error && (
            <Grid item xs={12}>
              <Alert severity="error" sx={{ mt: 1 }}>{error}</Alert>
            </Grid>
          )}
        </Grid>
      </DialogContent>
      <DialogActions sx={{ p: 1, justifyContent: "flex-end" }}>
        <CancelButton
          variant="contained"
          onClick={onClose}
          sx={{ minWidth: 100 }}  // Set desired width
        >
          Cancel
        </CancelButton>
        <SaveButton
          variant="contained"
          onClick={handleSave}
          disabled={isSaving}
          startIcon={isSaving ? <CircularProgress size={16} color="inherit" /> : null}
          sx={{ minWidth: 100 }}  // Same width as Cancel button
        >
          Save
        </SaveButton>
      </DialogActions>
      <Dialog
        open={popupOpen}
        onClose={handleClosePopup}
        PaperComponent={DraggablePaper}
        aria-labelledby="draggable-success-dialog-title"
      >
        <DialogTitle
          className="draggable-dialog-title"
          style={{ cursor: 'move' }}
          id="draggable-success-dialog-title"
          sx={{ textAlign: "center", color: "#4caf50", py: 2, fontSize: "18px" }}
        >
          <CheckCircle sx={{ fontSize: 28, mr: 1 }} />
          Success
        </DialogTitle>
        <DialogContent>
          {popupData && (
            <Box textAlign="center">
              <Typography variant="body1" sx={{ fontWeight: "500" }}>
                Master [{popupData.masterName} : {popupData.masterLoginId}] Saved
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Added: {popupData.childAdded}  | Deleted: {popupData.childDeleted}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", pb: 2 }}>
          <StyledButton variant="contained" onClick={handleClosePopup}>OK</StyledButton>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={!!successMessage}
        autoHideDuration={4000}
        onClose={() => setSuccessMessage("")}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage("")}>
          {successMessage}
        </Alert>
      </Snackbar>
    </StyledDialog>
  );
};

export default MasterChild;
