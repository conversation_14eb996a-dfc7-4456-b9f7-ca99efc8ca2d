import React from "react";

const QTPChromePluginSection = ({
    quickTradeEnabled,
    setQuickTradeEnabled,
    ninjaProvider,
    setNinjaProvider,
}) => (
    <div className="qtp-chrome-plugin-section" style={{
        padding: "10px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box"
    }}>
        <div style={{ marginBottom: "30px" }}>
            <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <input
                    type="checkbox"
                    checked={quickTradeEnabled}
                    onChange={() => setQuickTradeEnabled(prev => !prev)}
                    style={{ width: "16px", height: "16px" }}
                />
                <span style={{ fontSize: "18px", fontWeight: "600" }}>Enable Quick Trade Panel Chrome Plugin MTI</span>
            </label>
            <p style={{ fontSize: "13px", color: "#666", marginTop: "8px", marginLeft: "24px" }}>
                MTI Bridge is equipped with Quick Trade Panel where users can take trades manually by utilizing advanced Risk / Money and Order Management features such as Stoploss,Targets,Trailing,etc
            </p>
            <p style={{ fontSize: "13px", color: "#666", marginTop: "8px", marginLeft: "24px" }}>
                Even Manually Placed Trades can now attain the benefits of algo trading like target exits and SL management with MIS orders. Just the way you would execute at brokers,but glad to say we are lil advanced than your beloved brokers Quick Trade Panel can take your symbol directly from chart of active chrome tab bu using a Chrome Plugin            </p>
        </div>

        <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
            gap: "20px",
            marginBottom: "30px"
        }}>
            <div>
                <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "15px" }}>Data Providers</h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "15px" }}>
                    <div>
                        <label style={{ display: "block", fontSize: "14px", marginBottom: "8px" }}>
                            Ninja Trading Provider
                        </label>
                        <select
                            value={ninjaProvider}
                            onChange={(e) => setNinjaProvider(e.target.value)}
                            style={{
                                width: "100%",
                                padding: "8px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                background: "#fff"
                            }}
                        >
                            <option value="TrueData">TrueData</option>
                            <option value="NinjaTrader">NinjaTrader</option>
                            <option value="Custom">Custom</option>
                        </select>
                    </div>
                    <div>
                        <label style={{ display: "block", fontSize: "14px", marginBottom: "8px" }}>
                            AmiBroker
                        </label>
                        <select
                            value={ninjaProvider}
                            onChange={(e) => setNinjaProvider(e.target.value)}
                            style={{
                                width: "100%",
                                padding: "8px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                background: "#fff"
                            }}
                        >
                            <option value="AmiBroker">AmiBroker</option>
                            <option value="Custom">Custom</option>
                        </select>
                    </div>
                    <div>
                        <label style={{ display: "block", fontSize: "14px", marginBottom: "8px" }}>
                            MT4/5
                        </label>
                        <select
                            value={ninjaProvider}
                            onChange={(e) => setNinjaProvider(e.target.value)}
                            style={{
                                width: "100%",
                                padding: "8px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                background: "#fff"
                            }}
                        >
                            <option value="MetaTrade">MetaTrade</option>
                            <option value="Custom">Custom</option>
                        </select>
                    </div>
                </div>
            </div>

            <div>
                <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "15px" }}>Plugin Installation</h3>
                <div style={{ background: "#f8fafc", padding: "30px", borderRadius: "8px" }}>
                    <p style={{ fontSize: "14px", marginBottom: "10px" }}>
                        <a href="#" style={{ color: "#4661bd", textDecoration: "none", fontWeight: "600" }}>
                            Download QTP Chrome Plugin
                        </a>
                    </p>
                    <ul style={{ paddingLeft: "20px", fontSize: "14px", color: "#666", margin: 0 }}>
                        <li style={{ marginBottom: "8px" }}>Close Chrome browser before installation</li>
                        <li style={{ marginBottom: "8px" }}>Please click on the link which will open the Plugin Page in Chrome. There you need to click on IABQTP 2.4.crx file and then 'Click Add Extension' when asked by Chrome</li>
                        <li style={{ marginBottom: "8px" }}>After installing the Plugin, remember to click the 'Configure' button below.</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "15px"
        }}>
            <button style={{
                padding: "12px 25px",
                background: "#4661bd",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "background 0.3s ease"
            }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Configure
            </button>
            <button style={{
                padding: "12px 25px",
                background: "#fff",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "all 0.3s ease"
            }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default QTPChromePluginSection;