import React, { useState, useRef, useEffect } from "react";
import "../styles.css";
import {
  OptimizedMarketIndex,
  OptimizedTopNav,
  OptimizedErrorContainer,
  OptimizedLeftNav,
  OptimizedRightNav,
} from "../components/Layout/OptimizedComponenets";
import { useSelector, useDispatch } from "react-redux";
import {
  setBrokers,
  setAllSeq,
  setAllVis,
  setConsoleMsgs,
} from "../store/slices";
import Cookies from "universal-cookie";
import { Oval } from "react-loader-spinner";
import Draggable from "react-draggable";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import useClickOutside from "../hooks/useClickOutside";
import EditIcon from "@mui/icons-material/Edit";
import TableHeaderWithFilter from "../components/TableHeaderWithFilter";
import filterIcon from "../assets/newFilter.png";
import { fetchWithAuth } from "../utils/api";

function OrderFlow() {
  const errorContainerRef = useRef(null);
  const { orders: orderBook } = useSelector((state) => state.orderBookReducer);
  const cookies = new Cookies();
  const mainUser = cookies.get("USERNAME");
  const token = cookies.get("TOKEN");
  const { brokers: rows } = useSelector((state) => state.brokerReducer);
  const dispatch = useDispatch();
  const { collapsed } = useSelector((state) => state.collapseReducer);
  const [ msgs, setMsgs ] = useState([]);

  const allSeqState = useSelector((state) => state.allSeqReducer);
  const allVisState = useSelector((state) => state.allVisReducer);

  const orderFlowCols = [
    "Client ID",
    "Stock Symbol",
    "Exchange",
    "Edit",
    "Order Time",
    "Trade ID",
    "Transaction",
    "Avg Execution Price",
    "Order Size",
    "Execution Quantity",
    "Trade Type",
    "Product Type",
    "Price",
    "Trigger Price",
    "Trigger Time",
    "Exchange Trade ID",
    "Instrument",
    "Trade Duration",
    "Trade Status",
    "Display Name",
    "Status Message",
    "Label",
  ];
  const [ orderFlowColVis, setOrderFlowColVis ] = useState(() => {
    const initialVisibility = {};
    orderFlowCols.forEach(col => {
      initialVisibility[ col ] = true;
    });
    return initialVisibility;
  });
  const [ orderFlowSeq, setOrderFlowSeq ] = useState(allSeqState.orderFlowSeq);

  const handleClearLogs = () => {
    if (msgs.length === 0) return;
    setMsgs([]);
  };

  const handleMsg = (Msg) => {
    const messageWithColor = { ...Msg, color: Msg.color || "black" };
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;
      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === messageWithColor.msg &&
        lastMsg.user === messageWithColor.user &&
        lastMsg.strategy === messageWithColor.strategy &&
        lastMsg.portfolio === messageWithColor.portfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ messageWithColor, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ messageWithColor, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };



  const [ orderFlowColsSelectedALL, setOrderFlowColsSelectedALL ] = useState(false);

  const orderFlowColSelectALL = () => {
    const newValue = !orderFlowColsSelectedALL;
    setOrderFlowColsSelectedALL(newValue);

    const updatedVisibility = {};
    orderFlowCols.forEach(col => {
      updatedVisibility[ col ] = !newValue;
    });

    setOrderFlowColVis(updatedVisibility);

    if (!newValue) {
      setOrderFlowSeq(orderFlowCols);
    } else {
      setOrderFlowSeq([]);
    }
  };

  useEffect(() => {
    dispatch(setAllVis({ ...allVisState, orderFlowVis: orderFlowColVis }));
  }, [ orderFlowColVis ]);

  useEffect(() => {
    dispatch(setAllSeq({ ...allSeqState, orderFlowSeq: orderFlowSeq }));
  }, [ orderFlowSeq ]);

  const filterPopupRef = useRef(null);
  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  const mappedOrders = React.useMemo(() => {
    return orderBook.map((order) => ({
      "Client ID": order.broker_user_id,
      "Stock Symbol": order.symbol,
      Exchange: order.exchange,
      Edit: "0",
      "Order Time": order.orderDateTime,
      "Trade ID": order.id,
      Transaction: order.side,
      "Avg Execution Price": order.tradedPrice,
      "Order Size": order.qty,
      "Execution Quantity": order.qty,
      "Trade Type": order.order_type,
      "Product Type": order.product_type,
      Price: order.tradedPrice,
      "Trigger Price": "",
      "Trigger Time": "",
      "Exchange Trade ID": "",
      Instrument: "",
      "Trade Duration": "",
      "Trade Status": order.status,
      "Display Name": "",
      "Status Message": "",
      Label: order.orderTag,
      LTP: order.LTP,
    }));
  }, [ orderBook ]);

  const [ filters, setFilters ] = useState({});
  const [ filterPopup, setFilterPopup ] = useState(null);
  const [ tempFilters, setTempFilters ] = useState({});
  const [ filteredData, setFilteredData ] = useState(mappedOrders);
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 });

  // Get unique values for a column, considering whether it's the first filter or not
  const getDynamicUniqueValues = (column) => {
    // For the first filter or if no filters are applied yet, show all available values from mappedOrders
    const isFirstFilter = Object.keys(filters).length === 0 ||
      (Object.keys(filters).length === 1 && filters[ column ]);

    // Use the original data (mappedOrders) for the first filter column
    // For subsequent filters, use the already filtered data
    const sourceData = isFirstFilter ? mappedOrders : filteredData;

    return Array.from(new Set(sourceData.map((row) => row[ column ] || "")));
  };

  const handleFilterToggle = (column, event) => {
    // Add null check for event and event.target
    if (!event || !event.target) {
      console.error('Event or event.target is undefined in handleFilterToggle');
      return;
    }

    try {
      const { top, left, height } = event.target.getBoundingClientRect();
      setFilterPopup(filterPopup === column ? null : column);
      setPopupPosition({ top: top + height, left });
      setTempFilters({ ...filters });
    } catch (error) {
      console.error('Error in handleFilterToggle:', error);
    }
  };

  const applyFilters = (data, filterSet) => {
    return data.filter((row) =>
      Object.keys(filterSet).every((col) =>
        filterSet[ col ]?.length > 0 ? filterSet[ col ].includes(row[ col ]) : true
      )
    );
  };

  const handleApplyFilter = () => {
    const newFilters = { ...filters, [ filterPopup ]: tempFilters[ filterPopup ] || [] };
    setFilters(newFilters);
    const filteredResult = applyFilters(mappedOrders, newFilters);
    setFilteredData(filteredResult);
    setFilterPopup(null);
  };

  useEffect(() => {
    setFilteredData(applyFilters(mappedOrders, filters));
  }, [ mappedOrders, filters ]);

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) => selectedOptions.includes(opt));

    setTempFilters((prev) => ({
      ...prev,
      [ column ]: allSelected ? [] : [ ...currentOptions ],
    }));
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  const hasColumnData = (row, column) => {
    return row[ column ] !== undefined && row[ column ] !== "";
  };

  const tooltipStyle = {
    position: "absolute",
    backgroundColor: "#333",
    color: "#fff",
    padding: "5px 10px",
    borderRadius: "4px",
    fontSize: "12px",
    zIndex: 1000,
    maxWidth: "300px",
    whiteSpace: "pre-wrap",
  };

  const [ hoverData, setHoverData ] = useState(null);

  const handleMouseEnter = (order, event) => {
    setHoverData({
      content: order[ "Stock Symbol" ],
      x: event.clientX + 10,
      y: event.clientY + 10,
    });
  };

  const handleMouseLeave = () => {
    setHoverData(null);
  };

  const overlayStyle = {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.3)",
    zIndex: 999,
  };

  const modalStyle1 = {
    position: "fixed",
    top: "30%",
    left: "25%",
    transform: "translate(-50%, -50%)",
    backgroundColor: "#fff",
    padding: "20px",
    boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
    zIndex: 1000,
    borderRadius: "5px",
    width: "50%",
    maxHeight: "100%",
    textAlign: "center",
  };

  const buttonStyle = {
    backgroundColor: "#4661bd",
    color: "#fff",
    border: "none",
    padding: "10px 20px",
    borderRadius: "5px",
    marginTop: "0px",
    marginRight: "0px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    textAlign: "center",
    margin: "0 auto",
  };

  const { masterChildAccounts } = useSelector(
    (state) => state.masterChildAccountsReducer
  );
  const [ childOrders, setChildOrders ] = useState([]);
  const [ isLoadingModify, setIsLoadingModify ] = useState(false);
  const [ isLoadingCancel, setIsLoadingCancel ] = useState(false);
  const [ orderType, setOrderType ] = useState("LIMIT");
  const [ orderDetails, setOrderDetails ] = useState(null);
  const [ editModal, setEditModal ] = useState(false);
  const [ price, setPrice ] = useState(0);
  const [ quantity, setQuantity ] = useState(0);
  const [ error, setError ] = useState("");
  const [ dynamicLTP, setDynamicLTP ] = useState(0); // Separate state for dynamic LTP

  const fetchAccountDetails = async () => {
    try {
      const response = await fetchWithAuth(`/api/get_user_data/${mainUser}`, {
        method: "GET",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Something went wrong.");
      }

      const responseData = await response.json();
      const pseudoAccounts = responseData?.broker_credentials?.filter(
        (row) => row.broker === "pseudo_account"
      );

      if (!pseudoAccounts || pseudoAccounts.length === 0) {
        throw new Error("Pseudo account not found.");
      }

      const newFilledData = rows.map((account) => {
        const matchingPseudoAccount = pseudoAccounts.find(
          (pseudoAccount) => pseudoAccount.broker_user_id === account.userId
        );

        if (matchingPseudoAccount) {
          return {
            ...account,
            availableMargin: matchingPseudoAccount.available_balance ?? "0.00",
            utilized_Margin: matchingPseudoAccount.utilized_margin ?? "0.00",
          };
        }
        return account;
      });

      dispatch(setBrokers({ brokers: newFilledData }));
    } catch (error) {
      console.error("Error fetching account details:", error.message);
    }
  };

  const handleModifySelected = async () => {
    setIsLoadingModify(true);
    const strategy = orderDetails.Label;
    const userid = orderDetails[ "Client ID" ];
    const orderid = orderDetails[ "Trade ID" ];
    const exchange = orderDetails.Exchange;
    const LTP = orderDetails.LTP;

    const currentTime = new Date();

    if (!isWithinTradingWindow()) {
      return handleTimeWindowError(userid, strategy);
    }

    if (isNaN(parseInt(quantity)) || quantity <= 0) {
      handleMsg({
        msg: "Invalid quantity.",
        logType: "ERROR",
        timestamp: currentTime.toLocaleString(),
        user: userid,
        strategy,
      });
      setIsLoadingModify(false);
      return;
    }

    if (orderType === "LIMIT" && (isNaN(parseFloat(price)) || price <= 0)) {
      handleMsg({
        msg: "Invalid price for LIMIT order.",
        logType: "ERROR",
        timestamp: currentTime.toLocaleString(),
        user: userid,
        strategy,
      });
      setIsLoadingModify(false);
      return;
    }

    const sendRequest = async (url, data) => {
      try {
        const response = await fetchWithAuth(url, {
          method: "POST",
          body: JSON.stringify(data),
        });
        if (!response.ok) throw new Error("Network response was not ok");
        if (response.ok) {
          const SqoffResponse = await response.json();
          if (Array.isArray(SqoffResponse)) {
            SqoffResponse.forEach((responseObj) => {
              handleMsg({
                msg: responseObj.message,
                logType: "MESSAGE",
                timestamp: `${new Date().toLocaleString()}`,
                user: userid,
              });
              fetchAccountDetails();
            });
          } else {
            handleMsg({
              msg: SqoffResponse.message,
              logType: "MESSAGE",
              timestamp: `${new Date().toLocaleString()}`,
              user: userid,
            });
          }
        }
      } catch (error) {
        console.error("Fetch error:", error);
        handleMsg({
          msg: "Failed to modify orders. Please try again.",
          logType: "ERROR",
          timestamp: currentTime.toLocaleString(),
          user: userid,
          strategy,
        });
      } finally {
        setIsLoadingModify(false);
        closeModalDelete();
      }
    };

    const isMasterChild = /^\d+$/.test(strategy);

    if (isMasterChild) {
      const masterId = strategy.slice(6);
      const childOrderIds = getChildOrderIds(strategy, userid, orderid);
      const firstOrderId = childOrderIds[ 0 ];
      const order = mappedOrders.find(o => o[ "Trade ID" ] === firstOrderId);
      const childBrokerUserId = order ? order[ "Client ID" ] : null;


      if (childOrderIds.length === 0) {
        handleMsg({
          msg: "No orders found to modify.",
          logType: "ERROR",
          timestamp: currentTime.toLocaleString(),
          user: userid,
          strategy,
        });
        setIsLoadingModify(false);
        return;
      }

      if (orderType === "LIMIT") {
        const dataToUpdate = {
          order_ids: childOrderIds,
          new_price: parseFloat(price),
          new_quantity: parseInt(quantity, 10),
          child_broker_user_id: childBrokerUserId
        };
        await sendRequest(
          `/api/modify_mc_orders/${mainUser}/${masterId}`,
          dataToUpdate,
        );
      } else {
        const dataToUpdate = {
          order_ids: childOrderIds,
          current_ltp: LTP,
          new_quantity: parseInt(quantity, 10),
          exchange,
        };
        await sendRequest(
          `/api/execute_at_market_mc_orders/${mainUser}/${masterId}`,
          dataToUpdate,
        );
      }
    } else {
      if (orderType === "LIMIT") {
        const dataToUpdate = {
          new_price: parseFloat(price),
          new_quantity: parseInt(quantity, 10),
          exchange,
        };
        await sendRequest(
          `/api/modify_portfolio_orders/${mainUser}/${orderid}`,
          dataToUpdate,
        );
      } else {
        const dataToUpdate = {
          new_quantity: parseInt(quantity, 10),
          current_ltp: LTP,
          exchange,
        };
        await sendRequest(
          `/api/execute_at_market_orders/${mainUser}/${orderid}`,
          dataToUpdate,
        );
      }
    }
  };

  const cancelOrders = async () => {
    setIsLoadingCancel(true);

    const { Label: strategy, "Client ID": userId, "Trade ID": orderId, Exchange: exchange } =
      orderDetails;

    try {
      if (!isWithinTradingWindow()) {
        return handleTimeWindowError(userId, strategy);
      }

      const isMasterChild = /^\d+$/.test(strategy);
      let responseData;

      if (isMasterChild) {
        const masterId = strategy.slice(6);
        const childOrderIds = getChildOrderIds(strategy, userId, orderId);
        responseData = await apiService.cancelMasterChildOrders(masterId, childOrderIds);
      } else {
        responseData = await apiService.cancelSingleOrder(orderId, exchange);
      }

      handleMsg({
        msg: responseData.message || "Orders cancelled successfully",
        logType: "MESSAGE",
        timestamp: new Date().toLocaleString(),
        user: userId,
        strategy,
        color: isMasterChild ? undefined : "red"
      });
      fetchAccountDetails();
      closeModalDelete();
    } catch (error) {
      handleError(error);
    } finally {
      setIsLoadingCancel(false);
    }
  };

  const isWithinTradingWindow = () => {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();

    return (
      (hours === 9 && minutes >= 15) ||
      (hours > 9 && hours < 15) ||
      (hours === 15 && minutes <= 30)
    );
  };

  const handleTimeWindowError = (userId, strategy) => {
    handleMsg({
      msg: "Order not placed as current time is outside the allowed trading window.",
      logType: "INFO",
      timestamp: new Date().toLocaleString(),
      user: userId,
      strategy,
    });
    setEditModal(false);
  };

  const apiService = {
    async cancelMasterChildOrders(masterId, orderIds) {
      const firstOrderId = orderIds[ 0 ];
      const order = mappedOrders.find(o => o[ "Trade ID" ] === firstOrderId);
      const childBrokerUserId = order ? order[ "Client ID" ] : null;

      const response = await fetchWithAuth(`/api/cancel_mc_orders/${mainUser}/${masterId}`, {
        method: "POST",
        body: JSON.stringify({
          order_ids: orderIds,
          child_broker_user_id: childBrokerUserId
        }),
      });
      return this.handleApiResponse(response);
    },

    async cancelSingleOrder(orderId, exchange) {
      const response = await fetchWithAuth(`/api/cancel_portfolio_orders/${mainUser}/${orderId}`, {
        method: "POST",
        body: JSON.stringify({ exchange }),
      });
      return this.handleApiResponse(response);
    },

    async handleApiResponse(response) {
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(errorData.message || "API request failed");
      }

      const data = await response.json();
      if (Array.isArray(data)) {
        return {
          message: data.map(item => item.message).join(", "),
          success: true
        };
      }

      return data;
    }
  };

  const getChildOrderIds = (strategy, userId, orderId) => {
    const masterId = strategy.slice(6);
    const masterChildDetails = masterChildAccounts.find((mc) => mc.id === parseInt(masterId, 10));
    const childOrderIds = [];
    if (masterChildDetails && userId === masterChildDetails[ "broker_user_id" ]) {
      childOrderIds.push(orderId);
      masterChildDetails.child_accounts.forEach((childAcc) => {
        mappedOrders.forEach((order) => {
          if (
            order[ "Client ID" ] === childAcc.broker_user_id &&
            order[ "Label" ] === strategy
          ) {
            childOrderIds.push(order[ "Trade ID" ]);
          }
        });
      });
    } else {
      childOrderIds.push(orderId);
    }

    return childOrderIds;
  };

  const openEditModal = (order) => {
    if (!order || typeof order !== 'object') {
      return;
    }

    setOrderDetails(order);
    setPrice(order[ "Price" ] || 0);
    setQuantity(order[ "Execution Quantity" ] || 0);
    setDynamicLTP(order[ "LTP" ] || 0);
    setEditModal(true);

    const { "Client ID": userid, Label: strategy } = order;

    if (!strategy) {
      setChildOrders([]);
      return;
    }

    const isMasterChild = /^\d+$/.test(strategy);

    if (!isMasterChild) {
      setChildOrders([]);
      return;
    }

    const masterId = strategy.slice(6);

    if (!masterChildAccounts || !Array.isArray(masterChildAccounts)) {
      setChildOrders([]);
      return;
    }

    const masterChildDetails = masterChildAccounts.find((mc) => mc.id === Number(masterId));

    if (!masterChildDetails || userid !== masterChildDetails[ "broker_user_id" ]) {
      setChildOrders([]);
      return;
    }

    if (!masterChildDetails.child_accounts || !Array.isArray(masterChildDetails.child_accounts)) {
      setChildOrders([]);
      return;
    }

    const childUserIds = masterChildDetails.child_accounts.map(
      (childAcc) => childAcc.broker_user_id
    );

    if (!mappedOrders || !Array.isArray(mappedOrders)) {
      setChildOrders([]);
      return;
    }

    const fetchedChildOrders = mappedOrders.filter(
      (order) => childUserIds.includes(order[ "Client ID" ]) && order[ "Label" ] === strategy
    );

    setChildOrders(fetchedChildOrders);
  };

  const closeModalDelete = () => {
    setEditModal(false);
    setOrderDetails(null);
  };

  useEffect(() => {
    if (editModal && orderDetails) {
      const latestOrder = mappedOrders.find(
        (order) => order[ "Trade ID" ] === orderDetails[ "Trade ID" ]
      );
      if (latestOrder) {
        setDynamicLTP(latestOrder[ "LTP" ] || 0);
      }
    }
  }, [ mappedOrders, editModal, orderDetails ]);

  const handlePriceChange = (event) => {
    setPrice(event.target.value);
  };

  const handleQtyChange = (event) => {
    const qty = event.target.value;
    setQuantity(qty);
    validateQuantity(qty);
  };

  const validateQuantity = (qty) => {
    const symbol = orderDetails[ "Stock Symbol" ];
    let errorMessage = "";

    if ((symbol.includes("NIFTY") && qty % 75 !== 0) || (qty == 0 && symbol.includes("NIFTY"))) {
      errorMessage = "Quantity should be a multiple of 75 for NIFTY";
    } else if (
      (symbol.includes("BANKNIFTY") && qty % 30 !== 0) ||
      (qty == 0 && symbol.includes("BANKNIFTY"))
    ) {
      errorMessage = "Quantity should be a multiple of 30 for BANKNIFTY";
    } else if (
      (symbol.includes("FINNIFTY") && qty % 25 !== 0) ||
      (qty == 0 && symbol.includes("FINNIFTY"))
    ) {
      errorMessage = "Quantity should be a multiple of 25 for FINNIFTY";
    }

    setError(errorMessage);
    return errorMessage === "";
  };

  const handleInputChange = (e) => {
    setOrderType(e.target.value);
  };

  const handleError = (error) => {
    const { Label: strategy, "Client ID": userId } = orderDetails;
    handleMsg({
      msg: error.message || "An error occurred",
      logType: "ERROR",
      timestamp: new Date().toLocaleString(),
      user: userId,
      strategy,
    });
    console.error("Error in OrderFlow:", error);
  };

  return (
    <div>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav
            pageCols={orderFlowCols}
            colsSelectedAll={orderFlowColsSelectedALL}
            setColsSelectedALL={setOrderFlowColsSelectedALL}
            selectAll={orderFlowColSelectALL}
            colVis={orderFlowColVis}
            setColVis={setOrderFlowColVis}
            setSeq={setOrderFlowSeq}
          />
          <div className="main-table" style={{ overflow: "auto", height: "calc(92vh - 100px)" }}>
            <table className="orderflowtable" style={{ tableLayout: "auto", width: "100%", borderCollapse: "separate", borderSpacing: "0" }}>
              <thead style={{ position: "sticky", top: 0, zIndex: 10, backgroundColor: "#D8E1FF" }}>
                <tr>
                  {orderFlowSeq.map((column) => {
                    const hasFilter = filters[ column ] && filters[ column ].length > 0;
                    const selectedItems = filters[ column ]?.length || 0;

                    return (
                      <th
                        key={column}
                        style={{
                          fontSize: "13px",
                          padding: "4px 3px",
                          textAlign: "center",
                          backgroundColor: hasFilter ? "#f0f7ff" : "inherit",
                          borderBottom: hasFilter ? "2px solid #1976d2" : "inherit",
                          height: "auto",
                          whiteSpace: "normal",
                          verticalAlign: "middle",
                          lineHeight: "1.1",
                          writingMode: "horizontal-tb !important",
                          textOrientation: "mixed !important",
                          minWidth: (() => {
                            // Base width calculation
                            let baseWidth = column === "Avg Execution Price" ? 150 :
                              column === "Execution Quantity" ? 130 :
                                column === "Product Type" ? 110 :
                                  column === "Trade Status" ? 100 : 130;

                            // Add extra width for filter badge when filter is applied
                            const filterExtraWidth = hasFilter ? 30 : 0;

                            return `${baseWidth + filterExtraWidth}px`;
                          })(),
                          wordBreak: "break-word",
                          hyphens: "auto"
                        }}
                      >
                        <div style={{
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "center",
                          position: "relative",
                          padding: "0",
                          margin: "0",
                          gap: "4px",
                          width: "100%"
                        }}>
                          <TableHeaderWithFilter
                            col={column}
                            columnDisplayNames={{}}
                            hasFilter={hasFilter}
                            selectedItems={selectedItems}
                            handleFilterToggle={handleFilterToggle}
                            filterIcon={filterIcon}
                          />
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody className="tabletbody">
                {filteredData?.map((order, rowIndex) => (
                  <tr
                    key={rowIndex}
                    style={{
                      textAlign: "center",
                      backgroundColor: rowIndex % 2 === 0 ? "#FFFFFF" : "#E8E6E6"
                    }}
                  >
                    {orderFlowSeq.map((column) =>
                      hasColumnData(order, column) ? (
                        <td
                          style={{
                            padding: "4px",
                            textAlign: column === "Edit" ? "center" : "center",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "200px",
                            verticalAlign: "middle",
                            width: "auto"
                          }}
                          key={column}
                          {...(column === "Stock Symbol"
                            ? {
                              onMouseEnter: (e) => handleMouseEnter(order, e),
                              onMouseLeave: handleMouseLeave,
                            }
                            : {})}
                        >
                          {column === "Edit" ? (
                            orderFlowColVis[ "Edit" ] &&
                              order[ "Trade Status" ]?.toLowerCase() === "open" ? (
                              <span className="tooltip-container">
                                <EditIcon
                                  style={{
                                    fontSize: "20px",
                                    cursor: "pointer",
                                    color: "black",
                                  }}
                                  onClick={() => openEditModal(order)}
                                />
                              </span>
                            ) : null
                          ) : (
                            order[ column ]
                          )}
                        </td>
                      ) : (
                        <td style={{ padding: "4px", width: "auto", verticalAlign: "middle" }} key={column}></td>
                      )
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {hoverData && (
            <div
              style={{
                ...tooltipStyle,
                top: hoverData.y,
                left: hoverData.x,
              }}
            >
              {hoverData.content}
            </div>
          )}
          <div className="add_collapse">
            <button className="hiddenbutton button">Add</button>
            <button
              style={{ zIndex: "0" }}
              onClick={() => errorContainerRef.current.toggleCollapse()}
              className="button"
              id="collapse"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>
          <OptimizedErrorContainer
            ref={errorContainerRef}
            msgs={msgs}
            handleClearLogs={handleClearLogs}
          />
        </div>
        <OptimizedRightNav />
      </div>

      {editModal && orderDetails && (
        <>
          <div style={overlayStyle}></div>
          <Draggable>
            <div style={modalStyle1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  background: "#fff",
                  boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
                  borderRadius: "8px",
                }}
              >
                <h3 style={{ color: "#2E8B57" }}>
                  Present LTP is:{" "}
                  <span style={{ color: "#FF6347" }}>{dynamicLTP}</span>
                </h3>
                <h2 style={{ color: "#00008B" }}>Modify Order(s)</h2>
              </div>

              <table
                onMouseDown={(e) => e.stopPropagation()}
                style={{ width: "100%", marginBottom: "20px", overflowY: "auto" }}
              >
                <thead style={{ backgroundColor: "rgb(216, 225, 255)" }}>
                  <tr>
                    <th>Symbol</th>
                    <th>Order Type</th>
                    <th>UserID</th>
                    <th>Qty</th>
                    <th>Status</th>
                    <th>Order ID</th>
                    <th>Limit Price</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>{orderDetails[ "Stock Symbol" ]}</td>
                    <td>{orderDetails[ "Trade Type" ]}</td>
                    <td>{orderDetails[ "Client ID" ]}</td>
                    <td>{orderDetails[ "Execution Quantity" ]}</td>
                    <td>{orderDetails[ "Trade Status" ]}</td>
                    <td>{orderDetails[ "Trade ID" ]}</td>
                    <td>{orderDetails[ "Price" ]}</td>
                  </tr>
                  {childOrders && childOrders.length > 0 && (
                    <tr>
                      <td
                        colSpan="7"
                        style={{ textAlign: "center", fontWeight: "bold" }}
                      >
                        Child Orders
                      </td>
                    </tr>
                  )}
                  {childOrders?.map((childOrder, index) => (
                    <tr key={index}>
                      <td>{childOrder[ "Stock Symbol" ]}</td>
                      <td>{childOrder[ "Trade Type" ]}</td>
                      <td>{childOrder[ "Client ID" ]}</td>
                      <td>{childOrder[ "Execution Quantity" ]}</td>
                      <td>{childOrder[ "Trade Status" ]}</td>
                      <td>{childOrder[ "Trade ID" ]}</td>
                      <td>{childOrder[ "Price" ]}</td>
                    </tr>
                  ))}
                </tbody>
              </table>

              <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                <div
                  onMouseDown={(e) => e.stopPropagation()}
                  style={{ display: "flex", alignItems: "center", gap: "50px" }}
                >
                  <label>
                    Modify Price:
                    <input
                      type="text"
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                      }}
                      value={price}
                      style={{
                        width: "70px",
                        backgroundColor: "white",
                        border: `${price >= dynamicLTP ? "5px" : "1px"} solid ${price >= dynamicLTP ? "red" : "black"
                          }`,
                        borderRadius: "5px",
                        padding: "5px",
                        userSelect: "text",
                      }}
                      onChange={handlePriceChange}
                      disabled={orderType === "MARKET"}
                    />
                  </label>
                  <label>
                    Modify Quantity:
                    <input
                      type="text"
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                      }}
                      value={quantity}
                      style={{
                        width: "70px",
                        backgroundColor: "white",
                        border: `${error ? "4px" : "1px"} solid ${error ? "red" : "gray"}`,
                        borderRadius: "5px",
                        padding: "5px",
                        userSelect: "text",
                      }}
                      onChange={handleQtyChange}
                    />
                  </label>
                </div>
                <div
                  onMouseDown={(e) => e.stopPropagation()}
                  style={{ display: "flex", alignItems: "center", gap: "30px", marginLeft: "50px" }}
                >
                  <label>
                    <input
                      type="radio"
                      name="ordertype"
                      value="LIMIT"
                      onChange={handleInputChange}
                      checked={orderType === "LIMIT"}
                    />{" "}
                    LIMIT
                  </label>
                  <label>
                    <input
                      type="radio"
                      name="ordertype"
                      value="MARKET"
                      onChange={handleInputChange}
                      checked={orderType === "MARKET"}
                    />{" "}
                    MARKET
                  </label>
                </div>
              </div>

              <div>
                {error && <span style={{ color: "red" }}>{error}</span>}
                {price && price >= dynamicLTP && (
                  <span style={{ color: "red", fontWeight: "bold" }}>
                    Price must be less than LTP
                  </span>
                )}
              </div>

              <div
                onMouseDown={(e) => e.stopPropagation()}
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: "19px",
                  gap: "30px",
                }}
              >
                <button
                  style={{
                    ...buttonStyle,
                    width: "200px",
                    height: "40px",
                    backgroundColor:
                      price >= dynamicLTP || error || price === "" ? "#A6C8FF" : "#007BFF",
                    color: "#FFFFFF",
                    cursor:
                      price >= dynamicLTP || error || price === "" ? "not-allowed" : "pointer",
                  }}
                  onClick={handleModifySelected}
                  disabled={isLoadingModify || price >= dynamicLTP || error || price === ""}
                >
                  {isLoadingModify ? (
                    <Oval height="20" width="20" color="white" strokeWidth={3} />
                  ) : (
                    "OK"
                  )}
                </button>
                <button
                  style={{
                    ...buttonStyle,
                    width: "200px",
                    height: "40px",
                    backgroundColor: isLoadingCancel ? "#F8D7DA" : "#DC3545",
                    color: "#FFFFFF",
                    cursor: isLoadingCancel ? "not-allowed" : "pointer",
                  }}
                  onClick={cancelOrders}
                  disabled={isLoadingCancel}
                >
                  {isLoadingCancel ? (
                    <Oval height="20" width="20" color="white" strokeWidth={3} />
                  ) : (
                    "Cancel Order's"
                  )}
                </button>
                <button
                  style={{
                    ...buttonStyle,
                    width: "200px",
                    height: "40px",
                    backgroundColor: "#6C757D",
                    color: "#FFFFFF",
                    cursor: "pointer",
                  }}
                  onClick={closeModalDelete}
                >
                  Close
                </button>
              </div>
            </div>
          </Draggable>
        </>
      )}
      {filterPopup && filterPopup !== "action" && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "3px 0 3px 3px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            zIndex: 1000,
            minWidth: "150px",
            maxWidth: "200px"
          }}
        >
          <div style={{ paddingRight: "1px" }}>
            <div style={{
              borderBottom: "1px solid #e0e0e0",
              paddingBottom: "1px",
              marginBottom: "1px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            }}>
              <span style={{ fontWeight: "bold", color: "#1976d2" }}>
                Filter: {filterPopup}
              </span>
              <span style={{
                fontSize: "12px",
                color: "#666",
                backgroundColor: "#f5f5f5",
                padding: "2px 6px",
                borderRadius: "4px"
              }}>
                {getDynamicUniqueValues(filterPopup).length} items
              </span>
            </div>

            {getDynamicUniqueValues(filterPopup).length > 10 && (
              <div style={{ marginBottom: "1px", paddingRight: "1px" }}>
                <input
                  type="text"
                  placeholder="Search..."
                  style={{
                    // width: "100%",
                    maxWidth: "100px",
                    padding: "3px 3px",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                    fontSize: "12px"
                  }}
                />
              </div>
            )}

            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "1px 1px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "1px",
                backgroundColor: "#f8f9fa",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[ filterPopup ] &&
                  getDynamicUniqueValues(filterPopup).every((opt) =>
                    tempFilters[ filterPopup ].includes(opt)
                  )
                }
                onChange={() => handleSelectAll(filterPopup)}
                style={{ marginRight: "8px" }}
              />
              <span>Select All</span>
            </label>

            <div
              style={{
                maxHeight: "150px",
                overflowY: "auto",
                margin: "0",
                scrollbarWidth: "thin",
                scrollbarColor: "#888 #f1f1f1",
                border: "1px solid #eee",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              {getDynamicUniqueValues(filterPopup).length > 0 ? (
                getDynamicUniqueValues(filterPopup).map((item) => {
                  const isSelected = tempFilters[ filterPopup ]?.includes(item) || false;
                  return (
                    <div
                      key={item}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "1px 2px",
                        cursor: "pointer",
                        margin: "0",
                        borderBottom: "1px solid #f0f0f0",
                        backgroundColor: isSelected ? "#f0f7ff" : "transparent",
                        transition: "background-color 0.2s"
                      }}
                      onClick={() => handleFilterChange(filterPopup, item)}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => { }}
                        style={{ marginRight: "8px" }}
                      />
                      <span style={{
                        color: isSelected ? "#1976d2" : "#444",
                        fontWeight: isSelected ? "500" : "normal"
                      }}>
                        {item || "(Empty)"}
                      </span>
                    </div>
                  );
                })
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "10px",
                    color: "#666",
                    fontStyle: "italic",
                    minHeight: "40px",
                    justifyContent: "center"
                  }}
                >
                  <span>No options available</span>
                </div>
              )}
            </div>

            <div style={{
              fontSize: "12px",
              color: "#666",
              margin: "1px 0",
              display: "flex",
              justifyContent: "space-between",
              marginRight: "1px"
            }}>
              <span>
                {tempFilters[ filterPopup ]?.length || 0} of {getDynamicUniqueValues(filterPopup).length} selected
              </span>
              {tempFilters[ filterPopup ]?.length > 0 && (
                <span
                  style={{ color: "#1976d2", cursor: "pointer" }}
                  onClick={() => setTempFilters({ ...tempFilters, [ filterPopup ]: [] })}
                >
                  Clear selection
                </span>
              )}
            </div>
          </div>

          <div
            style={{
              marginTop: "3px",
              display: "flex",
              gap: "10px",
              justifyContent: "center",
              paddingRight: "3px"
            }}
          >
            <button
              onClick={handleCancelFilter}
              style={{
                padding: "3px 3px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                background: "#f8f9fa",
                cursor: "pointer",
                color: "#333",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilter}
              style={{
                padding: "3px 3px",
                border: "none",
                borderRadius: "4px",
                background: "#1976d2",
                color: "white",
                cursor: "pointer",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Apply Filter
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default OrderFlow;





