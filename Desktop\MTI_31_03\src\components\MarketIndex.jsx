import React, { useEffect, useState, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import { setProfileImage, setProfileImageLoading, clearProfileImage } from "../store/slices/profileImage";
import Cookies from "universal-cookie";
import { useNavigate } from "react-router-dom";
import "../styles.css";
import { fetchWithAuth } from "../utils/api";
import { useIdleTimeout } from "../hooks/useIdleTimeout";
import HeartbeatStatus from "./HeartbeatStatus";
import {
  Menu,
  MenuItem,
  IconButton,
  Divider,
  Box,
  Typography,
  Dialog,
  DialogActions,
  Button,
  Slider, // Use @mui/material/Slider
} from "@mui/material";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import EmailIcon from "@mui/icons-material/Email";
import LockIcon from "@mui/icons-material/Lock";
import LogoutIcon from "@mui/icons-material/Logout";
import EditIcon from "@mui/icons-material/Edit";
import Cropper from "react-easy-crop";

function MarketIndex() {
  const navigate = useNavigate();
  const cookies = new Cookies();
  const mainUser = cookies.get("USERNAME");
  const dispatch = useDispatch();

  const inactivityTimeout = 30 * 60 * 1000;

  // Set up optimized idle timeout with heartbeat integration
  const idleTimeout = useIdleTimeout({
    timeout: inactivityTimeout,
    onIdle: () => {
      console.log('User became idle, logging out...');
      logout();
    },
    onWarning: (data) => {
      console.log('Idle warning:', data);
      // You can show a warning modal here if needed
    },
    warningTime: 5 * 60 * 1000, // 5 minutes warning
    enabled: true,
  });

  const { profileImage, isLoading: isLoadingImage } = useSelector((state) => state.profileImageReducer);
  const [ openPopup, setOpenPopup ] = useState(false);
  const [ selectedImage, setSelectedImage ] = useState(null);
  const [ crop, setCrop ] = useState({ x: 0, y: 0 });
  const [ zoom, setZoom ] = useState(1);
  const [ croppedAreaPixels, setCroppedAreaPixels ] = useState(null);
  const fileInputRef = useRef(null);
  const menuAnchorRef = useRef(null);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const cachedImage = localStorage.getItem("PROFILE_IMAGE");
        if (cachedImage && cachedImage !== "null" && cachedImage !== "undefined") {
          dispatch(setProfileImage({ profileImage: cachedImage }));
          return;
        }
        if (!isLoadingImage) {
          dispatch(setProfileImageLoading({ isLoading: true }));

          const response = await fetchWithAuth(`/api/get_image/${mainUser}`, {
            method: "GET",
          });

          if (response.ok) {
            const imageData = await response.json();
            if (imageData.image) {
              const base64Image = `data:image/jpeg;base64,${imageData.image}`;
              dispatch(setProfileImage({ profileImage: base64Image }));
              localStorage.setItem("PROFILE_IMAGE", base64Image);
            } else {
              // Set null explicitly in localStorage to avoid repeated fetches
              localStorage.setItem("PROFILE_IMAGE", "null");
              dispatch(setProfileImage({ profileImage: null }));
            }
          } else {
            console.error("Failed to fetch image, using default.");
            localStorage.setItem("PROFILE_IMAGE", "null");
            dispatch(setProfileImage({ profileImage: null }));
          }

          dispatch(setProfileImageLoading({ isLoading: false }));
        }
      } catch (error) {
        console.error("Error fetching image:", error);
        localStorage.setItem("PROFILE_IMAGE", "null");
        dispatch(setProfileImage({ profileImage: null }));
        dispatch(setProfileImageLoading({ isLoading: false }));
      }
    };

    // Only fetch if we don't already have a profile image
    if (!profileImage) {
      fetchImage();
    }
  }, [ mainUser, profileImage, isLoadingImage, dispatch ]);

  const logout = async () => {
    try {
      const response = await fetchWithAuth(`/api/app_logout`, {
        method: "POST",
        body: JSON.stringify({ username: mainUser }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to log out.");
      }

      [ "TOKEN", "USERNAME", "session_id", "PROFILE_IMAGE" ].forEach((cookie) =>
        cookies.remove(cookie, { path: "/" })
      );

      // Clear profile image from Redux store
      dispatch(clearProfileImage());
      localStorage.removeItem("PROFILE_IMAGE");

      navigate("/");
      window.location.reload();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Old inactivity system replaced by useIdleTimeout hook above

  const marketData = useSelector((state) => state.marketReducer);

  const handleMsg = useCallback(
    (Msg) => {
      dispatch((dispatch, getState) => {
        const { consoleMsgs } = getState().consoleMsgsReducer;
        const lastMsg = consoleMsgs[ 0 ];
        const isDuplicate =
          lastMsg &&
          lastMsg.msg === Msg.msg &&
          lastMsg.user === Msg.user &&
          lastMsg.strategy === Msg.strategy &&
          lastMsg.portfolio === Msg.portfolio;

        dispatch(
          setConsoleMsgs({
            consoleMsgs: isDuplicate
              ? [ Msg, ...consoleMsgs.slice(1) ]
              : [ Msg, ...consoleMsgs ],
          })
        );
      });
    },
    [ dispatch ]
  );

  // useEffect(() => {
  //   const eventSource = new EventSource(
  //     `http://106.51.129.99:80/tradingview_events/${mainUser}`,
  //     {
  //       headers: {
  //         Authorization: `Bearer ${token}`,
  //       },
  //     }
  //   );

  //   eventSource.onmessage = (event) => {
  //     try {
  //       const responseData = JSON.parse(event.data);

  //       if (typeof responseData === "object" && responseData.mtitest12) {
  //         Object.keys(responseData).forEach((key) => {
  //           const userData = responseData[ key ][ 0 ];
  //           const portfolioName = userData.portfolio_name;
  //           const brokerResponses = userData.broker_responses;

  //           if (brokerResponses.message) {
  //             handleMsg({
  //               msg: brokerResponses.message,
  //               logType: "TRADING",
  //               timestamp: `${new Date().toLocaleString()}`,
  //               user: key,
  //               strategy: "TradingView",
  //               portfolio: portfolioName,
  //             });
  //           } else {
  //             for (const broker in brokerResponses) {
  //               const brokerArray = brokerResponses[ broker ];
  //               brokerArray.forEach((entry) => {
  //                 const messages = entry.message.messages;
  //                 messages.forEach((messageObj) => {
  //                   const messageText = messageObj.message;
  //                   handleMsg({
  //                     msg: messageText,
  //                     logType: "TRADING",
  //                     timestamp: `${new Date().toLocaleString()}`,
  //                     user: broker,
  //                     strategy: "TradingView",
  //                     portfolio: portfolioName,
  //                   });
  //                 });
  //               });
  //             }
  //           }
  //         });
  //       } else if (Array.isArray(responseData)) {
  //         responseData.forEach((userData) => {
  //           const portfolioName = userData.portfolio_name;
  //           const brokerResponses = userData.broker_responses;

  //           for (const broker in brokerResponses) {
  //             const brokerArray = brokerResponses[ broker ];
  //             brokerArray.forEach((entry) => {
  //               const messages = entry.message.messages;
  //               messages.forEach((messageObj) => {
  //                 const messageText = messageObj.message;
  //                 handleMsg({
  //                   msg: messageText,
  //                   logType: "TRADING",
  //                   timestamp: `${new Date().toLocaleString()}`,
  //                   strategy: "TradingView",
  //                   portfolio: portfolioName,
  //                 });
  //               });
  //             });
  //           }
  //         });
  //       } else {
  //         console.error("Unrecognized data format");
  //       }
  //     } catch (error) {
  //       console.error("Error parsing event data:", error);
  //     }
  //   };

  //   eventSource.onerror = () => {
  //     console.error("EventSource failed. Closing connection.");
  //     eventSource.close();
  //   };

  //   return () => {
  //     eventSource.close();
  //   };
  // }, [ token, handleMsg ]);

  const indices = [ "sensex", "nifty50", "niftybank", "finnifty" ];

  const [ anchorEl, setAnchorEl ] = useState(null);
  const [ timeoutId, setTimeoutId ] = useState(null);
  const open = Boolean(anchorEl);

  const handleMenuOpen = (event) => {
    if (timeoutId) clearTimeout(timeoutId);
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    const id = setTimeout(() => {
      setAnchorEl(null);
    }, 200);
    setTimeoutId(id);
  };

  const handleNavigation = (path) => {
    navigate(path);
    setAnchorEl(null);
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[ 0 ];
    if (file) {
      if (!file.type.match("image.*")) {
        console.error("Please upload a valid image file");
        return;
      }
      const reader = new FileReader();
      reader.onloadend = () => {
        setSelectedImage(reader.result);
        console.log("Selected image loaded:", reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
    console.log("Cropped area pixels:", croppedAreaPixels);
  }, []);

  const getCroppedImg = async (imageSrc, pixelCrop) => {
    const image = new Image();
    image.src = imageSrc;
    await new Promise((resolve) => (image.onload = resolve));

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    return new Promise((resolve) => {
      const croppedDataUrl = canvas.toDataURL("image/jpeg");
      console.log("Cropped image generated:", croppedDataUrl);
      resolve(croppedDataUrl);
    });
  };

  const handleSaveCroppedImage = async () => {
    try {
      if (!selectedImage || !croppedAreaPixels) {
        console.error("No image or crop area available");
        return;
      }

      const croppedImg = await getCroppedImg(selectedImage, croppedAreaPixels);
      if (!croppedImg) {
        console.error("Failed to generate cropped image");
        return;
      }

      const base64String = croppedImg.split(",")[ 1 ];
      if (!base64String) {
        console.error("Failed to extract base64 string from cropped image");
        return;
      }

      console.log("Base64 string to upload:", base64String);

      const response = await fetchWithAuth(`/api/upload_image/${mainUser}`, {
        method: "POST",
        body: JSON.stringify({ image: base64String }),
      });

      if (response.ok) {
        console.log("Image uploaded successfully, setting profile image:", croppedImg);
        dispatch(setProfileImage({ profileImage: croppedImg }));
        localStorage.setItem("PROFILE_IMAGE", croppedImg);
        setSelectedImage(null);
        setOpenPopup(false);
      } else {
        const errorData = await response.json();
        console.error("Failed to upload image:", errorData);
      }
    } catch (error) {
      console.error("Error in handleSaveCroppedImage:", error);
    }
  };

  const handleRemoveImage = async () => {
    try {
      const response = await fetchWithAuth(`/api/delete_image/${mainUser}`, {
        method: "POST",
      });

      if (response.ok) {
        dispatch(clearProfileImage());
        localStorage.removeItem("PROFILE_IMAGE");
      } else {
        console.error("Failed to remove image");
      }
    } catch (error) {
      console.error("Error removing image:", error);
    }
    setOpenPopup(false);
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleEditClick = () => {
    setOpenPopup(true);
  };

  return (
    <nav className="navbar">
      <div className="sensex-container">
        {indices.map((index) => (
          <div key={index} style={{ width: "290px", marginBottom: "10px" }}>
            <span className="sensex-one">
              {index.toUpperCase()}{" "}
              <span>{marketData?.marketData?.[ index ]?.c}</span>
            </span>
            <span className="sensex-two">
              {marketData?.marketData?.[ index ]?.ch && (
                <span
                  style={{
                    color:
                      marketData?.marketData?.[ index ]?.ch < 0 ? "red" : "green",
                    fontWeight: "bold",
                  }}
                >
                  {marketData?.marketData?.[ index ]?.ch < 0 ? (
                    <span>▼</span>
                  ) : (
                    <span>▲</span>
                  )}{" "}
                  {marketData?.marketData?.[ index ]?.ch} (
                  {marketData?.marketData?.[ index ]?.chp}%)
                </span>
              )}
            </span>
          </div>
        ))}
      </div>

      {/* Heartbeat Status Indicator */}
      <div className="heartbeat-indicator" style={{ marginRight: '10px' }}>
        <HeartbeatStatus showDetails={false} />
      </div>

      <div className="options-div">
        <Box>
          <IconButton
            ref={menuAnchorRef}
            sx={{ padding: 0 }}
            aria-controls={open ? "user-menu" : undefined}
            aria-haspopup="true"
            aria-expanded={open ? "true" : undefined}
            onClick={handleMenuOpen}
          >
            {profileImage ? (
              <img
                src={profileImage}
                alt="Profile"
                style={{ width: 50, height: 50, borderRadius: "50%", border: "1px solid #2071B2" }}
              />
            ) : (
              <AccountCircleIcon sx={{ color: "#2071B2", fontSize: 50 }} />
            )}
          </IconButton>

          <Menu
            id="user-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            disableAutoFocusItem={true}
            disableRestoreFocus={true}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            PaperProps={{
              sx: {
                borderRadius: "8px",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                minWidth: "200px",
              },
            }}
          >
            <MenuItem sx={{ padding: "4px 8px", position: "relative" }}>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                width="100%"
              >
                <Typography variant="body1">{mainUser}</Typography>
                <Box sx={{ position: "relative" }}>
                  {profileImage ? (
                    <Box sx={{ position: "relative", cursor: "pointer" }} onClick={handleEditClick}>
                      <img
                        src={profileImage}
                        alt="Profile"
                        style={{ width: 30, height: 30, borderRadius: "50%", border: "1px solid #2071B2" }}
                      />
                      <Box
                        sx={{
                          position: "absolute",
                          right: -3,
                          bottom: 0,
                          width: 16,
                          height: 16,
                          borderRadius: "50%",
                          backgroundColor: "#2071B2",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          border: "1px solid white",
                          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.2)",
                          pointerEvents: "none",
                        }}
                      >
                        <EditIcon sx={{ fontSize: 10, color: "white", border: "1px solid #2071B2" }} />
                      </Box>
                    </Box>
                  ) : (
                    <Box sx={{ position: "relative", cursor: "pointer" }} onClick={handleEditClick}>
                      <AccountCircleIcon sx={{ fontSize: 30, color: "#2071B2" }} />
                      <Box
                        sx={{
                          position: "absolute",
                          bottom: 5,
                          right: -3,
                          width: 16,
                          height: 16,
                          borderRadius: "50%",
                          backgroundColor: "white",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          border: "1px solid white",
                          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.2)",
                          pointerEvents: "none",
                        }}
                      >
                        <EditIcon sx={{ fontSize: 10, color: "black" }} />
                      </Box>
                    </Box>
                  )}
                </Box>
              </Box>
            </MenuItem>

            <Divider />

            <MenuItem
              onClick={() => handleNavigation("/Change_Password")}
              sx={{ padding: "4px 8px" }}
            >
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                width="100%"
              >
                <Typography variant="body1">Change Password</Typography>
                <LockIcon sx={{ fontSize: 30, color: "#2071B2" }} />
              </Box>
            </MenuItem>

            <MenuItem
              onClick={() => handleNavigation("/Subscription")}
              sx={{ padding: "4px 8px" }}
            >
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                width="100%"
              >
                <Typography variant="body1">Subscription</Typography>
                <EmailIcon sx={{ fontSize: 30, color: "#2071B2" }} />
              </Box>
            </MenuItem>

            <MenuItem onClick={logout} sx={{ padding: "4px 8px" }}>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                width="100%"
              >
                <Typography variant="body1">Logout</Typography>
                <LogoutIcon sx={{ fontSize: 30, color: "#2071B2" }} />
              </Box>
            </MenuItem>
          </Menu>

          <Dialog open={openPopup} onClose={() => setOpenPopup(false)}>
            <Box sx={{ padding: 2, textAlign: "center" }}>
              {selectedImage ? (
                <div style={{ position: "relative", width: "300px", height: "300px" }}>
                  <Cropper
                    image={selectedImage}
                    crop={crop}
                    zoom={zoom}
                    aspect={1}
                    cropShape="round"
                    showGrid={false}
                    onCropChange={setCrop}
                    onZoomChange={setZoom}
                    onCropComplete={onCropComplete}
                  />
                  <div style={{ marginTop: "20px" }}>
                    <Typography>Zoom</Typography>
                    <Slider
                      value={zoom}
                      min={1}
                      max={3}
                      step={0.1}
                      onChange={(e, newValue) => setZoom(newValue)}
                      sx={{ width: 200 }}
                    />
                  </div>
                </div>
              ) : profileImage ? (
                <img
                  src={profileImage}
                  alt="Profile Preview"
                  style={{ width: 200, height: 200, borderRadius: "50%", marginBottom: 2, border: "1px solid #2071B2" }}
                />
              ) : (
                <AccountCircleIcon sx={{ fontSize: 100, color: "#2071B2", marginBottom: 2 }} />
              )}
            </Box>
            <DialogActions sx={{ justifyContent: "center" }}>
              {selectedImage ? (
                <>
                  <Button
                    onClick={handleSaveCroppedImage}
                    variant="contained"
                    color="primary"
                  >
                    Save
                  </Button>
                  <Button
                    onClick={() => setSelectedImage(null)}
                    variant="contained"
                    color="secondary"
                  >
                    Cancel
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    onClick={triggerFileInput}
                    variant="contained"
                    color="primary"
                    startIcon={<EditIcon />}
                  >
                    Change
                  </Button>
                  <Button
                    onClick={handleRemoveImage}
                    variant="contained"
                    color="secondary"
                    startIcon={<AccountCircleIcon />}
                  >
                    Remove
                  </Button>
                </>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                style={{ display: "none" }}
                tabIndex={-1}
              />
            </DialogActions>
          </Dialog>
        </Box>
      </div>
    </nav>
  );
}

export default MarketIndex;
