import React, { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  OptimizedMarketIndex,
  OptimizedTopNav,
  OptimizedLeftNav,
  OptimizedErrorContainer,
  OptimizedRightNav,
} from "../components/Layout/OptimizedComponenets";
import MarketOrdersModal from "../components/MarketOrdersModal";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import Log from "../assets/log.png";
import Logout from "../assets/logout.png";
import Cookies from "universal-cookie";
import filterIcon from "../assets/newFilter.png";
const cookies = new Cookies();
import { useSelector, useDispatch } from "react-redux";
import {
  setBrokers,
  setAllSeq,
  setAllVis,
  setConsoleMsgs,
} from "../store/slices";
import useClickOutside from "../hooks/useClickOutside";
import useDebounce from '../hooks/useDebounce';
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import Modal from "react-modal";
import TableHeaderWithFilter from "../components/TableHeaderWithFilter";
import TimePicker from "react-time-picker";
import {
  Stop as StopIcon,
  PlayArrow as PlayArrowIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import "react-time-picker/dist/TimePicker.css";
import { fetchWithAuth } from "../utils/api";
import Draggable from 'react-draggable';


const styles = `
  .middle-main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .table-container {
    flex: 1;
    overflow: auto;
    height: calc(92vh - 100px);
    position: relative;
  }
  .user-profiles-table {
    border-collapse: separate;
    border-spacing: 0;
  }
  .user-profiles-table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #D8E1FF;
  }
  .user-profiles-table th {
    font-size: 15px;
    padding: 4px 3px; /* Reduced padding */
    text-align: center; /* Center-align headers */
    border-bottom: 1px solid #ddd;
    white-space: normal; /* Allow text wrapping */
    vertical-align: middle;
    height: auto;
    line-height: 1.1;
    writing-mode: horizontal-tb !important; /* Force horizontal text */
    text-orientation: mixed !important; /* Ensure horizontal text */
    min-width: 50px; /* Minimum width for narrow columns */
  }

  /* Special style for narrow columns to prevent vertical text */
  .user-profiles-table th[data-narrow-column] {
    min-width: 100px !important;
    width: 100px !important;
  }

  .user-profiles-table th[data-narrow-column] div {
    width: 90% !important;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    white-space: nowrap !important;
    word-break: normal !important;
  }
  .user-profiles-table td {
    text-align: center; /* Center-align cell content */
    vertical-align: middle;
  }
  /* Fixed column styles */
  .fixed-column-1 {
    position: sticky !important;
    left: 0 !important;
    z-index: 5 !important;
    box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1) !important;
  }

  .fixed-column-2 {
    position: sticky !important;
    left: 96px !important; /* Width of the first column */
    z-index: 5 !important;
    box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1) !important;
    min-width: 120px !important; /* Minimum width for Client ID column */
    width: 120px !important; /* Fixed width for Client ID column */
  }

  .fixed-column-3 {
    position: sticky !important;
    left: 216px !important; /* Width of the first column (96px) + second column (120px) */
    z-index: 5 !important;
    box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1) !important;
    min-width: 100px !important; /* Minimum width for MTM (All) column */
    width: 100px !important; /* Fixed width for MTM (All) column */
  }

  .fixed-column-header-1 {
    position: sticky !important;
    left: 0 !important;
    z-index: 15 !important;
    background-color: #D8E1FF !important;
  }

  .fixed-column-header-2 {
    position: sticky !important;
    left: 96px !important; /* Width of the first column */
    z-index: 15 !important;
    background-color: #D8E1FF !important;
    min-width: 120px !important; /* Minimum width for Client ID column */
    width: 120px !important; /* Fixed width for Client ID column */
    padding: 0 !important; /* Remove padding */
  }

  .fixed-column-header-3 {
    position: sticky !important;
    left: 216px !important; /* Width of the first column (96px) + second column (120px) */
    z-index: 15 !important;
    background-color: #D8E1FF !important;
    min-width: 100px !important; /* Minimum width for MTM (All) column */
    width: 100px !important; /* Fixed width for MTM (All) column */
  }

  /* Background colors for fixed columns to prevent see-through */
  .user-profiles-table tbody tr:nth-child(even) .fixed-column-1,
  .user-profiles-table tbody tr:nth-child(even) .fixed-column-2,
  .user-profiles-table tbody tr:nth-child(even) .fixed-column-3 {
    background-color: #E8E6E6 !important;
  }

  .user-profiles-table tbody tr:nth-child(odd) .fixed-column-1,
  .user-profiles-table tbody tr:nth-child(odd) .fixed-column-2,
  .user-profiles-table tbody tr:nth-child(odd) .fixed-column-3 {
    background-color: #FFFFFF !important;
  }
  .user-profiles-table tbody tr:nth-child(even),
  .user-profiles-table tbody tr:nth-child(even) input,
  .user-profiles-table tbody tr:nth-child(even) select {
    background-color: #E8E6E6;
  }
  .user-profiles-table tbody tr:nth-child(odd),
  .user-profiles-table tbody tr:nth-child(odd) input,
  .user-profiles-table tbody tr:nth-child(odd) select {
    background-color: #FFFFFF;
  }

  .tooltip-container {
    position: relative;
    display: inline-block;
    margin: 0 2px; /* Tighten spacing between icons */
  }
  .tooltiptext {
    visibility: hidden;
    width: 80px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 2px;
    position: absolute;
    z-index: 9999;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
  }
  .tooltip-container:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
    z-index: 9999;
  }
  .logout_icon {
    cursor: pointer;
  }
  .custom-time-picker {
    padding: 8px;
    width: 105px;
  }
  .custom-select {
    padding: 4px;
  }

  input[readonly] {
    background-color: #f0f0f0;
  }
  .action-icons {
    display: flex;
    justify-content: center; /* Center the icons */
    align-items: center;
    gap: 2px; /* Controlled spacing between icons */
  }
`;
export const inputStyles = {
  padding: '8px',
  borderRadius: '3px',
  width: 'auto',
  minWidth: '20px',
  maxWidth: '130px',
  boxSizing: 'border-box',
  fontSize: '14px',
  textAlign: 'center'
};

function UserProfiles() {
  const mainUser = cookies.get("USERNAME");

  const filterPopupRef = useRef(null);
  const dispatch = useDispatch();
  const tableRef = useRef(null);
  const popupRef = useRef(null);

  const { collapsed } = useSelector((state) => state.collapseReducer);
  const [ actionFilter, setActionFilter ] = useState("all");
  const [ isOpen, setIsOpen ] = useState(false);
  const [ brokerName, setBrokerName ] = useState([]);
  const [ isOpenModal, setIsOpenModal ] = useState(false);
  const [ hoverData, setHoverData ] = useState(null);

  const { brokers: rows } = useSelector((state) => state.brokerReducer);
  const isStarted = useSelector((state) => state.placeOrderStartReducer.placeOrderStart);

  const iconStyle = {
    border: "1px solid black",
    width: "25px",
    height: "25px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: "4px",
    marginRight: "5px",
  };

  const errorContainerRef = useRef(null);

  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;

      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.strategy &&
        lastMsg.portfolio === Msg.portfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };
  const openModal = () => {
    setIsOpen(true);
  };
  const closeModal = () => {
    setIsOpen(false);
  };

  const [ showPasswordRowsApi, setShowPasswordRowsApi ] = useState(
    Array(rows.length).fill(false)
  );
  const [ showPasswordRowsQr, setShowPasswordRowsQr ] = useState(
    Array(rows.length).fill(false)
  );
  const [ showPasswordRowsPassword, setShowPasswordRowsPassword ] = useState(
    Array(rows.length).fill(false)
  );
  const togglePasswordVisibilityForRowApi = (index) => {
    setShowPasswordRowsApi((prevState) => {
      const updatedState = [ ...prevState ];
      updatedState[ index ] = !updatedState[ index ];
      return updatedState;
    });
  };
  const togglePasswordVisibilityForRowQr = (index) => {
    setShowPasswordRowsQr((prevState) => {
      const updatedState = [ ...prevState ];
      updatedState[ index ] = !updatedState[ index ];
      return updatedState;
    });
  };
  const togglePasswordVisibilityForRowPassword = (index) => {
    setShowPasswordRowsPassword((prevState) => {
      const updatedState = [ ...prevState ];
      updatedState[ index ] = !updatedState[ index ];
      return updatedState;
    });
  };
  const [ showPasswordRowsSecretKey, setShowPasswordRowsSecretKey ] = useState(
    Array(rows.length).fill(false)
  );

  const togglePasswordVisibilityForRowSecretKey = (index) => {
    setShowPasswordRowsSecretKey((prevState) => {
      const updatedState = [ ...prevState ];
      updatedState[ index ] = !updatedState[ index ];
      return updatedState;
    });
  };


  const HandleBrokers = useCallback(async () => {
    try {
      const response = await fetchWithAuth(`api/broker_list`, {
        method: "GET",
      });
      if (response.ok) {

        const data = await response.json();
        setBrokerName(data.brokers);
        localStorage.setItem("brokerList", JSON.stringify(data.brokers));
      } else {
        console.error("Failed to fetch broker list");
      }
    } catch (error) {
      console.error("Error fetching broker list:", error);
    }
  }, []);

  useEffect(() => {
    const cachedBrokers = localStorage.getItem("brokerList");
    if (cachedBrokers) {
      setBrokerName(JSON.parse(cachedBrokers));
    } else {
      HandleBrokers();
    }
  }, [ HandleBrokers ]);

  const userProfPageCols = [
    "action",
    "userId",
    "mtmAll",
    "manualExit",
    "availableMargin",
    "name",
    "broker",
    "apiKey",
    "secretKey",
    "historicalApi",
    "qrCode",
    "sqOffTime",
    "autoLogin",
    "password",
    "maxProfit",
    "maxLoss",
    "profitLocking",
    "qtyByExposure",
    "qtyOnMaxLossPerTrade",
    "maxLossPerTrade",
    "maxOpenTrades",
    "qtyMultiplier",
    "mobile",
    "email",
    "net",
    "marketOrders",
    "enableNRMLSqoff",
    "enableCNCSqoff",
    "exitOrderType",
    "twoFA",
    "maxLossWait",
    "tradingAuthorizationReq",
    "commodityMargin",
    "apiUserDetails",
    "utilizedMargin",
  ];
  const allSeqState = useSelector((state) => state.allSeqReducer);
  const allVisState = useSelector((state) => state.allVisReducer);

  const [ userProfColVis, setuserProfColVis ] = useState(allVisState.userProfVis);
  const [ userProfSeq, setuserProfSeq ] = useState(allSeqState.userProfSeq);
  const [ profColsSelectedALL, setprofColsSelectedALL ] = useState(false);

  const profPageColSelectAll = () => {
    setprofColsSelectedALL((prev) => !prev);
    userProfPageCols.map((userSettingCol) => {
      setuserProfColVis((prev) => ({
        ...prev,
        [ userSettingCol ]: profColsSelectedALL,
      }));
    });
  };


  useEffect(() => {
    setuserProfSeq(allSeqState.userProfSeq);
    setuserProfColVis((prev) => {
      const colVis = {};
      Object.keys(userProfColVis).map((col) => {
        if (allSeqState.userProfSeq.includes(col)) {
          colVis[ col ] = true;
        } else {
          colVis[ col ] = false;
        }
      });

      return { ...colVis };
    });
  }, []);

  useEffect(() => {
    dispatch(
      setAllVis({
        ...allVisState,
        userProfVis: userProfColVis,
      })
    );

    if (new Set(Object.values(userProfColVis)).size === 1) {
      if (Object.values(userProfColVis).includes(true)) {
        setuserProfSeq(userProfPageCols);
      } else {
        setuserProfSeq([]);
      }
    }
  }, [ userProfColVis ]);

  useEffect(() => {
    dispatch(
      setAllSeq({
        ...allSeqState,
        userProfSeq: userProfSeq,
      })
    );
  }, [ userProfSeq ]);

  const [ errorDisplayed, setErrorDisplayed ] = useState(false);

  const handleAddRow = useCallback((rowData) => {
    const mandatoryFields = [ "userId", "name", "broker", "qrCode", "password" ];
    let fieldsWithError = {};

    const hasMissingFields = rows.some((row) => {
      if (row.broker === "pseudo_account") {
        return false;
      }
      const missingFields = mandatoryFields.filter((field) => !row[ field ]);
      missingFields.forEach((field) => (fieldsWithError[ field ] = true));
      return missingFields.length > 0;
    });

    const isDuplicateUserId = rows.some(
      (row, index) =>
        rows
          .slice(0, index)
          .some((otherRow) => otherRow.userId === row.userId) ||
        rows.slice(index + 1).some((otherRow) => otherRow.userId === row.userId)
    );

    if (isDuplicateUserId) {
      handleMsg({
        msg: "Duplicate clientId detected. Please use a Different userId.",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
        user: rowData.userId || "USER",
      });
      return;
    }

    if (!hasMissingFields && !isDuplicateUserId) {
      setErrorDisplayed(false);
      const newRow = {
        enabled: true,
        mtmAll: "0",
        net: "0",
        availableMargin: 0,
        name: "",
        userId: "",
        broker: "",
        secretKey: "",
        apiKey: "",
        qrCode: "",
        sqOffTime: "00:00:00",
        maxProfit: "0",
        maxLoss: "0",
        profitLocking: "",
        qtyByExposure: "0",
        maxLossPerTrade: "0",
        maxOpenTrades: "0",
        qtyMultiplier: 0.0,
        mobile: "",
        email: "",
        password: "",
        autoLogin: false,
        historicalApi: false,
        inputDisabled: false,
        utilizedMargin: 0.0,
        max_open_trades: 0,
      };
      const updatedRows = [ ...rows, newRow ];
      dispatch(
        setBrokers({
          brokers: updatedRows,
        })
      );
    } else {
      const missingFields = mandatoryFields.filter(
        (field) => fieldsWithError[ field ]
      );
      if (missingFields.length > 0) {
        const errorMsg = `Please enter ${missingFields.join(", ")} before adding a new row.`;
        handleMsg({
          msg: errorMsg,
          logType: "WARNING",
          timestamp: `${new Date().toLocaleString()}`,
          user: rowData.userId || "USER",
        });
      }
      setErrorDisplayed(true);
    }
  }, [ rows, dispatch, handleMsg ]);

  useEffect(() => {
    const scrollTimeout = setTimeout(() => {
      if (tableRef.current) {
        tableRef.current.scrollLeft = 0;
        tableRef.current.scrollTop = tableRef.current.scrollHeight;
      }
    }, 100);

    return () => clearTimeout(scrollTimeout);
  }, [ rows.length ]);


  const openModalForExpiry = () => {
    setIsOpenModal(true);
  };

  const closeModalForExpiry = () => {
    setIsOpenModal(false);
  };
  const navigate = useNavigate();

  const HandleRedirect = () => {
    navigate("/Subscription");
  };

  const savedExpiry = new Date(cookies.get("expiryDate"));
  const currentDate = new Date();
  const timeDifference = savedExpiry - currentDate;
  const dayDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

  const alertShown = localStorage.getItem("subscriptionAlertShown");
  const daysRemaining = savedExpiry.toDateString();

  if (dayDifference <= 2 && dayDifference > 0 && !alertShown) {
    openModalForExpiry();
    localStorage.setItem("subscriptionAlertShown", "true");
  }
  const mappedRows = useMemo(() => {
    if (!rows || rows.length === 0) {
      return [];
    }
    const mapped = rows.map((row) => {
      const mappedRow = {
        enabled: row.enabled ?? false,
        inputDisabled: row.inputDisabled ?? false,
        userId: row.userId ?? "",
        manualExit: row.manualExit ?? "",
        mtmAll: row.mtmAll ?? "0",
        availableMargin: row.availableMargin ?? "0",
        name: row.name ?? "",
        broker: row.broker ?? "",
        apiKey: row.apiKey ?? "",
        secretKey: row.secretKey ?? "",
        historicalApi: row.historicalApi ?? false,
        qrCode: row.qrCode ?? "",
        sqOffTime: row.sqOffTime ?? "00:00:00",
        autoLogin: row.autoLogin ?? false,
        password: row.password ?? "",
        maxProfit: row.maxProfit ?? "0",
        maxLoss: row.maxLoss ?? "0",
        profitLocking: row.profitLocking ?? "",
        qtyByExposure: row.qtyByExposure ?? "0",
        qtyOnMaxLossPerTrade: row.qtyOnMaxLossPerTrade ?? "0",
        maxLossPerTrade: row.maxLossPerTrade ?? "0",
        maxOpenTrades: row.maxOpenTrades ?? "0",
        qtyMultiplier: row.qtyMultiplier ?? "1",
        mobile: row.mobile ?? "",
        email: row.email ?? "",
        net: row.net ?? "0",
        marketOrders: row.marketOrders ?? "",
        enableNRMLSqoff: row.enableNRMLSqoff ?? false,
        enableCNCSqoff: row.enableCNCSqoff ?? false,
        exitOrderType: row.exitOrderType ?? "Market",
        twoFA: row.twoFA ?? "",
        maxLossWait: row.maxLossWait ?? "",
        tradingAuthorizationReq: row.tradingAuthorizationReq ?? false,
        commodityMargin: row.commodityMargin ?? "0",
        apiUserDetails: row.apiUserDetails ?? "",
        utilizedMargin: row.utilized_Margin ?? "0",
        isDuplicate: row.isDuplicate ?? false,
      };
      userProfPageCols.forEach((col) => {
        if (!(col in mappedRow)) {
          mappedRow[ col ] = "";
        }
      });
      return mappedRow;
    });

    return mapped;
  }, [ rows ]);

  const [ filters, setFilters ] = useState({});

  const [ tempFilters, setTempFilters ] = useState({});

  const [ filterPopup, setFilterPopup ] = useState(null);
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 });

  const [ showSecretKey, setShowSecretKey ] = useState(true);

  const [ isChecked, setIsChecked ] = useState(false);
  const handleCheckboxChangeQty = (e) => {
    setIsChecked(e.target.checked);
  };

  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  const handleFilterToggle = (column, event) => {
    const { top, left, height } = event.target.getBoundingClientRect();
    setFilterPopup(filterPopup === column ? null : column);
    setPopupPosition({ top: top + height, left });
    setTempFilters(filters);
  };

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) =>
      selectedOptions.includes(opt)
    );

    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [ column ]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [ column ]: [ ...currentOptions ] }));
    }
  };

  const handleApplyFilter = () => {
    const newFilters = { ...filters, [ filterPopup ]: tempFilters[ filterPopup ] || [] };
    setFilters(newFilters);
    setFilterPopup(null);
  };

  const filteredRows = useMemo(() => {
    let result = Array.isArray(mappedRows) ? mappedRows : [];

    if (actionFilter !== "all") {
      result = result.filter((row) =>
        actionFilter === "checked" ? row.enabled : !row.enabled
      );
    }

    if (Object.keys(filters).length > 0) {
      result = result.filter((row) =>
        Object.keys(filters).every((col) => {
          const key = col.replace(/\s/g, "");
          return filters[ col ]?.length > 0
            ? filters[ col ].includes(String(row[ key ]).toLowerCase())
            : true;
        })
      );
    }
    return result;
  }, [ mappedRows, filters, actionFilter ]);


  const getDynamicUniqueValues = (column) => {
    const key = column.replace(/\s/g, "");
    const isFirstFilter = Object.keys(filters).length === 0 ||
      (Object.keys(filters).length === 1 && filters[ column ]);
    const sourceData = isFirstFilter ? mappedRows : filteredRows;

    return Array.from(
      new Set(sourceData.map((row) => String(row[ key ] || "").toLowerCase()))
    ).filter(Boolean);
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  const updateRowData = async (index, updatedData) => {
    const updatedRows = [ ...rows ];
    const existingRow = updatedRows[ index ];

    updatedRows[ index ] = { ...existingRow, ...updatedData };

    dispatch(
      setBrokers({
        brokers: updatedRows,
      })
    );
  };

  const handleLogout = async (rowData, index) => {
    let count = 0;
    for (let j = 0; j < rows.length; j++) {
      if (rows[ j ].inputDisabled === true) {
        count++;
      }
    }

    if (isStarted && count <= 1) {
      openModal();
      return;
    }
    try {
      if (rowData.broker !== "pseudo_account") {
        const response = await fetchWithAuth(
          `api/logout_broker_accounts/${rowData.broker}/${rowData.userId}`,
          {
            method: "POST",
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message || "Something bad happened. Please try again"
          );
        }
        const responseData = await response.json();
        const updatedBrokers = [ ...rows ];
        updatedBrokers[ index ] = {
          ...updatedBrokers[ index ],
          apiUserDetails: "",
          availableMargin: 0,
          net: 0,
          inputDisabled: false,
        };

        dispatch(
          setBrokers({
            brokers: updatedBrokers,
          })
        );
        handleMsg({
          msg: `Logged out Successfully. - ${rowData.userId}`,
          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
          user: rowData.userId,
        });
      } else {
        const updatedBrokers = [ ...rows ];
        updatedBrokers[ index ] = {
          ...updatedBrokers[ index ],
          apiUserDetails: "",
          availableMargin: 0,
          net: 0,
          inputDisabled: false,
        };

        dispatch(
          setBrokers({
            brokers: updatedBrokers,
          })
        );
        handleMsg({
          msg: `Logged out Successfully. - ${rowData.userId}`,
          logType: "MESSAGE",
          timestamp: ` ${new Date().toLocaleString()}`,
          user: rowData.userId,
        });
      }
    } catch (error) { }
  };

  const handleDelete = async (rowData, index) => {
    try {
      if (rowData.broker !== "" && rowData.userId != "") {
        const response = await fetchWithAuth(
          `/api/delete_credentials/${mainUser}/${rowData.userId}/${rowData.broker}`,
          {
            method: "DELETE",
          }
        );

        if (!response.ok) {
          throw new Error(await response.json());
        }
      }
    } catch (error) {
    } finally {
      const updatedRows = rows.filter((_, i) => i !== index);

      dispatch(
        setBrokers({
          brokers: updatedRows,
        })
      );
      handleMsg({
        msg: `Row Deleted Successfully. - ${rowData.userId}`,
        logType: "MESSAGE",
        timestamp: ` ${new Date().toLocaleString()}`,
        user: rowData.userId,
      });
    }
  };

  const handleManualSqOff = async (index) => {
    try {
      const row = rows[ index ];
      if (!row || !row.inputDisabled) {
        handleMsg({
          msg: "Please log in the broker account",
          logType: "WARNING",
          timestamp: `${new Date().toLocaleString()}`,
          color: "red",
        });
        return;
      }

      let endpoint = "";
      let endpoint2 = "";

      switch (row.broker) {
        case "angelone":
          endpoint = `/api/angelone_user_equity_sqoff/${mainUser}/${row.userId}`;
          endpoint2 = `/api/angelone_user_options_sqoff/${mainUser}/${row.userId}`;
          break;
        case "fyers":
          endpoint = `/api/fyers_user_equity_sqoff/${mainUser}/${row.userId}`;
          endpoint2 = `/api/fyers_user_options_sqoff/${mainUser}/${row.userId}`;
          break;
        case "flattrade":
          endpoint = `/api/flattrade_user_equity_sqoff/${mainUser}/${row.userId}`;
          endpoint2 = `/api/flattrade_user_options_sqoff/${mainUser}/${row.userId}`;
          break;
        case "pseudo_account":
          endpoint = `/api/pseudo_user_options_sqoff/${mainUser}/${row.userId}`;
          endpoint2 = `api/pseudo_user_equity_sqoff/${mainUser}/${row.userId}`;
          break;
        default:
          return;
      }

      await callEndpoint(endpoint, row);
      await callEndpoint(endpoint2, row);
    } catch (error) { }
  };
  const callEndpoint = async (endpoint, rowData, endpointType) => {
    try {
      const response = await fetchWithAuth(endpoint, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        const message = errorData.message;
        handleMsg({
          msg: message,
          logType: "ERROR",
          timestamp: `${new Date().toLocaleString()}`,
          user: rowData.userId,
        });
      } else {
        const responseData = await response.json();
        const message = responseData.message;
        handleMsg({
          msg: message,
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
          user: rowData.userId,
        });
      }
    } catch (error) {
      console.error(
        `Error occurred while calling ${endpointType} API:`,
        error.message
      );
    }
  };

  const previousValuesRef = useRef({});

  const debouncedUpdateDisplayName = useDebounce((name, index) => {
    const row = rows[ index ];
    const prevName = previousValuesRef.current[ index ]?.name;


    if (name !== prevName) {
      previousValuesRef.current[ index ] = { name };
      updateDisplayName(name, index);
    }
  }, 1000);

  const updateDisplayName = async (Name, index) => {
    const row = rows[ index ];
    try {
      const response = await fetchWithAuth(
        `/api/update_displayname/${mainUser}/${row.userId}`,
        {
          method: "POST",
          body: JSON.stringify({ display_name: Name }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update display name");
      }

      const data = await response.json();

    } catch (error) {
      console.error("Error updating display name:", error);
    }
  };

  const debouncedUpdateValues = useDebounce((
    maxProfit,
    maxLoss,
    maxLossPerTrade,
    qtyMultiplier,
    maxOpenTrades,
    sqOffTime,
    index
  ) => {
    const currentValues = {
      maxProfit,
      maxLoss,
      maxLossPerTrade,
      qtyMultiplier,
      maxOpenTrades,
      sqOffTime
    };

    const prevValues = previousValuesRef.current[ index ] || {};
    const hasChanged = Object.keys(currentValues).some(
      key => currentValues[ key ] !== prevValues[ key ]
    );

    if (!hasChanged) return;
    previousValuesRef.current[ index ] = { ...currentValues };

    handleUpdateValues(
      maxProfit,
      maxLoss,
      maxLossPerTrade,
      qtyMultiplier,
      maxOpenTrades,
      sqOffTime,
      index
    );
  }, 1000);

  const handleUpdateValues = async (
    maxProfit,
    maxLoss,
    maxLossPerTrade,
    qtyMultiplier,
    maxOpenTrades,
    sqOffTime,
    index
  ) => {
    const row = rows[ index ];
    try {
      const maxValues = {
        max_profit: maxProfit,
        max_loss: maxLoss,
        max_loss_per_trade: maxLossPerTrade,
        user_multiplier: qtyMultiplier,
        max_open_trades: maxOpenTrades,
        exit_time: sqOffTime,
      };
      const response = await fetchWithAuth(
        `/api/update_user_data/${mainUser}/${row.userId}`,
        {
          method: "POST",
          body: JSON.stringify(maxValues),
        }
      );

      if (response.ok) {
        const res = await response.json();
        handleMsg({
          msg: res.message,
          logType: "MESSAGE",
          timestamp: new Date().toLocaleString(),
          user: row.userId,
        });
      } else {
        console.error("Failed to update values:", response.status);
      }
    } catch (error) {
      console.error("Error while updating values:", error);
    }
  };

  const [ showPopup, setShowPopup ] = useState(false);
  const [ popupValues, setPopupValues ] = useState({
    index: "",
    profitReaches: "",
    lockMinimumProfit: "",
    increaseInProfit: "",
    trailProfitBy: "",
  });

  useEffect(() => {
    if (!showPopup) {
      setPopupValues({
        index: "",
        profitReaches: "",
        lockMinimumProfit: "",
        increaseInProfit: "",
        trailProfitBy: "",
      });
    }
  }, [ showPopup ]);

  const handleClickOutside = (event) => {
    if (popupRef.current && !popupRef.current.contains(event.target)) {
      setShowPopup(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [ popupRef ]);

  const togglePopup = (index) => {
    const profitLocking = rows.filter((row, i) => index === i)[ 0 ]?.profitLocking?.split("~") || [ "", "", "", "" ];
    const profitReaches = profitLocking[ 0 ];
    const lockMinimumProfit = profitLocking[ 1 ];
    const increaseInProfit = profitLocking[ 2 ];
    const trailProfitBy = profitLocking[ 3 ];
    setPopupValues((prev) => ({
      ...prev,
      index: index,
      profitReaches: profitReaches,
      lockMinimumProfit: lockMinimumProfit,
      increaseInProfit: increaseInProfit,
      trailProfitBy: trailProfitBy,
    }));
    setShowPopup(!showPopup);
  };

  const handleInputChange = (name, value) => {
    setPopupValues((prevValues) => ({ ...prevValues, [ name ]: value }));
  };

  const updateUserProfitLocking = async (
    brokerID,
    profitReachesValue,
    lockProfitValue,
    increaseInProfit,
    trailProfitBy
  ) => {
    try {
      const profitLockingValue = `${profitReachesValue}~${lockProfitValue}~${increaseInProfit}~${trailProfitBy}`;
      const modifiedRows = rows.map((row) =>
        row.userId === brokerID
          ? {
            ...row,
            profitLocking: profitLockingValue,
          }
          : row
      );
      const response = await fetchWithAuth(
        `/api/update_user_profit_locking/${mainUser}/${brokerID}`,
        {
          method: "POST",
          body: JSON.stringify({
            profit_locking: `${profitReachesValue},${lockProfitValue},${increaseInProfit},${trailProfitBy}`,
          }),
        }
      );

      if (response.ok) {
        dispatch(
          setBrokers({
            brokers: modifiedRows,
          })
        );
        console.log(
          "Profit locking updated successfully for broker:",
          brokerID
        );
      } else {
        const err = await response.json();
        console.error("Failed to update profit locking:", err);
      }
    } catch (error) {
      console.error("An unexpected error occurred:", error);
    }
  };

  const handleSetTrailTGT = () => {
    let profitReachesValue = document.getElementById("trail_tgt_0").value;
    let lockProfitValue = document.getElementById("trail_tgt_1").value;
    let increaseInProfitValue = document.getElementById("trail_tgt_2").value;
    let trailProfitByValue = document.getElementById("trail_tgt_3").value;

    document.getElementById("profitReachesError").innerText = "";
    document.getElementById("lockProfitError").innerText = "";
    document.getElementById("increaseInProfitError").innerText = "";
    document.getElementById("trailProfitByError").innerText = "";

    if (
      (profitReachesValue && !lockProfitValue) ||
      (!profitReachesValue && lockProfitValue)
    ) {
      if (!profitReachesValue) {
        document.getElementById("profitReachesError").innerText =
          "Value is required.";
      } else {
        document.getElementById("lockProfitError").innerText =
          "Value is required.";
      }
      return;
    }

    if (
      (increaseInProfitValue && !trailProfitByValue) ||
      (!increaseInProfitValue && trailProfitByValue)
    ) {
      if (!increaseInProfitValue) {
        document.getElementById("increaseInProfitError").innerText =
          "Value is required.";
      } else {
        document.getElementById("trailProfitByError").innerText =
          "Value is required.";
      }
      return;
    }

    updateRowData(popupValues.index, {
      profitLocking: `${profitReachesValue}~${lockProfitValue}~${increaseInProfitValue}~${trailProfitByValue}`,
    });
    const strategyIndex = popupValues?.index;

    const brokerID = rows[ strategyIndex ]?.userId;

    updateUserProfitLocking(
      brokerID,
      profitReachesValue,
      lockProfitValue,
      increaseInProfitValue,
      trailProfitByValue
    );
    setShowPopup(false);
  };

  const handleInputDelete = () => {
    updateRowData(popupValues.index, {
      profitLocking: "~~~",
    });

    const brokerID = rows.filter((row, i) => popupValues.index === i)[ 0 ]
      ?.userId;

    updateUserProfitLocking(brokerID, "", "", "", "");

    setPopupValues((prev) => ({
      ...prev,
      profitReaches: "",
      lockMinimumProfit: "",
      increaseInProfit: "",
      trailProfitBy: "",
    }));
  };

  const [ isModalOpen, setIsModalOpen ] = useState(false);
  const [ selectedUser, setSelectedUser ] = useState({
    userId: '',
    broker: '',
    name: ''
  });

  const openMarketOrdersModal = (userId, broker, name) => {
    setSelectedUser({
      userId,
      broker,
      name
    });
    setIsModalOpen(true);
  };

  const closeModalMarketOrders = () => {
    setIsModalOpen(false);
  };

  const handleSaveSettings = (settings) => {
    console.log('Saved settings:', settings);
    closeModalMarketOrders();
  };

  const columnDisplayNames = {
    action: "Action",
    userId: "Client ID",
    manualExit: "Manual Exit",
    mtmAll: "MTM (All)",
    availableMargin: "Available Margin",
    name: "Display Name",
    broker: "Broker",
    apiKey: "API Key",
    secretKey: "API Secret Key",
    historicalApi: "Data API",
    qrCode: "QR Code",
    sqOffTime: "Exit Time",
    autoLogin: "Auto Login",
    password: "Password",
    maxProfit: "Max Profit",
    maxLoss: "Max Loss",
    profitLocking: "Profit Locking",
    qtyByExposure: "Qty By Exposure",
    qtyOnMaxLossPerTrade: "Qty on Max Loss/Trade",
    maxLossPerTrade: "Max Loss Per Trade",
    maxOpenTrades: "Max Open Trades",
    qtyMultiplier: "Quantity Multiplier",
    mobile: "Mobile Number",
    email: "Email",
    net: "Net Value",
    marketOrders: "Market Orders",
    enableNRMLSqoff: "Enable NRML SqOff",
    enableCNCSqoff: "Enable CNC SqOff",
    exitOrderType: "Exit Order Type",
    twoFA: "Two-Factor Auth",
    maxLossWait: "Max Loss Wait Time",
    tradingAuthorizationReq: "Trading Auth Required",
    commodityMargin: "Commodity Margin",
    apiUserDetails: "API User Details",
    utilizedMargin: "Utilized Margin",
  };

  return (
    <div>
      <style>{styles}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav
            pageCols={userProfPageCols}
            colsSelectedAll={profColsSelectedALL}
            setColsSelectedALL={setprofColsSelectedALL}
            selectAll={profPageColSelectAll}
            colVis={userProfColVis}
            setColVis={setuserProfColVis}
            setSeq={setuserProfSeq}
            rows={rows}
          />
          <div className="table-container" ref={tableRef}>
            <table className="user-profiles-table" style={{ borderRadius: "10px" }}>
              <thead style={{ position: "sticky", top: 0, zIndex: "10", height: "auto" }}>
                <tr>
                  {userProfSeq.map((col) => {
                    const hasFilter = filters[ col ] && filters[ col ].length > 0;
                    const selectedItems = filters[ col ]?.length || 0;

                    return (
                      <th
                        key={col}
                        data-narrow-column={col === "historicalApi" || col === "autoLogin" ? true : undefined}
                        className={
                          col === "action" ? "fixed-column-header-1" :
                            col === "userId" ? "fixed-column-header-2" :
                              col === "mtmAll" ? "fixed-column-header-3" : ""
                        }
                        style={{
                          fontSize: "15px",
                          padding: col === "userId" ? "0" : "4px 2px",
                          textAlign: "center",
                          backgroundColor: hasFilter ? "#f0f7ff" : "inherit",
                          borderBottom: hasFilter ? "2px solid #1976d2" : "inherit",
                          height: "auto",
                          minWidth: (() => {
                            // Base width calculation
                            let baseWidth = col === "userId" || col === "manualExit" ? 80 :
                              col === "availableMargin" ? 140 : col === "qtyOnMaxLossPerTrade" ? 100 :
                                col === "historicalApi" || col === "autoLogin" ? 100 :
                                  col === "maxProfit" || col === "maxLoss" ? 100 : 80;

                            // Add extra width for filter badge when filter is applied
                            const filterExtraWidth = hasFilter ? 30 : 0;

                            return `${baseWidth + filterExtraWidth}px`;
                          })(),
                          width: col === "historicalApi" || col === "autoLogin" ? "100px" : "auto",

                        }}
                      >
                        <div style={{
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "center",
                          position: "relative",
                          padding: "0",
                          margin: "0",
                          gap: "3px",
                          width: "100%"
                        }}>
                          {/* Columns that don't need filters */}
                          {(col === "apiKey" || col === "secretKey" || col === "qrCode" || col === "historicalApi" || col === "autoLogin" || col === "password" || col === "manualExit" || col === "tradingAuthorizationReq" || col === "enableCNCSqoff" || col === "enableNRMLSqoff" || col === "qtyOnMaxLossPerTrade") ? (
                            <div style={{
                              fontSize: "14px",
                              fontWeight: "600",
                              wordBreak: "break-word",
                              hyphens: "auto",
                              lineHeight: "1",
                              margin: "0",
                              padding: "0",
                              textAlign: "center",
                              width: "100%"
                            }}>
                              {columnDisplayNames[ col ] ||
                                col.charAt(0).toUpperCase() + col.slice(1).replace(/([A-Z])/g, " $1")}
                            </div>
                          ) : (
                            <TableHeaderWithFilter
                              col={col}
                              columnDisplayNames={columnDisplayNames}
                              hasFilter={hasFilter}
                              selectedItems={selectedItems}
                              handleFilterToggle={handleFilterToggle}
                              filterIcon={filterIcon}
                            />
                          )}

                          {col === "action" && filterPopup === "action" && (
                            <div
                              ref={filterPopupRef}
                              style={{
                                position: "absolute",
                                top: "100%",
                                left: "0",
                                zIndex: 1000,
                                background: "white",
                                border: "1px solid #ccc",
                                boxShadow: "0 2px 5px rgba(0,0,0,0.15)",
                                minWidth: "100px",
                                marginLeft: "-10px",
                              }}
                            >
                              <select
                                value={actionFilter}
                                onChange={(e) => setActionFilter(e.target.value)}
                                style={{ padding: "0.1rem 0.3rem", width: "100%", margin: "1px" }}
                              >
                                <option value="all">All</option>
                                <option value="checked">Enable</option>
                                <option value="unchecked">Disable</option>
                              </select>
                            </div>
                          )}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody>
                {filteredRows?.map((rowData, index) => {
                  const userProfTD = {
                    action: userProfColVis[ "action" ] && (
                      <td className="fixed-column-1" style={{ width: "auto" }} colSpan="1">
                        <div className="action-icons">
                          <span className="tooltip-container" style={{ display: "inline-block", width: "25px", height: "25px" }}>
                            {rowData.enabled ? (
                              <div
                                style={{ ...iconStyle, display: "flex", justifyContent: "center", alignItems: "center" }}
                                onClick={() => updateRowData(index, { enabled: !rowData.enabled })}
                              >
                                <StopIcon sx={{ color: "red" }} />
                              </div>
                            ) : (
                              <div
                                style={{ ...iconStyle, display: "flex", justifyContent: "center", alignItems: "center" }}
                                onClick={() => updateRowData(index, { enabled: !rowData.enabled })}
                              >
                                <PlayArrowIcon sx={{ color: "green" }} />
                              </div>
                            )}
                            <span className="tooltiptext">{rowData.enabled ? "Disable" : "Enable"}</span>
                          </span>

                          <span className="tooltip-container" style={{ display: "inline-block", width: "25px", height: "25px" }}>
                            {rowData.inputDisabled ? (
                              <img
                                src={Logout}
                                alt="icon"
                                className="logout_icon"
                                style={{ height: "25px", width: "25px" }}
                                onClick={() => handleLogout(rowData, index)}
                              />
                            ) : (
                              <img
                                src={Log}
                                alt="icon"
                                className="logout_icon"
                                style={{ height: "25px", width: "25px" }}
                              />
                            )}
                            <span className={`tooltiptext ${rowData.inputDisabled ? "login-tooltip" : "logout-tooltip"}`}>
                              {rowData.inputDisabled ? "Logout" : "Login"}
                            </span>
                          </span>

                          <span className="tooltip-container" style={{ display: "inline-block", width: "25px", height: "25px" }}>
                            <div
                              style={{ ...iconStyle, display: "flex", justifyContent: "center", alignItems: "center" }}
                              onClick={() => handleDelete(rowData, index)}
                            >
                              <DeleteIcon sx={{ color: "red" }} />
                            </div>
                            <span className="tooltiptext delete-tooltip">Delete</span>
                          </span>
                        </div>
                      </td>
                    ),
                    userId: userProfColVis[ "userId" ] && (
                      <td
                        className="fixed-column-2"
                        style={{
                          position: "relative",
                          minWidth: "120px",
                          width: "120px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          padding: "0",
                        }}
                      >
                        <input
                          type="text"
                          value={rowData.userId || ""}
                          onChange={(e) => {
                            const newValue = e.target.value;
                            if (newValue !== rowData.userId) {
                              updateRowData(index, { userId: newValue });
                            }
                          }}
                          onMouseEnter={(e) => {
                            const input = e.target;
                            if (input.scrollWidth > input.clientWidth) {
                              setHoverData({
                                content: rowData.userId || "",
                                x: e.clientX + 10,
                                y: e.clientY + 10,
                              });
                            }
                          }}
                          onMouseLeave={() => setHoverData(null)}
                          onBlur={() => {
                            const newValue = rowData.userId;
                            const isDuplicate = rows.some(
                              (row, rowIndex) =>
                                row.userId === newValue && rowIndex !== index
                            );
                            if (isDuplicate) {
                              updateRowData(index, {
                                userId: "",
                                isDuplicate: true,
                              });
                              handleMsg({
                                msg: "Client Ids are the same. Please enter a different Client Id.",
                                logType: "WARNING",
                                timestamp: `${new Date().toLocaleString()}`,
                                user: newValue,
                              });
                            } else {
                              updateRowData(index, { isDuplicate: false });
                            }
                          }}
                          readOnly={rowData.inputDisabled}
                          autoComplete="off"
                          style={{
                            ...inputStyles,
                            color: rowData.inputDisabled
                              ? "initial"
                              : "darkgray",
                            padding: "8px 4px",
                            width: "100%",
                            minWidth: "100px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            textAlign: "center",
                          }}
                        />
                      </td>
                    ),
                    manualExit: userProfColVis[ "manualExit" ] && (
                      <td style={{ textAlign: "center" }}>
                        <img
                          src={Log}
                          alt="icon"
                          className="logout_icon"
                          style={{
                            height: "25px",
                            width: "25px",
                            display: "inline-block",
                          }}
                          onClick={() => {
                            handleManualSqOff(index);
                          }}
                        />
                      </td>
                    ),
                    mtmAll: userProfColVis[ "mtmAll" ] && (
                      <td className="fixed-column-3" style={{ textAlign: "right" }}>
                        <input
                          type="number"
                          value={
                            !isNaN(Number(rowData.mtmAll)) &&
                              Number(rowData.mtmAll) !== 0
                              ? Number(rowData.mtmAll).toFixed(2)
                              : 0
                          }
                          disabled
                          style={{ ...inputStyles, width: "100px", textAlign: "center", padding: "8px", color: !isNaN(Number(rowData.mtmAll)) && Number(rowData.mtmAll) < 0 ? "red" : "green" }}

                        />
                      </td>
                    ),
                    availableMargin: userProfColVis[ "availableMargin" ] && (
                      <td>
                        <input
                          type="number"
                          value={Number(rowData?.availableMargin).toFixed(2) || 0.00}
                          disabled
                          style={{ width: "140px", textAlign: "right", padding: "8px", color: "darkgray" }}
                          step="0.01"
                        />
                      </td>
                    ),
                    name: userProfColVis[ "name" ] && (
                      <td>
                        <input
                          type="text"
                          value={rowData.name}
                          onChange={(e) => {
                            updateRowData(index, { name: e.target.value });
                            debouncedUpdateDisplayName(e.target.value, index);
                          }}
                          onMouseEnter={(e) => {
                            if (e.target.scrollWidth > e.target.clientWidth) {
                              setHoverData({
                                content: rowData.name,
                                x: e.clientX + 10,
                                y: e.clientY + 10,
                              });
                            }
                          }}
                          onMouseLeave={() => setHoverData(null)}
                          style={{
                            ...inputStyles,
                            textAlign: "left",
                            padding: "8px 6px",
                            color: rowData.inputDisabled ? "initial" : "darkgray",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                        />
                      </td>
                    ),
                    broker: userProfColVis[ "broker" ] && (
                      <td>
                        <select
                          onChange={(e) =>
                            updateRowData(index, { broker: e.target.value })
                          }
                          value={rowData.broker}
                          disabled={rowData.inputDisabled}
                          style={{
                            ...inputStyles,
                            textAlign: "left",
                            padding: "8px 6px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            color: rowData.inputDisabled
                              ? "initial"
                              : "darkgray",
                          }}
                        >
                          <option value="">--select</option>
                          {brokerName?.map((broker) => (
                            <option key={broker.id} value={broker.name}>
                              {broker.name.charAt(0).toUpperCase() +
                                broker.name.slice(1)}
                            </option>
                          ))}
                        </select>
                      </td>
                    ),
                    apiKey: userProfColVis[ "apiKey" ] && (
                      <td>
                        <form autoComplete="off">
                          <input
                            type="text"
                            value={rowData.userId || ""}
                            readOnly={true}
                            style={{ display: "none" }}
                            id={`username-${index}-apiKey`}
                            autoComplete="username"
                            aria-hidden="true"
                          />
                          <div style={{ position: "relative" }}>
                            <input
                              type={
                                showPasswordRowsApi[ index ] ? "text" : "password"
                              }
                              value={rowData.apiKey}
                              onChange={(e) =>
                                updateRowData(index, { apiKey: e.target.value })
                              }
                              readOnly={rowData.inputDisabled}
                              style={{
                                ...inputStyles, paddingRight: "25px",
                                padding: "8px",
                                color: rowData.inputDisabled
                                  ? "initial"
                                  : "darkgray",
                              }}
                              autoComplete="new-password"
                              aria-label="API Key"
                            />
                            <span
                              style={{
                                position: "absolute",
                                right: "5px",
                                top: "50%",
                                transform: "translateY(-50%)",
                                cursor: "pointer",
                              }}
                              onClick={() =>
                                togglePasswordVisibilityForRowApi(index)
                              }
                            >
                              {showPasswordRowsApi[ index ] ? (
                                <FaEye />
                              ) : (
                                <FaEyeSlash />
                              )}
                            </span>
                          </div>
                        </form>
                      </td>
                    ),
                    secretKey: userProfColVis[ "secretKey" ] && (
                      <td>
                        {showSecretKey && (
                          <form autoComplete="off">
                            <input
                              type="text"
                              value={rowData.userId || ""}
                              readOnly={true}
                              style={{ display: "none" }}
                              id={`username-${index}-secretKey`}
                              autoComplete="username"
                              aria-hidden="true"
                            />
                            <div style={{ position: "relative" }}>
                              <input
                                type={
                                  showPasswordRowsSecretKey[ index ]
                                    ? "text"
                                    : "password"
                                }
                                value={rowData.secretKey}
                                readOnly={rowData.inputDisabled}
                                onChange={(e) =>
                                  updateRowData(index, {
                                    secretKey: e.target.value,
                                  })
                                }
                                style={{
                                  ...inputStyles, paddingRight: "25px",
                                  padding: "8px",
                                  color: rowData.inputDisabled
                                    ? "initial"
                                    : "darkgray",
                                }}
                                autoComplete="new-password"
                                aria-label="Secret Key"
                              />
                              <span
                                style={{
                                  position: "absolute",
                                  right: "5px",
                                  top: "50%",
                                  transform: "translateY(-50%)",
                                  cursor: "pointer",
                                }}
                                onClick={() =>
                                  togglePasswordVisibilityForRowSecretKey(index)
                                }
                              >
                                {showPasswordRowsSecretKey[ index ] ? (
                                  <FaEye />
                                ) : (
                                  <FaEyeSlash />
                                )}
                              </span>
                            </div>
                          </form>
                        )}
                      </td>
                    ),
                    historicalApi: userProfColVis[ "historicalApi" ] && (
                      <td style={{ textAlign: "center" }}>
                        <input
                          type="checkbox"
                          checked={rowData.historicalApi}
                          value={rowData.historicalApi}
                          onChange={() => {
                            updateRowData(index, {
                              historicalApi: !rowData.historicalApi,
                            });
                          }}
                          style={{ padding: "8px" }}
                        />
                      </td>
                    ),
                    qrCode: userProfColVis[ "qrCode" ] && (
                      <td>
                        <form autoComplete="off">
                          <input
                            type="text"
                            value={rowData.userId || ""}
                            readOnly={true}
                            style={{ display: "none" }}
                            id={`username-${index}-qrCode`}
                            autoComplete="username"
                            aria-hidden="true"
                          />
                          <div style={{ position: "relative" }}>
                            <input
                              type={
                                showPasswordRowsQr[ index ] ? "text" : "password"
                              }
                              value={rowData.qrCode}
                              onChange={(e) =>
                                updateRowData(index, { qrCode: e.target.value })
                              }
                              readOnly={rowData.inputDisabled}
                              style={{
                                ...inputStyles, paddingRight: "25px",
                                padding: "8px",
                                color: rowData.inputDisabled
                                  ? "initial"
                                  : "darkgray",
                              }}
                              autoComplete="new-password"
                              aria-label="QR Code"
                            />
                            <span
                              style={{
                                position: "absolute",
                                right: "5px",
                                top: "50%",
                                transform: "translateY(-50%)",
                                cursor: "pointer",
                              }}
                              onClick={() =>
                                togglePasswordVisibilityForRowQr(index)
                              }
                            >
                              {showPasswordRowsQr[ index ] ? (
                                <FaEye />
                              ) : (
                                <FaEyeSlash />
                              )}
                            </span>
                          </div>
                        </form>
                      </td>
                    ),
                    sqOffTime: userProfColVis[ "sqOffTime" ] && (
                      <td>
                        <TimePicker
                          className="custom-time-picker"
                          value={rowData.sqOffTime}
                          disableClock={true}
                          format="HH:mm:ss"
                          maxDetail={"second"}
                          clearIcon={null}
                          clockIcon={null}
                          onChange={(newTime) => {
                            updateRowData(index, { sqOffTime: newTime });
                            debouncedUpdateValues(
                              rowData.maxProfit,
                              rowData.maxLoss,
                              rowData.maxLossPerTrade,
                              rowData.qtyMultiplier,
                              rowData.maxOpenTrades,
                              newTime,
                              index
                            );
                          }}
                        />
                      </td>
                    ),
                    autoLogin: userProfColVis[ "autoLogin" ] && (
                      <td style={{ textAlign: "center" }}>
                        <input
                          type="checkbox"
                          checked={rowData.autoLogin}
                          value={rowData.autoLogin}
                          onChange={() => {
                            updateRowData(index, {
                              autoLogin: !rowData.autoLogin,
                            });
                          }}
                          style={{ padding: "8px" }}
                        />
                      </td>
                    ),
                    password: userProfColVis[ "password" ] && (
                      <td>
                        <form autoComplete="off">
                          <input
                            type="text"
                            value={rowData.userId || ""}
                            readOnly={true}
                            style={{ display: "none" }}
                            id={`username-${index}-password`}
                            autoComplete="username"
                            aria-hidden="true"
                          />
                          <div style={{ position: "relative" }}>
                            <input
                              type={
                                showPasswordRowsPassword[ index ] ? "text" : "password"
                              }
                              value={rowData.password}
                              onChange={(e) =>
                                updateRowData(index, { password: e.target.value })
                              }
                              readOnly={rowData.inputDisabled}
                              style={{
                                ...inputStyles, paddingRight: "25px",
                                padding: "8px",
                                color: rowData.inputDisabled
                                  ? "initial"
                                  : "darkgray",
                              }}
                              autoComplete="new-password"
                              aria-label="Password"
                            />
                            <span
                              style={{
                                position: "absolute",
                                right: "5px",
                                top: "50%",
                                transform: "translateY(-50%)",
                                cursor: "pointer",
                              }}
                              onClick={() =>
                                togglePasswordVisibilityForRowPassword(index)
                              }
                            >
                              {showPasswordRowsPassword[ index ] ? (
                                <FaEye />
                              ) : (
                                <FaEyeSlash />
                              )}
                            </span>
                          </div>
                        </form>
                      </td>
                    ),
                    maxProfit: userProfColVis[ "maxProfit" ] && (
                      <td>
                        <input
                          type="number"
                          value={rowData.maxProfit}
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[eE+\-]/g,
                              ""
                            );
                          }}
                          onChange={(e) => {
                            const regex = /^\d{1,7}(\.\d{0,2})?$/;
                            if (regex.test(e.target.value) || e.target.value === "") {
                              updateRowData(index, { maxProfit: e.target.value });
                              debouncedUpdateValues(
                                e.target.value,
                                rowData.maxLoss,
                                rowData.maxLossPerTrade,
                                rowData.qtyMultiplier,
                                rowData.maxOpenTrades,
                                rowData.sqOffTime,
                                index
                              );
                            }
                          }}
                          style={{
                            ...inputStyles, textAlign: "center",
                            padding: "8px",
                            color: "green",
                            width: "85px"
                          }}
                        />
                      </td>
                    ),
                    maxLoss: userProfColVis[ "maxLoss" ] && (
                      <td>
                        <input
                          type="number"
                          value={rowData.maxLoss}
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[eE+\-]/g,
                              ""
                            );
                          }}
                          onChange={(e) => {
                            const regex = /^\d{1,7}(\.\d{0,2})?$/;
                            if (regex.test(e.target.value) || e.target.value === "") {
                              updateRowData(index, { maxLoss: e.target.value });
                              debouncedUpdateValues(
                                rowData.maxProfit,
                                e.target.value,
                                rowData.maxLossPerTrade,
                                rowData.qtyMultiplier,
                                rowData.maxOpenTrades,
                                rowData.sqOffTime,
                                index
                              );
                            }
                          }}
                          style={{
                            ...inputStyles, textAlign: "center",
                            padding: "8px",
                            color: "red",
                            width: "85px"
                          }}
                        />
                      </td>
                    ),
                    profitLocking: userProfColVis[ "profitLocking" ] && (
                      <td style={{ padding: 0, position: "relative" }}>
                        <input
                          type="text"
                          value={rowData.profitLocking}
                          onChange={(e) =>
                            updateRowData(index, {
                              profitLocking: e.target.value,
                            })
                          }
                          style={{ padding: "8px", textAlign: "center" }}
                        />
                        <KeyboardArrowDownIcon
                          onClick={(e) => {
                            e.stopPropagation();
                            setPopupValues((prev) => ({ ...prev, index }));
                            togglePopup(index);
                          }}
                          style={{
                            cursor: "pointer",
                            position: "absolute",
                            right: "0px",
                            top: "8px",
                            fontSize: "16px",
                            color: "black",
                          }}
                        />
                      </td>
                    ),
                    qtyByExposure: userProfColVis[ "qtyByExposure" ] && (
                      <td>
                        <input
                          type="number"
                          value={rowData.qtyByExposure}
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[eE+\-]/g,
                              ""
                            );
                          }}
                          onChange={(e) =>
                            updateRowData(index, {
                              qtyByExposure: e.target.value,
                            })
                          }
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                        />
                      </td>
                    ),
                    qtyOnMaxLossPerTrade: userProfColVis[
                      "qtyOnMaxLossPerTrade"
                    ] && (
                        <td style={{ textAlign: "center", }}>
                          <input
                            type="checkbox"
                            name="Qty_on_Max_Loss_PerTrade"
                            checked={isChecked}
                            onChange={handleCheckboxChangeQty}
                            style={{ width: "80px" }}
                          />

                        </td>
                      ),
                    maxLossPerTrade: userProfColVis[ "maxLossPerTrade" ] && (
                      <td>
                        <input
                          type="number"
                          value={rowData.maxLossPerTrade}
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[eE+\-]/g,
                              ""
                            );
                          }}
                          onChange={(e) => {
                            const regex = /^\d{1,7}(\.\d{0,2})?$/;
                            const newMaxLossPerTrade = e.target.value;
                            if (regex.test(newMaxLossPerTrade) || newMaxLossPerTrade === "") {
                              updateRowData(index, { maxLossPerTrade: newMaxLossPerTrade });
                              if (
                                parseFloat(newMaxLossPerTrade) <= parseFloat(rowData.maxLoss) ||
                                parseFloat(rowData.maxLoss) === 0
                              ) {
                                debouncedUpdateValues(
                                  rowData.maxProfit,
                                  rowData.maxLoss,
                                  newMaxLossPerTrade,
                                  rowData.qtyMultiplier,
                                  rowData.maxOpenTrades,
                                  rowData.sqOffTime,
                                  index
                                );
                              } else {
                                handleMsg({
                                  msg: "max_Loss_Per_Trade value must be less than or equal to max_loss",
                                  logType: "MESSAGE",
                                  timestamp: `${new Date().toLocaleString()}`,
                                  user: rowData.userId,
                                });
                              }
                            }
                          }}
                          style={{
                            ...inputStyles, textAlign: "center",
                            padding: "8px",
                            color: "red",
                          }}
                        />
                      </td>
                    ),
                    maxOpenTrades: userProfColVis[ "maxOpenTrades" ] && (
                      <td>
                        <input
                          type="number"
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[eE+\-]/g,
                              ""
                            );
                          }}
                          value={rowData.maxOpenTrades}
                          onChange={(e) => {
                            const regex = /^\d{1,7}(\.\d{0,2})?$/;
                            const newValue = e.target.value;
                            if (regex.test(newValue) || newValue === "") {
                              updateRowData(index, { maxOpenTrades: newValue });
                              debouncedUpdateValues(
                                rowData.maxProfit,
                                rowData.maxLoss,
                                rowData.maxLossPerTrade,
                                rowData.qtyMultiplier,
                                newValue,
                                rowData.sqOffTime,
                                index
                              );
                            }
                          }}
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                        />
                      </td>
                    ),
                    qtyMultiplier: userProfColVis[ "qtyMultiplier" ] && (
                      <td>
                        <input
                          type="number"
                          onInput={(e) => {
                            e.target.value = e.target.value.replace(
                              /[eE+\-]/g,
                              ""
                            );
                          }}
                          value={
                            rowData.qtyMultiplier == null
                              ? "1"
                              : rowData.qtyMultiplier
                          }
                          onChange={(e) => {
                            const regex = /^\d{1,7}(\.\d{0,2})?$/;
                            const newValue = e.target.value;
                            if (regex.test(newValue) || newValue === "") {
                              updateRowData(index, { qtyMultiplier: newValue });
                              debouncedUpdateValues(
                                rowData.maxProfit,
                                rowData.maxLoss,
                                rowData.maxLossPerTrade,
                                newValue,
                                rowData.maxOpenTrades,
                                rowData.sqOffTime,
                                index
                              );
                            }
                          }}
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                        />
                      </td>
                    ),
                    mobile: userProfColVis[ "mobile" ] && (
                      <td>
                        <input
                          type="text"
                          value={rowData.mobile}
                          onChange={(e) =>
                            updateRowData(index, { mobile: e.target.value })
                          }
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                        />
                      </td>
                    ),
                    email: userProfColVis[ "email" ] && (
                      <td>
                        <input
                          type="email"
                          value={rowData.email}
                          onChange={(e) =>
                            updateRowData(index, { email: e.target.value })
                          }
                          onMouseEnter={(e) => {
                            if (e.target.scrollWidth > e.target.clientWidth) {
                              setHoverData({
                                content: rowData.email,
                                x: e.clientX + 10,
                                y: e.clientY + 10,
                              });
                            }
                          }}
                          onMouseLeave={() => setHoverData(null)}
                          style={{
                            ...inputStyles,
                            textAlign: "center",
                            padding: "8px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                        />
                      </td>
                    ),
                    net: userProfColVis[ "net" ] && (
                      <td>
                        <input
                          type="number"
                          value={rowData.net}
                          onChange={(e) =>
                            updateRowData(index, { net: e.target.value })
                          }
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                          disabled={rowData.inputDisabled}
                        />
                      </td>
                    ),
                    marketOrders: userProfColVis[ "marketOrders" ] && (
                      <td
                        onClick={() =>
                          openMarketOrdersModal(
                            rowData.userId,
                            rowData.broker,
                            rowData.name
                          )
                        }
                      >
                        <input type="text" value="" readOnly
                          style={{ ...inputStyles }}
                        />
                      </td>
                    ),
                    enableNRMLSqoff: userProfColVis[ "enableNRMLSqoff" ] && (
                      <td style={{ textAlign: "center" }}>
                        <select
                          className="custom-select"
                          style={{
                            ...inputStyles, width: "180px",
                            padding: "8px",
                            color: rowData.inputDisabled
                              ? "initial"
                              : "darkgray",
                          }}
                          value={rowData.enableNRMLSqoff ? "all" : "none"}
                          onChange={(e) =>
                            updateRowData(index, {
                              enableNRMLSqoff: e.target.value === "all",
                            })
                          }
                        >
                          <option value="none">None</option>
                          <option value="all">All</option>
                          <option value="today">Today</option>
                        </select>
                      </td>
                    ),
                    enableCNCSqoff: userProfColVis[ "enableCNCSqoff" ] && (
                      <td style={{ textAlign: "center" }}>
                        <input
                          type="checkbox"
                          checked={rowData.enableCNCSqoff}
                          style={{ width: "80px" }}
                          onChange={() =>
                            updateRowData(index, {
                              enableCNCSqoff: !rowData.enableCNCSqoff,
                            })
                          }
                        />
                      </td>
                    ),
                    exitOrderType: userProfColVis[ "exitOrderType" ] && (
                      <td style={{ textAlign: "center" }}>
                        <input
                          type="text"
                          value={rowData.exitOrderType}
                          disabled
                          style={{
                            ...inputStyles,
                            textAlign: "center", padding: "8px", color: "darkgray"
                          }}
                        />
                      </td>
                    ),
                    twoFA: userProfColVis[ "twoFA" ] && (
                      <td>
                        <input
                          type="text"
                          value={rowData.twoFA}
                          onChange={(e) =>
                            updateRowData(index, { twoFA: e.target.value })
                          }
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                        />
                      </td>
                    ),
                    maxLossWait: userProfColVis[ "maxLossWait" ] && (
                      <td>
                        <input
                          type="text"
                          value={rowData.maxLossWait}
                          onChange={(e) =>
                            updateRowData(index, {
                              maxLossWait: e.target.value,
                            })
                          }
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                        />
                      </td>
                    ),
                    tradingAuthorizationReq: userProfColVis[
                      "tradingAuthorizationReq"
                    ] && (
                        <td style={{ textAlign: "center" }}>
                          <input
                            type="checkbox"
                            checked={rowData.tradingAuthorizationReq}
                            style={{ width: "80px" }}
                            onChange={() =>
                              updateRowData(index, {
                                tradingAuthorizationReq:
                                  !rowData.tradingAuthorizationReq,
                              })
                            }
                          />
                        </td>
                      ),
                    commodityMargin: userProfColVis[ "commodityMargin" ] && (
                      <td>
                        <input
                          type="text"
                          value={rowData.commodityMargin}
                          onChange={(e) =>
                            updateRowData(index, {
                              commodityMargin: e.target.value,
                            })
                          }
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}

                        />
                      </td>
                    ),
                    apiUserDetails: userProfColVis[ "apiUserDetails" ] && (
                      <td>
                        <input
                          type="text"
                          value={rowData.apiUserDetails}
                          onChange={(e) =>
                            updateRowData(index, {
                              apiUserDetails: e.target.value,
                            })
                          }
                          onMouseEnter={(e) => {
                            if (e.target.scrollWidth > e.target.clientWidth) {
                              setHoverData({
                                content: rowData.apiUserDetails,
                                x: e.clientX + 10,
                                y: e.clientY + 10,
                              });
                            }
                          }}
                          onMouseLeave={() => setHoverData(null)}
                          style={{
                            ...inputStyles,
                            padding: "8px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                          disabled
                        />
                      </td>
                    ),
                    utilizedMargin: userProfColVis[ "utilizedMargin" ] && (
                      <td>
                        <input
                          type="number"
                          value={!isNaN(Number(rowData.utilizedMargin)) ? Number(rowData.utilizedMargin).toFixed(2) : "0.00"}
                          disabled
                          style={{ ...inputStyles, textAlign: "center", padding: "8px" }}
                        />
                      </td>
                    ),
                  };
                  return (
                    <tr key={index}>
                      {userProfSeq.map((colName, idx) => (
                        <React.Fragment key={idx}>
                          {userProfTD[ colName ]}
                        </React.Fragment>
                      ))}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          <div className="add_collapse">
            <button className="hiddenbutton button">Add</button>
            <button
              className="button"
              onClick={handleAddRow}
              style={{ zIndex: "0" }}
            >
              Add
            </button>
            <button
              style={{ zIndex: "0" }}
              onClick={() => {
                errorContainerRef.current.toggleCollapse();
              }}
              className="button"
              id="collapse"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>
          <MarketOrdersModal
            isOpen={isModalOpen}
            onClose={closeModalMarketOrders}
            selectedUserId={selectedUser.userId}
            selectedBroker={selectedUser.broker}
            selectedName={selectedUser.name}
            onSave={handleSaveSettings}
          />
          {showPopup && (
            <Draggable nodeRef={popupRef} >
              <div
                ref={popupRef}
                className="popupContainer"
                style={{
                  position: "fixed",
                  bottom: "6%",
                  right: "10%",
                  transform: "translate(-20%, 10%)",
                  backgroundColor: "#fff",
                  border: "1px solid #ccc",
                  padding: "20px",
                  width: "400px",
                  height: "480px",
                  zIndex: 1000,
                  borderRadius: "5px",
                  boxShadow: "0px 5px 15px rgba(0, 0, 0, 0.3)",
                }}
              >
                <div
                  className="popupContent"
                  style={{
                    border: "1px solid #d3d3d3",
                    padding: "8px",
                    borderRadius: "5px",
                  }}
                >
                  <h4
                    style={{
                      marginLeft: "0px",
                      fontFamily: "roboto",
                      fontSize: "14",
                    }}
                  >
                    Profit Locking
                  </h4>
                  <div
                    style={{
                      display: "flex",
                      marginTop: "10px",
                      marginRight: "10px",
                    }}
                  >
                    <div className="input-box">
                      <span
                        className="SLT"
                        style={{
                          display: "flex",
                          textAlign: "start",
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                        }}
                      >
                        If Profit Reaches
                      </span>
                      <input
                        className="number1"
                        type="number"
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                        }}
                        id="trail_tgt_0"
                        value={popupValues.profitReaches}
                        style={{
                          display: "flex",
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          outline: "none",
                          boxSizing: "border-box",
                          padding: "10px",
                        }}
                        onChange={(e) =>
                          handleInputChange("profitReaches", e.target.value)
                        }
                      />
                      <p
                        id="profitReachesError"
                        style={{
                          color: "red",
                          height: "18px",
                          marginTop: "4px",
                          marginLeft: "0px",
                        }}
                      ></p>
                    </div>
                    <div className="input-box" style={{ marginTop: "-5px" }}>
                      <span
                        className="SLT"
                        style={{
                          display: "flex",
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                          marginBottom: "-14px",
                          marginLeft: "10px",
                        }}
                      >
                        Lock Minimum <br />
                        Profit At
                      </span>
                      <input
                        className="number1"
                        type="number"
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                        }}
                        id="trail_tgt_1"
                        value={popupValues.lockMinimumProfit}
                        style={{
                          display: "flex",
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          outline: "none",
                          boxSizing: "border-box",
                          padding: "6px",
                          marginLeft: "10px",
                          marginTop: "8px",
                        }}
                        onChange={(e) =>
                          handleInputChange("lockMinimumProfit", e.target.value)
                        }
                      />
                      <p
                        id="lockProfitError"
                        style={{
                          color: "red",
                          height: "18px",
                          marginTop: "4px",
                          marginLeft: "10px",
                        }}
                      ></p>
                    </div>
                  </div>
                </div>
                <div
                  className="popupContent"
                  style={{
                    border: "1px solid #d3d3d3",
                    padding: "8px",
                    borderRadius: "5px",
                    marginTop: "10px",
                  }}
                >
                  <h4
                    style={{
                      marginLeft: "0px",
                      fontFamily: "roboto",
                      fontSize: "14",
                    }}
                  >
                    Profit Trailing
                  </h4>
                  <div
                    style={{
                      display: "flex",
                      marginTop: "10px",
                      marginRight: "10px",
                    }}
                  >
                    <div className="input-box">
                      <span
                        className="SLT"
                        style={{
                          display: "flex",
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                          textAlign: "start",
                        }}
                      >
                        Then Every Increase <br /> In Profit By
                      </span>
                      <input
                        className="number1"
                        type="number"
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                        }}
                        id="trail_tgt_2"
                        value={popupValues.increaseInProfit}
                        style={{
                          display: "flex",
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          outline: "none",
                          boxSizing: "border-box",
                          padding: "10px",
                        }}
                        onChange={(e) =>
                          handleInputChange("increaseInProfit", e.target.value)
                        }
                      />
                      <p
                        id="increaseInProfitError"
                        style={{
                          color: "red",
                          height: "18px",
                          marginTop: "4px",
                          marginLeft: "0px",
                        }}
                      ></p>
                    </div>
                    <div className="input-box">
                      <span
                        className="SLT"
                        style={{
                          display: "flex",
                          alignItems: "center",
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                          marginTop: "17px",
                          marginLeft: "10px",
                        }}
                      >
                        Trail Profit By
                      </span>
                      <input
                        className="number1"
                        type="number"
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                        }}
                        id="trail_tgt_3"
                        value={popupValues.trailProfitBy}
                        style={{
                          display: "flex",
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          outline: "none",
                          boxSizing: "border-box",
                          padding: "10px",
                          marginTop: "2px",
                          marginLeft: "10px",
                        }}
                        onChange={(e) =>
                          handleInputChange("trailProfitBy", e.target.value)
                        }
                      />
                      <p
                        id="trailProfitByError"
                        style={{
                          color: "red",
                          height: "18px",
                          marginTop: "4px",
                          marginLeft: "10px",
                        }}
                      ></p>
                    </div>
                  </div>
                </div>
                <div
                  style={{
                    fontFamily: "roboto",
                    fontSize: "12px",
                    marginTop: "10px",
                    color: "orange",
                  }}
                >
                  VALUES SHOULD BE IN RUPEES ONLY
                </div>

                <div
                  style={{
                    fontFamily: "roboto",
                    fontSize: "12px",
                    marginTop: "5px",
                    color: "#4661bd",
                    marginLeft: "0px",
                  }}
                >
                  LOCKING AND TRAILING CAN BE USED INDEPENDENTLY
                </div>
                <div
                  style={{
                    fontFamily: "roboto",
                    fontSize: "12px",
                    marginTop: "5px",
                    color: "green",
                    marginLeft: "0px",
                  }}
                >
                  TGT/ SL ON PER LOT BASIS IF TICKED WILL BE APPLICABLE HERE
                </div>
                <div>
                  <button
                    style={{
                      marginTop: "20px",
                      padding: "4px 8px",
                      marginLeft: "4px",
                      backgroundColor: "#28A745",
                      color: "white",
                      border: "none",
                      borderRadius: "5px",
                      cursor: "pointer",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                      transition: "background-color 0.3s",
                    }}
                    onClick={handleSetTrailTGT}
                  >
                    OK
                  </button>
                  <button
                    style={{
                      marginTop: "20px",
                      padding: "4px 8px",
                      marginLeft: "3px",
                      backgroundColor: "#DC3545",
                      color: "white",
                      border: "none",
                      borderRadius: "5px",
                      cursor: "pointer",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                      transition: "background-color 0.3s",
                      fontWeight: "normal",
                    }}
                    onClick={handleInputDelete}
                  >
                    DELETE
                  </button>
                  <button
                    onClick={() => setShowPopup(false)}
                    style={{
                      marginTop: "20px",
                      padding: "4px 7px",
                      marginLeft: "3px",
                      backgroundColor: "#007bff",
                      color: "white",
                      border: "none",
                      borderRadius: "5px",
                      cursor: "pointer",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                      transition: "background-color 0.3s",
                    }}
                  >
                    CLOSE
                  </button>
                </div>
              </div>
            </Draggable>
          )}
          {hoverData && (
            <div
              className="hover-box"
              style={{
                position: "absolute",
                left: hoverData.x,
                top: hoverData.y,
                backgroundColor: "black",
                color: "white",
                padding: "5px",
                borderRadius: "4px",
                fontSize: "12px",
                zIndex: 1000,
                pointerEvents: "none",
                whiteSpace: "nowrap",
                boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
              }}
            >
              {hoverData.content}
            </div>
          )}

          <OptimizedErrorContainer ref={errorContainerRef} />
        </div>
        <OptimizedRightNav />
      </div>

      <div>
        <Modal
          isOpen={isOpen}
          contentLabel="Example Modal"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1000,
            },
            content: {
              width: "300px",
              height: "160px",
              margin: "auto",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "white",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              padding: "0px 30px 0px 30px",
            },
          }}
        >
          <p
            style={{
              fontSize: "20px",
              marginBottom: "5px",
              padding: 0,
              lineHeight: 1.5,
              textAlign: "center",
            }}
          >
            Before logging out all accounts, you need to stop the trading!
          </p>
          <button
            style={{
              backgroundColor: "#FF0000",
              color: "white",
              border: "none",
              cursor: "pointer",
              padding: " 8px 10px",
              borderRadius: "7px",
              width: "40px",
              marginTop: "20px",
            }}
            onClick={closeModal}
          >
            OK
          </button>
        </Modal>
      </div>
      <div>
        <Modal
          isOpen={isOpenModal}
          contentLabel="Example Modal"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1000,
            },
            content: {
              width: "300px",
              height: "160px",
              margin: "auto",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "white",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              padding: "0px 30px 0px 30px",
            },
          }}
        >
          <span
            style={{
              fontSize: "20px",
              marginBottom: "5px",
              padding: 0,
              lineHeight: 1.5,
              textAlign: "center",
            }}
          >
            Your subscription is ending on <strong>{daysRemaining} </strong>,
            Please renew soon.
          </span>
          <div style={{ display: "flex", flexDirection: "row", gap: "20px" }}>
            <button
              style={{
                backgroundColor: "blue",
                color: "white",
                border: "none",
                cursor: "pointer",
                padding: " 8px 10px",
                borderRadius: "7px",
                width: "70px",
                marginTop: "20px",
              }}
              onClick={HandleRedirect}
            >
              RENEW
            </button>
            <button
              style={{
                backgroundColor: "green",
                color: "white",
                border: "none",
                cursor: "pointer",
                padding: " 8px 10px",
                borderRadius: "7px",
                width: "70px",
                marginTop: "20px",
                marginRight: "-30px",
              }}
              onClick={closeModalForExpiry}
            >
              OK
            </button>
          </div>
        </Modal>
      </div>

      {filterPopup && filterPopup !== "action" && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "3px 0 3px 3px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            zIndex: 1000,
            minWidth: "150px",
            maxWidth: "200px"
          }}
        >
          <div style={{ paddingRight: "1px" }}>
            <div style={{
              borderBottom: "1px solid #e0e0e0",
              paddingBottom: "1px",
              marginBottom: "1px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            }}>
              <span style={{ fontWeight: "bold", color: "#1976d2" }}>
                Filter: {filterPopup}
              </span>
              <span style={{
                fontSize: "12px",
                color: "#666",
                backgroundColor: "#f5f5f5",
                padding: "2px 6px",
                borderRadius: "4px"
              }}>
                {getDynamicUniqueValues(filterPopup).length} items
              </span>
            </div>

            {getDynamicUniqueValues(filterPopup).length > 10 && (
              <div style={{ marginBottom: "1px", paddingRight: "1px" }}>
                <input
                  type="text"
                  placeholder="Search..."
                  style={{
                    // width: "100%",
                    maxWidth: "100px",
                    padding: "3px 3px",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                    fontSize: "12px"
                  }}
                />
              </div>
            )}

            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "1px 1px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "1px",
                backgroundColor: "#f8f9fa",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[ filterPopup ] &&
                  getDynamicUniqueValues(filterPopup).every((opt) =>
                    tempFilters[ filterPopup ].includes(opt)
                  )
                }
                onChange={() => handleSelectAll(filterPopup)}
                style={{ marginRight: "8px" }}
              />
              <span>Select All</span>
            </label>

            <div
              style={{
                maxHeight: "150px",
                overflowY: "auto",
                margin: "0",
                scrollbarWidth: "thin",
                scrollbarColor: "#888 #f1f1f1",
                border: "1px solid #eee",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              {getDynamicUniqueValues(filterPopup).length > 0 ? (
                getDynamicUniqueValues(filterPopup).map((item) => {
                  const isSelected = tempFilters[ filterPopup ]?.includes(item) || false;
                  return (
                    <div
                      key={item}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "1px 2px",
                        cursor: "pointer",
                        margin: "0",
                        borderBottom: "1px solid #f0f0f0",
                        backgroundColor: isSelected ? "#f0f7ff" : "transparent",
                        transition: "background-color 0.2s"
                      }}
                      onClick={() => handleFilterChange(filterPopup, item)}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => { }}
                        style={{ marginRight: "8px" }}
                      />
                      <span style={{
                        color: isSelected ? "#1976d2" : "#444",
                        fontWeight: isSelected ? "500" : "normal"
                      }}>
                        {item || "(Empty)"}
                      </span>
                    </div>
                  );
                })
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "10px",
                    color: "#666",
                    fontStyle: "italic",
                    minHeight: "40px",
                    justifyContent: "center"
                  }}
                >
                  <span>No options available</span>
                </div>
              )}
            </div>

            <div style={{
              fontSize: "12px",
              color: "#666",
              margin: "1px 0",
              display: "flex",
              justifyContent: "space-between",
              marginRight: "1px"
            }}>
              <span>
                {tempFilters[ filterPopup ]?.length || 0} of {getDynamicUniqueValues(filterPopup).length} selected
              </span>
              {tempFilters[ filterPopup ]?.length > 0 && (
                <span
                  style={{ color: "#1976d2", cursor: "pointer" }}
                  onClick={() => setTempFilters({ ...tempFilters, [ filterPopup ]: [] })}
                >
                  Clear selection
                </span>
              )}
            </div>
          </div>

          <div
            style={{
              marginTop: "3px",
              display: "flex",
              gap: "10px",
              justifyContent: "center",
              paddingRight: "3px"
            }}
          >
            <button
              onClick={handleCancelFilter}
              style={{
                padding: "3px 3px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                background: "#f8f9fa",
                cursor: "pointer",
                color: "#333",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilter}
              style={{
                padding: "3px 3px",
                border: "none",
                borderRadius: "4px",
                background: "#1976d2",
                color: "white",
                cursor: "pointer",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Apply Filter
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default UserProfiles;



