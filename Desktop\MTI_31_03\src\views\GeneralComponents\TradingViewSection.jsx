import React from "react";

const TradingViewSection = ({
    isWebhookEnabled,
    setWebhookEnabled,
    webhookUrl,
    fetchWebhookUrl,
    strategy,
    setStrategy,
    filteredPortfolios,
    isPluginEnabled,
    setPluginEnabled,
    onConfigure
}) => (
    <div className="trading-view-section" style={{
        padding: "10px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box"
    }}>

        <div style={{ marginBottom: "5px" }}>
            <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <input
                    type="checkbox"
                    checked={isWebhookEnabled}
                    onChange={() => setWebhookEnabled(prev => !prev)}
                    style={{ width: "16px", height: "16px" }}
                />
                <span style={{ fontSize: "16px", fontWeight: "600" }}>Enable TradingView WebHooks</span>
            </label>
        </div>

        <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", fontSize: "14px", marginBottom: "8px", fontWeight: "600" }}>
                WebHook URL
                <span style={{ color: "#e53e3e", fontWeight: "normal", marginLeft: "5px" }}>
                    (Sensitive - Do not share)
                </span>
            </label>
            <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                <input
                    type="text"
                    value={webhookUrl}
                    readOnly
                    style={{
                        flex: 1,
                        padding: "8px",
                        borderRadius: "6px",
                        border: "1px solid #ddd",
                        fontSize: "14px",
                        background: "#f5f5f5",
                        color: "#666"
                    }}
                />
                <button
                    onClick={fetchWebhookUrl}
                    style={{
                        padding: "8px 20px",
                        background: "#d8e1ff",
                        color: "#4661bd",
                        border: "none",
                        borderRadius: "6px",
                        cursor: "pointer",
                        fontSize: "14px",
                        fontWeight: "600",
                        transition: "background 0.3s ease"
                    }}
                    onMouseEnter={(e) => e.target.style.background = "#c7d2fe"}
                    onMouseLeave={(e) => e.target.style.background = "#d8e1ff"}
                >
                    Get URL
                </button>
            </div>
        </div>

        <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", fontSize: "14px", marginBottom: "8px", fontWeight: "600" }}>
                Default Portfolio
            </label>
            <select
                value={strategy}
                onChange={(e) => setStrategy(e.target.value)}
                style={{
                    width: "300px",
                    padding: "8px",
                    borderRadius: "6px",
                    border: "1px solid #ddd",
                    fontSize: "14px",
                    background: "#fff"
                }}
            >
                {filteredPortfolios.length === 0 ? (
                    <option value="">No portfolios available</option>
                ) : (
                    filteredPortfolios.map((item) => (
                        <option key={item.portfolio_name} value={item.portfolio_name}>
                            {item.portfolio_name}
                        </option>
                    ))
                )}
            </select>
        </div>

        <div style={{ marginBottom: "30px" }}>
            <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <input
                    type="checkbox"
                    checked={isPluginEnabled}
                    onChange={() => setPluginEnabled(prev => !prev)}
                    style={{ width: "16px", height: "16px" }}
                />
                <span style={{ fontSize: "16px", fontWeight: "600" }}>Enable TradingView Plugin</span>
            </label>
            <div style={{ marginTop: "15px", background: "#f8fafc", padding: "20px", borderRadius: "8px" }}>
                <p style={{ fontSize: "14px", marginBottom: "10px" }}>
                    <a href="#" style={{ color: "#4661bd", textDecoration: "none", fontWeight: "600" }}>
                        Download Chrome Plugin
                    </a>
                </p>
                <ul style={{ paddingLeft: "20px", fontSize: "14px", color: "#666", margin: 0 }}>
                    <li style={{ marginBottom: "8px" }}>Please close the CHROME browser, if running.</li>
                    <li style={{ marginBottom: "8px" }}>Please click on the link above, which will open the Plugin Page in Chrome.There, you need to
                        click on the file IABTV 2.4.crx and then click "Add Extension" when asked by Chrome.</li>
                    <li>After installing the Plugin, remember to click the Configure button below.</li>
                </ul>
            </div>
        </div>

        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "15px"
        }}>
            <button
                onClick={onConfigure}
                style={{
                    padding: "12px 25px",
                    background: "#4661bd",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "600",
                    transition: "background 0.3s ease"
                }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Configure
            </button>
            <button style={{
                padding: "12px 25px",
                background: "#fff",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "all 0.3s ease"
            }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default TradingViewSection;