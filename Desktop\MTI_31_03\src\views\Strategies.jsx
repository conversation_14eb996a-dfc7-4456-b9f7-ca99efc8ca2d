import React, { useState, useRef, useEffect, useMemo } from "react";
import {
  OptimizedMarketIndex,
  OptimizedLeftNav,
  OptimizedRightNav,
  OptimizedErrorContainer,
  OptimizedTopNav,
} from "../components/Layout/OptimizedComponenets";
import filterIcon from "../assets/newFilter.png";
import Log from "../assets/log.png";
import Logout from "../assets/logout.png";
import { useSelector, useDispatch } from "react-redux";
import { setStrategies, setAllSeq, setAllVis, setConsoleMsgs } from "../store/slices";
import MarketOrdersModal from "../components/MarketOrdersModal";
import Cookies from "universal-cookie";
import TableHeaderWithFilter from "../components/TableHeaderWithFilter";
import Modal from "react-modal";
import Draggable from "react-draggable";
import TimePicker from "react-time-picker";
import { Stop as StopIcon, PlayArrow as PlayArrowIcon, Delete as DeleteIcon } from "@mui/icons-material";
import "react-time-picker/dist/TimePicker.css";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import useClickOutside from "../hooks/useClickOutside";
import useDebounce from '../hooks/useDebounce';
import { fetchWithAuth } from "../utils/api";

const cookies = new Cookies();

export const inputStyles = {
  borderRadius: "3px",
  width: "100%",
  minWidth: "20px",
  maxWidth: "100%",
  boxSizing: "border-box",
  fontSize: "14px",
  textAlign: "center",
};

const columnWidths = {
  // Fixed columns
  "Action": "110px",
  "Strategy Label": "140px",

  // Columns with short text/numbers
  "P L": "80px",
  "Trade Size": "80px",
  "Duplicate Signal Prevention": "100px",
  "Max Profit": "80px",
  "Max Loss": "80px",
  "Delay Between Users": "100px",
  "Hold Sell Seconds": "80px",
  "Entry Retry Count": "80px",
  "Entry Retry Wait Seconds": "80px",
  "Exit Retry Count": "80px",
  "Exit Retry Wait Seconds": "80px",
  "Exit Max Wait Seconds": "80px",
  "Delta": "70px",
  "Theta": "70px",
  "Vega": "70px",

  // Boolean columns (checkboxes)
  "Unique ID Req for Order": "90px",
  "Cancel Previous Open Signal": "90px",
  "Stop Reverse": "90px",
  "Part Multi Exists": "90px",
  "Entry Order Retry": "90px",
  "Exit Order Retry": "90px",
  "Sq Off Done": "90px",

  // Time pickers
  "Open Time": "110px",
  "Close Time": "110px",
  "Sq Off Time": "110px",
  "Max Loss Wait Time": "110px",

  // Icon columns
  "Manual Exit": "80px",
  "Market Orders": "90px",

  // Dropdown columns
  "Allowed Trades": "100px",

  // Columns that need more space
  "Trading Account": "200px",
  "Profit Locking": "120px",
};

const styles = `
  .middle-main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .table-container {
    flex: 1;
    overflow: auto;
    height: calc(92vh - 100px);
    position: relative;
    // scrollbar-width: thin;
    // scrollbar-color: #d8d8d8 #f4f4f4;
  }
  .user-profiles-table {
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
    width: auto;
  }
  .user-profiles-table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #D8E1FF;
  }
  .user-profiles-table th {
    font-size: 15px;
    // padding: 4px 3px;
    text-align: center; /* Center-align headers */
    border-bottom: 1px solid #ddd;
    white-space: normal; /* Allow text wrapping */
    vertical-align: middle;
    height: auto;
    line-height: 1.1;
  }
  .user-profiles-table td {
    text-align: center; /* Center-align cell content */
    vertical-align: middle;
  }
  /* Fixed column styles */
  .fixed-column-1 {
    position: sticky !important;
    left: 0 !important;
    z-index: 5 !important;
    box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1) !important;
  }

  .fixed-column-2 {
    position: sticky !important;
    left: 110px !important; /* Width of the first column */
    z-index: 5 !important;
    box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1) !important;
  }

  .fixed-column-3 {
    position: sticky !important;
    left: 250px !important; /* Width of the first column (110px) + second column (140px) */
    z-index: 5 !important;
    box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1) !important;
    min-width: 80px !important; /* Minimum width for P&L column */
    width: 80px !important; /* Fixed width for P&L column */
  }

  /* Ensure inputs and other content inside fixed columns don't overflow */
  .fixed-column-1 input, .fixed-column-2 input, .fixed-column-3 input,
  .fixed-column-1 select, .fixed-column-2 select, .fixed-column-3 select,
  .fixed-column-1 div, .fixed-column-2 div, .fixed-column-3 div {
    position: relative;
    z-index: 6 !important;
  }

  .fixed-column-header-1 {
    position: sticky !important;
    left: 0 !important;
    z-index: 15 !important;
    background-color: #D8E1FF !important;
  }

  .fixed-column-header-2 {
    position: sticky !important;
    left: 110px !important; /* Width of the first column */
    z-index: 15 !important;
    background-color: #D8E1FF !important;
  }

  .fixed-column-header-3 {
    position: sticky !important;
    left: 250px !important; /* Width of the first column (110px) + second column (140px) */
    z-index: 15 !important;
    background-color: #D8E1FF !important;
    min-width: 80px !important; /* Minimum width for P&L column */
    width: 80px !important; /* Fixed width for P&L column */
  }

  /* Background colors for fixed columns to prevent see-through */
  .user-profiles-table tbody tr:nth-child(even) td.fixed-column-1,
  .user-profiles-table tbody tr:nth-child(even) td.fixed-column-2,
  .user-profiles-table tbody tr:nth-child(even) td.fixed-column-3 {
    background-color: #E8E6E6 !important;
  }

  .user-profiles-table tbody tr:nth-child(odd) td.fixed-column-1,
  .user-profiles-table tbody tr:nth-child(odd) td.fixed-column-2,
  .user-profiles-table tbody tr:nth-child(odd) td.fixed-column-3 {
    background-color: #FFFFFF !important;
  }
  .user-profiles-table tbody tr:nth-child(even),
  .user-profiles-table tbody tr:nth-child(even) input,
  .user-profiles-table tbody tr:nth-child(even) select {
    background-color: #E8E6E6;
  }
  .user-profiles-table tbody tr:nth-child(odd),
  .user-profiles-table tbody tr:nth-child(odd) input,
  .user-profiles-table tbody tr:nth-child(odd) select {
    background-color: #FFFFFF;
  }
  .filter-icon {
    margin-left: 2px; /* Minimal margin */
    cursor: pointer;
    font-size: 14px;
    vertical-align: middle;
  }
  .tooltip-container {
    position: relative;
    display: inline-block;
    margin: 0 2px; /* Tighten spacing between icons */
  }
  .tooltiptext {
    visibility: hidden;
    width: 80px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 2px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
  }
  .tooltip-container:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
  }
  .logout_icon {
    cursor: pointer;
  }
  .custom-time-picker {
    padding: 3px;
    width: 100%;
    max-width: 100px;
  }
  .custom-select {
    padding: 3px;
    width: 100%;
  }

  /* Make time picker more compact */
  .react-time-picker__wrapper {
    border: 1px solid #d3d3d3;
    border-radius: 3px;
    padding: 0 2px;
  }

  .react-time-picker__inputGroup {
    min-width: auto;
    padding: 0 2px;
  }

  .react-time-picker__inputGroup__input {
    padding: 0;
    min-width: 12px;
  }

  input[readonly] {
    background-color: #f0f0f0;
  }
  .action-icons {
    display: flex;
    justify-content: center; /* Center the icons */
    align-items: center;
    gap: 2px; /* Controlled spacing between icons */
  }
`;

function Strategies() {
  const tableRef = useRef(null);
  const popupRef = useRef(null);
  const filterPopupRef = useRef(null);
  const errorContainerRef = useRef(null);
  const mainUser = cookies.get("USERNAME");
  const { collapsed } = useSelector((state) => state.collapseReducer);
  const dispatch = useDispatch();

  const { strategies: data } = useSelector((state) => state.strategyReducer);
  const { brokers } = useSelector((state) => state.brokerReducer);
  const { portfolios: portfolioDetails } = useSelector((state) => state.portfolioReducer);

  const [ strategies, setStrategiesData ] = useState(data);
  const [ clearedCells, setClearedCells ] = useState([]);
  const [ actionFilter, setActionFilter ] = useState("all");
  const [ filterPopup, setFilterPopup ] = useState(null);
  const [ filters, setFilters ] = useState({});
  const [ tempFilters, setTempFilters ] = useState({});
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 });
  const [ msgs, setMsgs ] = useState([]);
  const [ isErrorDisplayed, setIsErrorDisplayed ] = useState(false);
  const [ isModalOpen, setModalOpen ] = useState(false);
  const [ hoverData, setHoverData ] = useState(null);
  const [ selectedTradingAccount, setSelectedTradingAccount ] = useState(null);
  const [ clickedRowIndex, setClickedRowIndex ] = useState(-1);
  const [ selectAllChecked, setSelectAllChecked ] = useState(false);
  const [ dataNew, setDataNew ] = useState([]);
  const [ filteredDataNew, setFilteredDataNew ] = useState([]);
  const [ isPopUpDataChanged, setIsPopUpDataChanged ] = useState(false);
  const [ isdropDownOpenUserID, setIsdropDownOpenUserID ] = useState(false);
  const [ isdropDownOpenBroker, setIsdropDownOpenBroker ] = useState(false);
  const [ isdropDownOpenMargin, setIsdropDownOpenMargin ] = useState(false);
  const [ isdropDownOpenAlias, setIsdropDownOpenAlias ] = useState(false);
  const [ userIDSelected, setUserIDSelected ] = useState([]);
  const [ aliasSelected, setAliasSelected ] = useState([]);
  const [ brokerSelected, setBrokerSelected ] = useState([]);
  const [ marginSelected, setMarginSelected ] = useState([]);
  const [ showModal, setShowModal ] = useState(false);
  const [ errorMessage, setErrorMessage ] = useState("");
  const [ showPopup, setShowPopup ] = useState(false);
  const [ popupValues, setPopupValues ] = useState({
    index: "",
    profitReaches: "",
    lockMinimumProfit: "",
    increaseInProfit: "",
    trailProfitBy: "",
  });
  const [ isModalOpenStratgey, setIsModalOpenStrategy ] = useState(false);
  const [ selectedUser, setSelectedUser ] = useState({
    userId: "",
    broker: "",
    name: "",
  });

  const openMarketOrdersModal = (userId, broker, name) => {
    setSelectedUser({ userId, broker, name });
    setIsModalOpenStrategy(true);
  };

  const closeModalMarketOrders = () => {
    setIsModalOpenStrategy(false);
  };

  const handleSaveSettings = (settings) => {
    console.log("Saved settings:", settings);
    closeModalMarketOrders();
  };

  const filteredRows = useMemo(() => {
    let result = Array.isArray(strategies) ? strategies : [];
    if (actionFilter !== "all") {
      result = result.filter((row) =>
        actionFilter === "checked" ? row.enabled : !row.enabled
      );
    }
    if (Object.keys(filters).length > 0) {
      result = result.filter((row) =>
        Object.keys(filters).every((col) => {
          const key = col.replace(/\s/g, "");
          return filters[ col ]?.length > 0
            ? filters[ col ].includes(String(row[ key ]).toLowerCase())
            : true;
        })
      );
    }
    return result;
  }, [ strategies, actionFilter, filters ]);

  // Get unique values for a column, considering whether it's the first filter or not
  const getDynamicUniqueValues = (column) => {
    const key = column.replace(/\s/g, "");

    // For the first filter or if no filters are applied yet, show all available values from strategies
    const isFirstFilter = Object.keys(filters).length === 0 ||
      (Object.keys(filters).length === 1 && filters[ column ]);

    // Use the original data (strategies) for the first filter column
    // For subsequent filters, use the already filtered data
    const sourceData = isFirstFilter ? strategies : filteredRows;

    return Array.from(
      new Set(sourceData.map((row) => String(row[ key ] || "").toLowerCase()))
    ).filter(Boolean);
  };

  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  const handleFilterToggle = (column, event) => {
    const { top, left, height } = event.target.getBoundingClientRect();
    setFilterPopup(filterPopup === column ? null : column);
    setPopupPosition({ top: top + height, left });
    setTempFilters(filters);
  };

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) =>
      selectedOptions.includes(opt)
    );
    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [ column ]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [ column ]: [ ...currentOptions ] }));
    }
  };

  const handleApplyFilter = () => {
    const newFilters = { ...filters, [ filterPopup ]: tempFilters[ filterPopup ] || [] };
    setFilters(newFilters);
    setFilterPopup(null);
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  const stPageCols = [
    "Action",
    "Strategy Label",
    "P L",
    "Manual Exit",
    "Trade Size",
    "Duplicate Signal Prevention",
    "Open Time",
    "Close Time",
    "Sq Off Time",
    "Trading Account",
    "Max Profit",
    "Max Loss",
    "Max Loss Wait Time",
    "Profit Locking",
    "Market Orders",
    "Delay Between Users",
    "Unique ID Req for Order",
    "Cancel Previous Open Signal",
    "Stop Reverse",
    "Part Multi Exists",
    "Hold Sell Seconds",
    "Allowed Trades",
    "Entry Order Retry",
    "Entry Retry Count",
    "Entry Retry Wait Seconds",
    "Exit Order Retry",
    "Exit Retry Count",
    "Exit Retry Wait Seconds",
    "Exit Max Wait Seconds",
    "Sq Off Done",
    "Delta",
    "Theta",
    "Vega",
  ];

  const allSeqState = useSelector((state) => state.allSeqReducer);
  const allVisState = useSelector((state) => state.allVisReducer);

  const [ stColVis, setStColVis ] = useState(allVisState.strategiesVis);
  const [ strategiesSeq, setStrategiesSeq ] = useState(allSeqState.strategiesSeq);
  const [ stColsSelectedALL, setStColsSelectedALL ] = useState(false);


  const stPageColSelectAll = () => {
    setStColsSelectedALL((prev) => !prev);
    stPageCols.map((stPageCol) => {
      setStColVis((prev) => ({
        ...prev,
        [ stPageCol ]: stColsSelectedALL,
      }));
    });
  };


  useEffect(() => {
    setStrategiesSeq(allSeqState.strategiesSeq);
    setStColVis((prev) => ({
      ...prev,
      ...Object.fromEntries(
        stPageCols.map((col) => [ col, allSeqState.strategiesSeq.includes(col) ])
      ),
    }));
  }, []);

  useEffect(() => {
    setStrategiesSeq(allSeqState.strategiesSeq);
    setStColVis(() => {
      const colVis = {};
      Object.keys(stColVis).map((col) => {
        if (allSeqState.strategiesSeq.includes(col)) {
          colVis[ col ] = true;
        } else {
          colVis[ col ] = false;
        }
      });

      return { ...colVis };
    });
  }, []);


  useEffect(() => {
    dispatch(
      setAllVis({
        ...allVisState,
        strategiesVis: stColVis,
      })
    );

    if (new Set(Object.values(stColVis)).size === 1) {
      if (Object.values(stColVis).includes(true)) {
        setStrategiesSeq(stPageCols);
      } else {
        setStrategiesSeq([]);
      }
    }
  }, [ stColVis ]);

  useEffect(() => {
    dispatch(
      setAllSeq({
        ...allSeqState,
        strategiesSeq: strategiesSeq,
      })
    );
  }, [ strategiesSeq ]);

  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;
      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.strategy &&
        lastMsg.portfolio === Msg.portfolio
      ) {
        dispatch(
          setConsoleMsgs({ consoleMsgs: [ Msg, ...previousConsoleMsgs.slice(1) ] })
        );
      } else {
        dispatch(setConsoleMsgs({ consoleMsgs: [ Msg, ...previousConsoleMsgs ] }));
      }
    });
  };

  const handleClearLogs = () => {
    if (msgs.length === 0) return;
    setMsgs([]);
  };

  useEffect(() => {
    setStrategiesData(Array.isArray(data) ? data : []);
  }, [ data ]);

  const addNewRow = () => {
    const mandatoryFields = [ "StrategyLabel", "TradingAccount" ];
    if (data.length === 0) {
      const newRow = {
        enabled: true,
        ManualSquareOff: "",
        StrategyLabel: "",
        PL: "0",
        TradeSize: "0",
        DuplicateSignalPrevention: "0",
        OpenTime: "00:00:00",
        CloseTime: "00:00:00",
        SqOffTime: "00:00:00",
        TradingAccount: "",
        MaxProfit: "0",
        MaxLoss: "0",
        MaxLossWaitTime: "00:00:00",
        ProfitLocking: "0",
        DelayBetweenUsers: "0",
        UniqueIDReqforOrder: "",
        CancelPreviousOpenSignal: "",
        StopReverse: "",
        PartMultiExists: "",
        HoldSellSeconds: "00",
        AllowedTrades: true,
        EntryOrderRetry: false,
        EntryRetryCount: "0",
        EntryRetryWaitSeconds: "00",
        ExitOrderRetry: false,
        ExitRetryCount: "0",
        ExitRetryWaitSeconds: "00",
        ExitMaxWaitSeconds: "00",
        SqOffDone: "",
        Delta: "0",
        Theta: "0",
        Vega: "0",
      };
      dispatch(setStrategies({ strategies: [ ...data, newRow ] }));
      return;
    }

    const lastRow = data[ data.length - 1 ];
    const allFieldsFilled = mandatoryFields.every((field) => lastRow[ field ]);

    if (allFieldsFilled) {
      setIsErrorDisplayed(false);
      const newStrategyLabel = lastRow[ "StrategyLabel" ];
      const isUnique =
        data.length < 2 ||
        data
          .slice(0, -1)
          .every((row) => row[ "StrategyLabel" ] !== newStrategyLabel);

      if (isUnique) {
        setIsErrorDisplayed(false);
        const newRow = {
          enabled: true,
          ManualSquareOff: "",
          StrategyLabel: "",
          PL: "0",
          TradeSize: "0",
          DuplicateSignalPrevention: "0",
          OpenTime: "00:00:00",
          CloseTime: "00:00:00",
          SqOffTime: "00:00:00",
          TradingAccount: "",
          MaxProfit: "0",
          MaxLoss: "0",
          MaxLossWaitTime: "00:00:00",
          ProfitLocking: "0",
          DelayBetweenUsers: "0",
          UniqueIDReqforOrder: "",
          CancelPreviousOpenSignal: "",
          StopReverse: "",
          PartMultiExists: "",
          HoldSellSeconds: "00",
          AllowedTrades: true,
          EntryOrderRetry: false,
          EntryRetryCount: "0",
          EntryRetryWaitSeconds: "00",
          ExitOrderRetry: false,
          ExitRetryCount: "0",
          ExitRetryWaitSeconds: "00",
          ExitMaxWaitSeconds: "00",
          SqOffDone: "",
          Delta: "0",
          Theta: "0",
          Vega: "0",
        };
        dispatch(setStrategies({ strategies: [ ...data, newRow ] }));
      } else {
        if (!isErrorDisplayed) {
          handleMsg({
            msg: "Please enter a unique strategy label before adding a new row.",
            logType: "WARNING",
            timestamp: `${new Date().toLocaleString()}`,
            strategy: "strategy",
          });
          setIsErrorDisplayed(true);
        }
      }
    } else {
      if (!isErrorDisplayed) {
        handleMsg({
          msg: "Please enter strategy label and trading account before adding a new row.",
          logType: "ERROR",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: "strategy",
        });
        setIsErrorDisplayed(true);
      }
    }
  };

  const handleInputChange = (index, fieldName, value) => {
    const newData = [ ...strategies ];
    const newStrategyLabel =
      fieldName === "StrategyLabel" ? value.toUpperCase().trim() : value;

    if (fieldName === "StrategyLabel" && newStrategyLabel !== "") {
      const isAlphaOnly = /^[a-zA-Z]+$/.test(newStrategyLabel);
      const isAlphaNumeric = /^[a-zA-Z0-9]+$/.test(newStrategyLabel);
      const isNumericOnly = /^[0-9]+$/.test(newStrategyLabel);

      if (!isAlphaOnly && (!isAlphaNumeric || isNumericOnly)) {
        newData[ index ] = { ...newData[ index ], [ fieldName ]: "" };
        setClearedCells((prev) => [ ...prev, index ]);
        handleMsg({
          msg: "Strategy Label must start with alphabets.",
          logType: "WARNING",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: "strategy",
        });
        return;
      }

      const isUnique =
        newData.length < 2 ||
        newData
          .slice(0, -1)
          .every((row) => row[ fieldName ].toUpperCase() !== newStrategyLabel);

      if (!isUnique) {
        newData[ index ] = { ...newData[ index ], [ fieldName ]: "" };
        setClearedCells((prev) => [ ...prev, index ]);
        handleMsg({
          msg: "Strategy tag must be unique",
          logType: "WARNING",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: "strategy",
        });
        return;
      } else {
        setClearedCells((prev) => prev.filter((i) => i !== index));
      }
    }

    newData[ index ] = { ...newData[ index ], [ fieldName ]: newStrategyLabel };
    dispatch(setStrategies({ strategies: newData }));
  };

  const updateActionProperty = (index, property, value) => {
    const newData = strategies.map((row, i) =>
      i === index ? { ...row, [ property ]: value } : row
    );
    dispatch(setStrategies({ strategies: newData }));
  };

  const handleStrategyRowDelete = async (index) => {
    try {
      const strategyLabel = data[ index ]?.StrategyLabel;
      const tradingAccount = data[ index ]?.TradingAccount;
      const portfolios = portfolioDetails.filter(
        (p) => p.strategy === strategyLabel
      );
      const names = portfolios.map((p) => p.portfolio_name);

      if (portfolios.length !== 0) {
        setErrorMessage(
          `Please delete the portfolios linked with ${strategyLabel} strategy: ${names.join(
            ", "
          )}. `
        );
        setShowModal(true);
        return;
      }

      if (!mainUser || !strategyLabel || !tradingAccount) {
        const updatedRows = data.filter((_, i) => i !== index);
        dispatch(setStrategies({ strategies: updatedRows }));
        handleMsg({
          msg: `Row Deleted Successfully`,
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: "strategy",
        });
        return;
      }

      const response = await fetchWithAuth(
        `/api/delete_strategy_tag/${mainUser}/${strategyLabel}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Something bad happened. Please try again"
        );
      }

      const updatedRows = data.filter((_, i) => i !== index);
      dispatch(setStrategies({ strategies: updatedRows }));
      const responseData = await response.json();
      handleMsg({
        msg: `${responseData.message} - ${strategyLabel}`,
        logType: "MESSAGE",
        timestamp: `${new Date().toLocaleString()}`,
        strategy: strategyLabel,
      });
    } catch (error) {
      handleMsg({
        msg: error.message,
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
        strategy: "strategy",
      });
    }
  };

  const handleMouseEnter = (content, event) => {
    setHoverData({
      content,
      x: event.clientX + 10,
      y: event.clientY + 10,
    });
  };

  const handleMouseLeave = () => setHoverData(null);


  const handleCellClick = (tradingAccountData, index) => {
    if (!data[ index ].StrategyLabel) {
      handleMsg({
        msg: "Please enter the Strategy Label",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
        strategy: "strategy",
      });
    } else {
      setSelectedTradingAccount({
        ...tradingAccountData,
        StrategyLabel: data[ index ].StrategyLabel,
      });
      setModalOpen(true);
      setClickedRowIndex(index);

      const checkedBoxes = data[ index ].TradingAccount.split(",").map((item) =>
        item.trim()
      );

      // Fetch data immediately and set initial state
      const fetchData = async () => {
        try {
          const response = await fetchWithAuth(`/api/get_startegy_account/${mainUser}`, {
            method: "POST",
          });

          if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

          const fetchedData = await response.json();
          const newData = fetchedData.data.map((account) => ({
            Select: checkedBoxes.includes(account.broker_id.trim()),
            UserID: account.broker_id,
            Broker: account.broker,
            Alias: account.display_name,
            Multiplier: tradingAccountData
              ? account.multiplier[ tradingAccountData.StrategyLabel ] || "1"
              : "1",
            Margin:
              brokers.find((row) => account.broker_id === row.userId)?.availableMargin || 0,
          }));
          setDataNew(newData);
          setFilteredDataNew(newData);
          setSelectAllChecked(newData.every((item) => item.Select));
        } catch (error) {
          console.error("Error fetching strategy accounts:", error);
        }
      };

      fetchData();
    }
  };

  const handleCloseModal = () => {
    setSelectedTradingAccount(null);
    setModalOpen(false);
    setDataNew([]);
    setFilteredDataNew([]);
    setSelectAllChecked(false);
    setUserIDSelected([]);
    setAliasSelected([]);
    setBrokerSelected([]);
    setMarginSelected([]);
    setIsPopUpDataChanged(false);
  };


  const handleSelectAllUsers = () => {
    const newSelectAllValue = !selectAllChecked;
    setSelectAllChecked(newSelectAllValue);
    const updatedData = dataNew.map((item) => ({
      ...item,
      Select: newSelectAllValue,
    }));
    setDataNew(updatedData);
    setFilteredDataNew(updatedData);
    setIsPopUpDataChanged(true);
  };

  const handleCheckboxChange = (index) => {
    const updatedData = [ ...dataNew ];
    updatedData[ index ].Select = !updatedData[ index ].Select;
    setDataNew(updatedData);
    setFilteredDataNew(updatedData);
    setSelectAllChecked(updatedData.every((item) => item.Select));
    setIsPopUpDataChanged(true);
  };

  const handleConfirm = () => {
    setIsPopUpDataChanged(false);
    const selectedStrategies = dataNew.filter((item) => item.Select);
    const stringVal = selectedStrategies.map((item) => item.UserID).join(", ");
    const newData = [ ...data ];
    if (clickedRowIndex !== -1) {
      newData[ clickedRowIndex ] = {
        ...newData[ clickedRowIndex ],
        TradingAccount: stringVal || "",
      };
    }
    dispatch(setStrategies({ strategies: newData }));

    const postBroker = async () => {
      try {
        const postdata = {
          broker_user_id: selectedStrategies.map((item) => item.UserID),
          broker: selectedStrategies.map((item) => item.Broker),
          multiplier: selectedStrategies.map((item) =>
            item.Multiplier !== undefined ? item.Multiplier : "1"
          ),
          strategy_tag: data[ clickedRowIndex ].StrategyLabel,
          alias: selectedStrategies.map((item) => item.Alias),
        };

        const response = await fetchWithAuth(
          `/api/store_broker_and_strategy_info/${mainUser}`,
          {
            method: "POST",
            body: JSON.stringify(postdata),
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw { message: errorData.message || "Something bad happened. Please try again" };
        }

        const responseData = await response.json();
        handleMsg({
          msg: responseData.message,
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: data[ clickedRowIndex ].StrategyLabel,
        });
      } catch (error) {
        handleMsg({
          msg: error.message,
          logType: "ERROR",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: data[ clickedRowIndex ].StrategyLabel,
        });
      }
    };
    postBroker();
    handleCloseModal();
  };

  const handleBrokerSquareOff = async (rowData, strategyLabel, brokerType) => {
    const endpoints = [
      `/api/${brokerType}_strategy_options_sqoff/${mainUser}/${strategyLabel}/${rowData.userId}`,
      `/api/${brokerType}_strategy_equity_sqoff/${mainUser}/${strategyLabel}/${rowData.userId}`,
    ];

    for (const endpoint of endpoints) {
      const response = await fetchWithAuth(endpoint, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        handleMsg({
          msg: errorData.message,
          logType: "ERROR",
          timestamp: `${new Date().toLocaleString()}`,
          user: rowData.userId,
          strategy: strategyLabel,
        });
      } else {
        const responseData = await response.json();
        handleMsg({
          msg: responseData.message,
          logType: "TRADING",
          timestamp: `${new Date().toLocaleString()}`,
          user: rowData.userId,
          strategy: strategyLabel,
        });
      }
    }
  };

  const handleManualSquareOff = async (strategyLabel, TradingAccount) => {
    const currentTime = new Date();
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();
    if (
      !(
        (currentHours === 9 && currentMinutes >= 15) ||
        (currentHours > 9 && currentHours < 15) ||
        (currentHours === 15 && currentMinutes <= 30)
      )
    ) {
      handleMsg({
        msg: "Order not placed as current time is outside the allowed time window.",
        logType: "INFO",
        timestamp: `${new Date().toLocaleString()}`,
        color: "red",
      });
      return;
    }

    try {
      const mappedUserIds = TradingAccount.split(", ");
      const requests = mappedUserIds.map((userId) => {
        const rowData = brokers.find((row) => row.userId === userId);
        if (rowData.inputDisabled) {
          switch (rowData.broker) {
            case "fyers":
              return handleBrokerSquareOff(rowData, strategyLabel, "fyers");
            case "flattrade":
              return handleBrokerSquareOff(rowData, strategyLabel, "flattrade");
            case "angelone":
              return handleBrokerSquareOff(rowData, strategyLabel, "angelone");
            case "pseudo_account":
              return handleBrokerSquareOff(rowData, strategyLabel, "pseudo");
            default:
              return null;
          }
        } else {
          handleMsg({
            msg: "Please Login to Broker Account",
            logType: "TRADING",
            timestamp: `${new Date().toLocaleString()}`,
            user: rowData.userId,
            strategy: strategyLabel,
          });
          return null;
        }
      });
      await Promise.allSettled(requests.filter(Boolean));
    } catch (error) {
      // Handle error if needed
    }
  };

  const handleInputChangeInputs = (index, fieldName, value) => {
    setDataNew((prev) => {
      const selectedStrategies = prev.filter((item) => item.Select);
      if (selectedStrategies.find((item) => item.UserID === prev[ index ].UserID)) {
        const newData = [ ...prev ];
        newData[ index ] = { ...newData[ index ], [ fieldName ]: value };
        setFilteredDataNew(newData); // Update filteredDataNew when Multiplier changes
        return newData;
      }
      return prev;
    });
    setIsPopUpDataChanged(true);
  };

  useEffect(() => {
    const applyFilters = () => {
      let filtered = [ ...dataNew ];
      if (userIDSelected.length > 0) {
        filtered = filtered.filter((item) => userIDSelected.includes(item.UserID));
      }
      if (aliasSelected.length > 0) {
        filtered = filtered.filter((item) => aliasSelected.includes(item.Alias));
      }
      if (brokerSelected.length > 0) {
        filtered = filtered.filter((item) => brokerSelected.includes(item.Broker));
      }
      if (marginSelected.length > 0) {
        filtered = filtered.filter((item) => marginSelected.includes(item.Margin));
      }
      setFilteredDataNew(filtered);
    };
    applyFilters();
  }, [ dataNew, userIDSelected, aliasSelected, brokerSelected, marginSelected ]);


  const previousValuesRef = useRef({});

  const debouncedMaxProfitLoss = useDebounce((
    maxProfit,
    maxLoss,
    strategyLabel,
    OpenTime,
    CloseTime,
    SqOffTime,
    index
  ) => {
    const currentValues = {
      maxProfit,
      maxLoss,
      OpenTime,
      CloseTime,
      SqOffTime
    };

    const prevValues = previousValuesRef.current[ index ] || {};
    const hasChanged = Object.keys(currentValues).some(
      key => currentValues[ key ] !== prevValues[ key ]
    );

    if (!hasChanged) return;

    previousValuesRef.current[ index ] = { ...currentValues };
    maxProfitLoss(maxProfit, maxLoss, strategyLabel, OpenTime, CloseTime, SqOffTime);
  }, 1000);

  const maxProfitLoss = async (
    maxProfit,
    maxLoss,
    strategyLabel,
    OpenTime,
    CloseTime,
    SqOffTime
  ) => {
    try {
      const maxValues = {
        max_profit: maxProfit,
        max_loss: maxLoss,
        open_time: OpenTime,
        close_time: CloseTime,
        square_off_time: SqOffTime,
      };
      const response = await fetchWithAuth(
        `/api/${mainUser}/${strategyLabel}/max_profit_loss`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(maxValues)
        }
      );
      const data = await response.json();
      handleMsg({
        msg: data.message,
        logType: "MESSAGE",
        timestamp: new Date().toLocaleString(),
        strategy: strategyLabel,
      });
    } catch (error) {
      console.error("Error in maxProfitLoss API:", error);
    }
  };

  const togglePopup = (index) => {
    const profitLocking = data[ index ]?.ProfitLocking.split("~");
    setPopupValues({
      index,
      profitReaches: profitLocking[ 0 ],
      lockMinimumProfit: profitLocking[ 1 ],
      increaseInProfit: profitLocking[ 2 ],
      trailProfitBy: profitLocking[ 3 ],
    });
    setShowPopup(!showPopup);
  };

  const handleInputChangePtLock = (name, value) => {
    setPopupValues((prev) => ({ ...prev, [ name ]: value }));
  };

  const updateUserProfitLocking = async (
    strategy_tag,
    profitReachesValue,
    lockProfitValue,
    increaseInProfit,
    trailProfitBy
  ) => {
    await fetchWithAuth(`/api/update_strategy_profit_locking/${mainUser}/${strategy_tag}`, {
      method: "POST",
      body: JSON.stringify({
        profit_locking: `${profitReachesValue},${lockProfitValue},${increaseInProfit},${trailProfitBy}`,
      }),
    });
  };

  const handleSetTrailTGT = () => {
    const profitReachesValue = popupValues.profitReaches;
    const lockProfitValue = popupValues.lockMinimumProfit;
    const increaseInProfitValue = popupValues.increaseInProfit;
    const trailProfitByValue = popupValues.trailProfitBy;

    if (
      (profitReachesValue && !lockProfitValue) ||
      (!profitReachesValue && lockProfitValue)
    )
      return;
    if (
      (increaseInProfitValue && !trailProfitByValue) ||
      (!increaseInProfitValue && trailProfitByValue)
    )
      return;

    const newData = [ ...strategies ];
    const strategyIndex = popupValues.index;
    newData[ strategyIndex ] = {
      ...newData[ strategyIndex ],
      ProfitLocking: `${profitReachesValue}~${lockProfitValue}~${increaseInProfitValue}~${trailProfitByValue}`,
    };
    dispatch(setStrategies({ strategies: newData }));

    const strategyTag = newData[ strategyIndex ].StrategyLabel;
    updateUserProfitLocking(
      strategyTag,
      profitReachesValue,
      lockProfitValue,
      increaseInProfitValue,
      trailProfitByValue
    );
    setShowPopup(false);
  };

  const handleInputDelete = () => {
    const newData = [ ...strategies ];
    newData[ popupValues.index ] = {
      ...newData[ popupValues.index ],
      ProfitLocking: "~~~",
    };
    dispatch(setStrategies({ strategies: newData }));
    const strategyTag = data[ popupValues.index ]?.StrategyLabel;
    updateUserProfitLocking(strategyTag, "", "", "", "");
    setPopupValues({
      index: "",
      profitReaches: "",
      lockMinimumProfit: "",
      increaseInProfit: "",
      trailProfitBy: "",
    });
  };

  useEffect(() => {
    if (!showPopup)
      setPopupValues({
        index: "",
        profitReaches: "",
        lockMinimumProfit: "",
        increaseInProfit: "",
        trailProfitBy: "",
      });
  }, [ showPopup ]);

  useClickOutside(popupRef, () => setShowPopup(false));

  const iconStyle = {
    border: "1px solid black",
    width: "25px",
    height: "25px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: "4px",
    marginRight: "5px",
  };

  const handleInputChangeFortrade = async (index, key, value) => {
    const updatedStrategies = [ ...strategies ];
    updatedStrategies[ index ] = { ...updatedStrategies[ index ], [ key ]: value };
    dispatch(setStrategies({ strategies: updatedStrategies }));
    const row = updatedStrategies[ index ];

    const requestBody = {
      allowed_trades: row.AllowedTrades,
      entry_order_retry: row.EntryOrderRetry,
      entry_retry_count: String(row.EntryRetryCount),
      entry_retry_wait: row.EntryRetryWaitSeconds,
      exit_order_retry: row.ExitOrderRetry,
      exit_retry_count: String(row.ExitRetryCount),
      exit_retry_wait: String(row.ExitRetryWaitSeconds),
      exit_max_wait: String(row.ExitMaxWaitSeconds),
    };

    try {
      const response = await fetchWithAuth(
        `/api/${mainUser}/${row.StrategyLabel}/update_wait_time`,
        {
          method: "POST",
          body: JSON.stringify(requestBody),
        }
      );

      if (response.ok) {
        handleMsg({
          msg: `Updated ${key} for ${row.StrategyLabel} successfully`,
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: row.StrategyLabel,
        });
      } else {
        const errorData = await response.json();
        handleMsg({
          msg: `Failed to update ${key}: ${errorData.message || "Unknown error"}`,
          logType: "ERROR",
          timestamp: `${new Date().toLocaleString()}`,
          strategy: row.StrategyLabel,
        });
      }
    } catch (error) {
      handleMsg({
        msg: `Error updating ${key}: ${error.message}`,
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
        strategy: row.StrategyLabel,
      });
    }
  };

  return (
    <div>
      <style>{styles}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav
            pageCols={stPageCols}
            colsSelectedAll={stColsSelectedALL}
            setColsSelectedALL={setStColsSelectedALL}
            selectAll={stPageColSelectAll}
            colVis={stColVis}
            setColVis={setStColVis}
            setSeq={setStrategiesSeq}
          />
          <div className="table-container" ref={tableRef}>
            <table className="user-profiles-table" style={{ borderRadius: "10px" }}>
              <thead style={{ position: "sticky", top: 0, zIndex: "10", height: "auto" }}>
                <tr>
                  {strategiesSeq.map((colName) => {
                    const hasFilter = filters[ colName ] && filters[ colName ].length > 0;
                    const selectedItems = filters[ colName ]?.length || 0;

                    return (
                      <th
                        key={colName}
                        className={
                          colName === "Action" ? "fixed-column-header-1" :
                            colName === "Strategy Label" ? "fixed-column-header-2" :
                              colName === "P L" ? "fixed-column-header-3" : ""
                        }
                        style={{
                          fontSize: "15px",
                          textAlign: "center",
                          backgroundColor: hasFilter ? "#f0f7ff" : "inherit",
                          borderBottom: hasFilter ? "2px solid #1976d2" : "inherit",
                          maxWidth: columnWidths[ colName ] || "auto",
                          minWidth: (() => {
                            // Base width calculation
                            let baseWidth = 100;

                            // Add extra width for filter badge when filter is applied
                            const filterExtraWidth = hasFilter ? 30 : 0;

                            return `${baseWidth + filterExtraWidth}px`;
                          })(),
                          height: "auto"
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "center",
                            position: "relative",
                            padding: "0",
                            margin: "0",
                            gap: "3px",
                            width: "100%"
                          }}
                        >
                          {/* Columns that don't need regular filters */}
                          {(colName === "Manual Exit" || colName === "Unique ID Req for Order" || colName === "Cancel Previous Open Signal" || colName === "Stop Reverse" || colName === "Part Multi Exists" || colName === "Entry Order Retry" || colName === "Exit Order Retry" || colName === "Sq Off Done" || colName === "Market Orders" || colName === "Profit Locking") ? (
                            <div style={{
                              fontSize: "14px",
                              fontWeight: "600",
                              wordBreak: "break-word",
                              hyphens: "auto",
                              lineHeight: "1",
                              margin: "0",
                              padding: "0",
                              textAlign: "center",
                              width: "100%"
                            }}>
                              {colName}
                            </div>
                          ) : colName === "Action" ? (
                            <div style={{
                              fontSize: "14px",
                              fontWeight: "600",
                              wordBreak: "break-word",
                              hyphens: "auto",
                              lineHeight: "1",
                              margin: "0",
                              padding: "0",
                              textAlign: "center",
                              width: "100%",
                              cursor: "pointer"
                            }}
                              onClick={(e) => handleFilterToggle("Action", e)}
                            >
                              {colName}
                            </div>
                          ) : (
                            <TableHeaderWithFilter
                              col={colName}
                              columnDisplayNames={{}}
                              hasFilter={hasFilter}
                              selectedItems={selectedItems}
                              handleFilterToggle={handleFilterToggle}
                              filterIcon={filterIcon}
                            />
                          )}

                          {colName === "Action" && filterPopup === "Action" && (
                            <div
                              ref={filterPopupRef}
                              style={{
                                position: "absolute",
                                top: "100%",
                                left: "0",
                                zIndex: 1000,
                                background: "white",
                                border: "1px solid #ccc",
                                boxShadow: "0 2px 5px rgba(0,0,0,0.15)",
                                minWidth: "100px",
                              }}
                            >
                              <select
                                value={actionFilter}
                                onChange={(e) => setActionFilter(e.target.value)}
                                style={{
                                  padding: "0.1rem 0.3rem",
                                  width: "100%",
                                  margin: "1px",
                                }}
                              >
                                <option value="all">All</option>
                                <option value="checked">Enable</option>
                                <option value="unchecked">Disable</option>
                              </select>
                            </div>
                          )}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody>
                {filteredRows.map((row, index) => (
                  <tr key={index}>
                    {strategiesSeq.map((keyName) => {
                      const key = keyName.replace(/\s/g, "");
                      if (
                        key === "UniqueIDReqforOrder"
                          ? stColVis[ "Unique ID Req for Order" ]
                          : stColVis[ keyName ]
                      ) {
                        return (
                          <td
                            key={key}
                            className={
                              key === "Action"
                                ? "fixed-column-1"
                                : key === "StrategyLabel"
                                  ? (clearedCells.includes(index) ? "fixed-column-2 cleared-cell" : "fixed-column-2")
                                  : key === "PL"
                                    ? "fixed-column-3"
                                    : ""
                            }
                            style={{
                              textAlign: "center",
                              width: columnWidths[ keyName ] || "auto",
                              maxWidth: columnWidths[ keyName ] || "auto",
                              minWidth: key === "ManualExit" ? "70px" : columnWidths[ keyName ] || "auto",
                            }}
                          >
                            {key === "Action" ? (
                              <div
                                style={{
                                  display: "flex",
                                  flexDirection: "row",
                                  paddingLeft: "9px",
                                }}
                              >
                                {row.enabled ? (
                                  <div
                                    style={iconStyle}
                                    onClick={() =>
                                      updateActionProperty(index, "enabled", false)
                                    }
                                  >
                                    <StopIcon sx={{ color: "red" }} />
                                  </div>
                                ) : (
                                  <div
                                    style={iconStyle}
                                    onClick={() =>
                                      updateActionProperty(index, "enabled", true)
                                    }
                                  >
                                    <PlayArrowIcon sx={{ color: "green" }} />
                                  </div>
                                )}
                                <span className="tooltip-container">
                                  {row.logged ? (
                                    <img
                                      src={Logout}
                                      alt="icon"
                                      style={{
                                        height: "25px",
                                        width: "25px",
                                        marginRight: "5px",
                                      }}
                                    />
                                  ) : (
                                    <img
                                      src={Log}
                                      alt="icon"
                                      style={{
                                        height: "25px",
                                        width: "25px",
                                        marginRight: "5px",
                                      }}
                                    />
                                  )}
                                  <span className="tooltiptext login-tooltip">
                                    complete
                                  </span>
                                </span>
                                <span className="tooltip-container">
                                  <div
                                    style={iconStyle}
                                    onClick={() => handleStrategyRowDelete(index)}
                                  >
                                    <DeleteIcon sx={{ color: "red" }} />
                                  </div>
                                  <span className="tooltiptext delete-tooltip">
                                    Delete
                                  </span>
                                </span>
                              </div>
                            ) : key === "ManualExit" ? (
                              <img
                                src={Log}
                                alt="icon"
                                style={{ height: "25px", width: "25px", display: "inline-block", }}
                                onClick={() =>
                                  handleManualSquareOff(
                                    row[ "StrategyLabel" ],
                                    row[ "TradingAccount" ]
                                  )
                                }
                              />
                            ) : key === "StrategyLabel" ? (
                              <input
                                style={{
                                  ...inputStyles,
                                  textAlign: "left",
                                  minWidth: "100%",
                                  minHeight: "20px",
                                  border: "1px solid transparent",
                                  display: "inline-block",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  scrollbarWidth: "thin",
                                  maxWidth: "11rem",
                                  maxHeight: "50px",
                                  overflowY: "auto",
                                  paddingLeft: "3px",
                                }}

                                type="text"
                                value={row[ key ]}
                                onChange={(e) =>
                                  handleInputChange(index, key, e.target.value)
                                }
                                onMouseEnter={(event) =>
                                  handleMouseEnter(row[ key ], event)
                                }
                                onMouseLeave={handleMouseLeave}
                              />
                            ) : key === "TradingAccount" ? (
                              <span
                                className="clickable-cell"
                                onClick={() => handleCellClick(row, index)}
                                onMouseEnter={(event) =>
                                  handleMouseEnter(row[ key ], event)
                                }
                                onMouseLeave={handleMouseLeave}
                                style={{
                                  minWidth: "100%",
                                  minHeight: "20px",
                                  border: "1px solid transparent",
                                  display: "inline-block",
                                  alignItems: "center",
                                  justifyContent: "flex-start",
                                  scrollbarWidth: "thin",
                                  maxWidth: "11rem",
                                  maxHeight: "50px",
                                  overflowY: "auto",
                                  textAlign: "left",
                                  paddingLeft: "3px",
                                }}
                              >
                                {row[ key ]}
                              </span>
                            ) : key === "PL" ? (
                              <input
                                type="text"
                                value={
                                  parseFloat(row[ key ]) === 0
                                    ? 0
                                    : parseFloat(row[ key ]).toFixed(2)
                                }
                                readOnly
                                style={{
                                  ...inputStyles,
                                  textAlign: "center",
                                  color: parseFloat(row[ key ]) < 0 ? "red" : "green",
                                }}
                              />
                            ) : key === "MaxProfit" || key === "MaxLoss" ? (
                              <input
                                type="number"
                                value={row[ key ]}
                                onChange={(e) => {
                                  handleInputChange(index, key, e.target.value);
                                  debouncedMaxProfitLoss(
                                    key === "MaxProfit" ? e.target.value : row[ "MaxProfit" ],
                                    key === "MaxLoss" ? e.target.value : row[ "MaxLoss" ],
                                    row[ "StrategyLabel" ],
                                    row[ "OpenTime" ],
                                    row[ "CloseTime" ],
                                    row[ "SqOffTime" ],
                                    index
                                  );
                                }}
                                onInput={(e) =>
                                  (e.target.value = e.target.value.replace(/[eE+\-]/g, ""))
                                }
                                style={{
                                  ...inputStyles,
                                  textAlign: "center",
                                  color: key === "MaxLoss" ? "red" : "green",
                                }}
                              />
                            ) : key === "OpenTime" ||
                              key === "CloseTime" ||
                              key === "SqOffTime" ? (
                              <TimePicker
                                value={row[ key ]}
                                disableClock={true}
                                format="HH:mm:ss"
                                maxDetail="second"
                                clearIcon={null}
                                clockIcon={null}
                                onChange={(newTime) => {
                                  handleInputChange(index, key, newTime);
                                  debouncedMaxProfitLoss(
                                    row[ "MaxProfit" ],
                                    row[ "MaxLoss" ],
                                    row[ "StrategyLabel" ],
                                    key === "OpenTime" ? newTime : row[ "OpenTime" ],
                                    key === "CloseTime" ? newTime : row[ "CloseTime" ],
                                    key === "SqOffTime" ? newTime : row[ "SqOffTime" ],
                                    index
                                  );
                                }}
                              />
                            ) : key === "ProfitLocking" ? (
                              <td style={{ padding: 0, position: "relative" }}>
                                <input
                                  type="text"
                                  value={row[ key ]}
                                  style={{
                                    padding: "3px",
                                    textAlign: "center",
                                    width: "100%",
                                    fontSize: "14px"
                                  }}
                                  readOnly
                                />
                                <KeyboardArrowDownIcon
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    togglePopup(index);
                                  }}
                                  style={{
                                    cursor: "pointer",
                                    position: "absolute",
                                    right: "0px",
                                    top: "8px",
                                    fontSize: "18px",
                                  }}
                                />
                              </td>
                            ) : key === "AllowedTrades" ? (
                              <td style={{ textAlign: "center" }}>
                                <select
                                  className="custom-select"
                                  value={row[ key ] || "Both"}
                                  onChange={(e) =>
                                    handleInputChangeFortrade(
                                      index,
                                      key,
                                      e.target.value
                                    )
                                  }
                                  style={{
                                    width: "150%",
                                    padding: "3px",
                                    color: "#000",
                                    fontSize: "14px"
                                  }}
                                >
                                  <option value="Both">Both</option>
                                  <option value="Long">Long</option>
                                  <option value="Short">Short</option>
                                </select>
                              </td>
                            ) : key === "EntryOrderRetry" || key === "ExitOrderRetry" ? (
                              <input
                                type="checkbox"
                                checked={row[ key ] || false}
                                onChange={(e) =>
                                  handleInputChangeFortrade(index, key, e.target.checked)
                                }
                                style={{
                                  padding: "3px",
                                  textAlign: "center",
                                  width: "100%"
                                }}
                              />
                            ) : key === "ExitMaxWaitSeconds" ||
                              key === "ExitRetryWaitSeconds" ||
                              key === "ExitRetryCount" ||
                              key === "EntryRetryWaitSeconds" ||
                              key === "EntryRetryCount" ? (
                              <input
                                type="number"
                                value={row[ key ] || ""}
                                onChange={(e) =>
                                  handleInputChangeFortrade(index, key, e.target.value)
                                }
                                readOnly={
                                  (key === "ExitMaxWaitSeconds" && !row[ "ExitOrderRetry" ]) ||
                                  (key === "ExitRetryWaitSeconds" &&
                                    !row[ "ExitOrderRetry" ]) ||
                                  (key === "ExitRetryCount" && !row[ "ExitOrderRetry" ]) ||
                                  (key === "EntryRetryWaitSeconds" &&
                                    !row[ "EntryOrderRetry" ]) ||
                                  (key === "EntryRetryCount" && !row[ "EntryOrderRetry" ])
                                }
                                onInput={(e) =>
                                  (e.target.value = e.target.value.replace(/[eE+\-]/g, ""))
                                }
                                style={{
                                  padding: "3px",
                                  textAlign: "center",
                                  width: "100%",
                                  fontSize: "14px"
                                }}
                              />
                            ) : key === "SqOffDone" ? (
                              <input
                                type="checkbox"
                                checked={row[ key ] || false}
                                style={{
                                  padding: "3px",
                                  textAlign: "center",
                                  width: "100%"
                                }}
                              />
                            ) : key === "MarketOrders" ? (
                              <td
                                onClick={() =>
                                  openMarketOrdersModal(
                                    row.userId,
                                    row.broker,
                                    row.name
                                  )
                                }
                              >
                                <input type="text" value="" style={{ ...inputStyles }} />
                              </td>
                            ) : (
                              <input
                                type="text"
                                value={row[ key ]}
                                onChange={(e) =>
                                  handleInputChange(index, key, e.target.value)
                                }
                                style={{ ...inputStyles, textAlign: "center" }}
                              />
                            )}
                          </td>
                        );
                      }
                      return null;
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {hoverData && (
            <div
              className="hover-box"
              style={{
                position: "absolute",
                left: hoverData.x,
                top: hoverData.y,
                backgroundColor: "black",
                border: "1px solid #ccc",
                padding: "5px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                zIndex: 1000,
                pointerEvents: "none",
                color: "white",
              }}
            >
              {hoverData.content}
            </div>
          )}
          {filterPopup && filterPopup !== "action" && (
            <div
              ref={filterPopupRef}
              style={{
                position: "absolute",
                top: `${popupPosition.top + 5}px`,
                left: `${popupPosition.left}px`,
                background: "#ffffff",
                border: "1px solid #e0e0e0",
                borderRadius: "8px",
                padding: "3px 0 3px 3px",
                boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                zIndex: 1000,
                minWidth: "100px",
                maxWidth: "180px"
              }}
            >
              <div style={{ paddingRight: "1px" }}>
                <div style={{
                  borderBottom: "1px solid #e0e0e0",
                  paddingBottom: "1px",
                  marginBottom: "1px",
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center"
                }}>
                  <span style={{ fontWeight: "bold", color: "#1976d2" }}>
                    Filter: {filterPopup}
                  </span>
                  <span style={{
                    fontSize: "12px",
                    color: "#666",
                    backgroundColor: "#f5f5f5",
                    padding: "2px 6px",
                    borderRadius: "4px"
                  }}>
                    {getDynamicUniqueValues(filterPopup).length} items
                  </span>
                </div>

                {getDynamicUniqueValues(filterPopup).length > 10 && (
                  <div style={{ marginBottom: "1px", paddingRight: "1px" }}>
                    <input
                      type="text"
                      placeholder="Search..."
                      style={{
                        width: "100%",
                        padding: "1px 1px",
                        border: "1px solid #ddd",
                        borderRadius: "4px",
                        fontSize: "12px"
                      }}
                    />
                  </div>
                )}

                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "1px 1px",
                    cursor: "pointer",
                    fontWeight: "500",
                    color: "#333",
                    marginBottom: "1px",
                    backgroundColor: "#f8f9fa",
                    borderRadius: "4px",
                    marginRight: "1px"
                  }}
                >
                  <input
                    type="checkbox"
                    checked={
                      tempFilters[ filterPopup ] &&
                      getDynamicUniqueValues(filterPopup).every((opt) =>
                        tempFilters[ filterPopup ].includes(opt)
                      )
                    }
                    onChange={() => handleSelectAll(filterPopup)}
                    style={{ marginRight: "8px" }}
                  />
                  <span>Select All</span>
                </label>

                <div
                  style={{
                    maxHeight: "200px",
                    overflowY: "auto",
                    margin: "0",
                    scrollbarWidth: "thin",
                    scrollbarColor: "#888 #f1f1f1",
                    border: "1px solid #eee",
                    borderRadius: "4px",
                    marginRight: "1px"
                  }}
                >
                  {getDynamicUniqueValues(filterPopup).length > 0 ? (
                    getDynamicUniqueValues(filterPopup).map((item) => {
                      const isSelected = tempFilters[ filterPopup ]?.includes(item) || false;
                      return (
                        <div
                          key={item}
                          style={{
                            display: "flex",
                            alignItems: "center",
                            padding: "1px 2px",
                            cursor: "pointer",
                            margin: "0",
                            borderBottom: "1px solid #f0f0f0",
                            backgroundColor: isSelected ? "#f0f7ff" : "transparent",
                            transition: "background-color 0.2s"
                          }}
                          onClick={() => handleFilterChange(filterPopup, item)}
                        >
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => { }}
                            style={{ marginRight: "8px" }}
                          />
                          <span style={{
                            color: isSelected ? "#1976d2" : "#444",
                            fontWeight: isSelected ? "500" : "normal"
                          }}>
                            {item || "(Empty)"}
                          </span>
                        </div>
                      );
                    })
                  ) : (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "10px",
                        color: "#666",
                        fontStyle: "italic",
                        minHeight: "40px",
                        justifyContent: "center"
                      }}
                    >
                      <span>No options available</span>
                    </div>
                  )}
                </div>

                <div style={{
                  fontSize: "12px",
                  color: "#666",
                  margin: "1px 0",
                  display: "flex",
                  justifyContent: "space-between",
                  marginRight: "1px"
                }}>
                  <span>
                    {tempFilters[ filterPopup ]?.length || 0} of {getDynamicUniqueValues(filterPopup).length} selected
                  </span>
                  {tempFilters[ filterPopup ]?.length > 0 && (
                    <span
                      style={{ color: "#1976d2", cursor: "pointer" }}
                      onClick={() => setTempFilters({ ...tempFilters, [ filterPopup ]: [] })}
                    >
                      Clear selection
                    </span>
                  )}
                </div>
              </div>

              <div
                style={{
                  marginTop: "3px",
                  display: "flex",
                  gap: "10px",
                  justifyContent: "center",
                  paddingRight: "3px"
                }}
              >
                <button
                  onClick={handleCancelFilter}
                  style={{
                    padding: "3px 3px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    background: "#f8f9fa",
                    cursor: "pointer",
                    color: "#333",
                    transition: "all 0.2s",
                    fontWeight: "500"
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleApplyFilter}
                  style={{
                    padding: "3px 3px",
                    border: "none",
                    borderRadius: "4px",
                    background: "#1976d2",
                    color: "white",
                    cursor: "pointer",
                    transition: "all 0.2s",
                    fontWeight: "500"
                  }}
                >
                  Apply Filter
                </button>
              </div>
            </div>
          )}

          <div className="add_collapse">
            <button className="hiddenbutton button">Add</button>
            <button
              className="button"
              onClick={addNewRow}
              style={{ zIndex: "0" }}
            >
              Add
            </button>
            <button
              style={{ zIndex: "0" }}
              onClick={() => errorContainerRef.current.toggleCollapse()}
              className="button"
              id="collapse"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>
          {showPopup && (
            <Draggable nodeRef={popupRef} >
              <div
                ref={popupRef}
                className="popupContainer"
                style={{
                  position: "fixed",
                  bottom: "6%",
                  right: "10%",
                  backgroundColor: "#fff",
                  border: "1px solid #ccc",
                  padding: "20px",
                  width: "400px",
                  height: "460px",
                  zIndex: 1000,
                  borderRadius: "5px",
                  boxShadow: "0px 5px 15px rgba(0, 0, 0, 0.3)",
                }}
              >
                <div
                  style={{
                    border: "1px solid #d3d3d3",
                    padding: "8px",
                    borderRadius: "5px",
                  }}
                >
                  <h4
                    style={{
                      marginLeft: "0px",
                      fontFamily: "roboto",
                      fontSize: "14",
                    }}
                  >
                    Profit Locking
                  </h4>
                  <div
                    style={{
                      display: "flex",
                      marginTop: "10px",
                      marginRight: "10px",
                    }}
                  >
                    <div className="input-box">
                      <span
                        style={{
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                        }}
                      >
                        If Profit Reaches
                      </span>
                      <input
                        type="number"
                        id="trail_tgt_0"
                        value={popupValues.profitReaches}
                        onChange={(e) =>
                          handleInputChangePtLock("profitReaches", e.target.value)
                        }
                        onInput={(e) =>
                          (e.target.value = e.target.value.replace(/[eE+\-]/g, ""))
                        }
                        style={{
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          padding: "10px",
                        }}
                      />
                    </div>
                    <div className="input-box" style={{ marginLeft: "10px" }}>
                      <span
                        style={{
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                        }}
                      >
                        Lock Minimum Profit At
                      </span>
                      <input
                        type="number"
                        id="trail_tgt_1"
                        value={popupValues.lockMinimumProfit}
                        onChange={(e) =>
                          handleInputChangePtLock("lockMinimumProfit", e.target.value)
                        }
                        onInput={(e) =>
                          (e.target.value = e.target.value.replace(/[eE+\-]/g, ""))
                        }
                        style={{
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          padding: "10px",
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div
                  style={{
                    border: "1px solid #d3d3d3",
                    padding: "8px",
                    borderRadius: "5px",
                    marginTop: "10px",
                  }}
                >
                  <h4
                    style={{
                      marginLeft: "0px",
                      fontFamily: "roboto",
                      fontSize: "14",
                    }}
                  >
                    Profit Trailing
                  </h4>
                  <div
                    style={{
                      display: "flex",
                      marginTop: "10px",
                      marginRight: "10px",
                    }}
                  >
                    <div className="input-box">
                      <span
                        style={{
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                        }}
                      >
                        Then Every Increase In Profit By
                      </span>
                      <input
                        type="number"
                        id="trail_tgt_2"
                        value={popupValues.increaseInProfit}
                        onChange={(e) =>
                          handleInputChangePtLock("increaseInProfit", e.target.value)
                        }
                        onInput={(e) =>
                          (e.target.value = e.target.value.replace(/[eE+\-]/g, ""))
                        }
                        style={{
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          padding: "10px",
                        }}
                      />
                    </div>
                    <div className="input-box" style={{ marginLeft: "10px" }}>
                      <span
                        style={{
                          color: "#4661bd",
                          fontFamily: "roboto",
                          fontSize: "14",
                        }}
                      >
                        Trail Profit By
                      </span>
                      <input
                        type="number"
                        id="trail_tgt_3"
                        value={popupValues.trailProfitBy}
                        onChange={(e) =>
                          handleInputChangePtLock("trailProfitBy", e.target.value)
                        }
                        onInput={(e) =>
                          (e.target.value = e.target.value.replace(/[eE+\-]/g, ""))
                        }
                        style={{
                          border: "none",
                          width: "160px",
                          borderBottom: "1px solid #000",
                          padding: "10px",
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div
                  style={{
                    fontFamily: "roboto",
                    fontSize: "12px",
                    marginTop: "10px",
                    color: "orange",
                  }}
                >
                  VALUES SHOULD BE IN RUPEES ONLY
                </div>
                <div
                  style={{
                    fontFamily: "roboto",
                    fontSize: "12px",
                    marginTop: "5px",
                    color: "#4661bd",
                  }}
                >
                  LOCKING AND TRAILING CAN BE USED INDEPENDENTLY
                </div>
                <div
                  style={{
                    fontFamily: "roboto",
                    fontSize: "12px",
                    marginTop: "5px",
                    color: "green",
                  }}
                >
                  TGT/ SL ON PER LOT BASIS IF TICKED WILL BE APPLICABLE HERE
                </div>
                <div>
                  <button
                    style={{
                      marginTop: "20px",
                      padding: "4px 8px",
                      backgroundColor: "#28A745",
                      color: "white",
                      borderRadius: "5px",
                    }}
                    onClick={handleSetTrailTGT}
                  >
                    OK
                  </button>
                  <button
                    style={{
                      marginTop: "20px",
                      padding: "4px 8px",
                      marginLeft: "3px",
                      backgroundColor: "#DC3545",
                      color: "white",
                      borderRadius: "5px",
                    }}
                    onClick={handleInputDelete}
                  >
                    DELETE
                  </button>
                  <button
                    style={{
                      marginTop: "20px",
                      padding: "4px 7px",
                      marginLeft: "3px",
                      backgroundColor: "#007bff",
                      color: "white",
                      borderRadius: "5px",
                    }}
                    onClick={() => setShowPopup(false)}
                  >
                    CLOSE
                  </button>
                </div>
              </div>
            </Draggable>
          )}
          <OptimizedErrorContainer
            ref={errorContainerRef}
            msgs={msgs}
            handleClearLogs={handleClearLogs}
          />
        </div>
        <OptimizedRightNav />
      </div>
      <MarketOrdersModal
        isOpen={isModalOpenStratgey}
        onClose={closeModalMarketOrders}
        selectedUserId={selectedUser.userId}
        selectedBroker={selectedUser.broker}
        selectedName={selectedUser.name}
        onSave={handleSaveSettings}
      />
      <Modal
        isOpen={isModalOpen}
        onRequestClose={handleCloseModal}
        contentLabel="Trading Account Modal"
        style={{
          content: {
            overflow: "hidden",
            width: "100%",
            height: "100%",
            margin: "auto",
            marginTop: "-40px",
            marginBottom: "-40px",
            overflowX: "hidden",
            backgroundColor: "transparent",
            border: "transparent",
            padding: "0",
          },
          overlay: { backgroundColor: "rgba(0, 0, 0, 0.5)", zIndex: 1000 },
        }}
      >
        <Draggable handle=".modal-headerStrategy">
          <div
            style={{
              backgroundColor: "white",
              height: 410,
              width: 700,
              margin: "auto",
              borderRadius: "6px",
              marginTop: "9%",
            }}
          >
            <div
              className="modal-headerStrategy"
              style={{
                fontSize: "17px",
                backgroundColor: "#d8e1ff",
                borderRadius: "6px",
                marginBottom: "2px",
                padding: "10px",
                overflowX: "hidden",
              }}
            >
              <h2 style={{ margin: 0 }}>
                Select User(s) for Strategies:{" "}
                <span style={{ color: "blue" }}>
                  {selectedTradingAccount?.StrategyLabel}
                </span>
              </h2>
            </div>
            <div
              className="container"
              style={{ height: "300px", overflow: "auto" }}
            >
              <table className="custom-table" style={{ width: "100%" }}>
                <thead style={{ position: "sticky", top: 0, zIndex: 10 }}>
                  <tr
                    style={{
                      background: "#d8e1ff",
                      height: 30,
                      fontSize: "18px",
                    }}
                  >
                    <th style={{ textAlign: "center", width: "13%" }}>
                      <small>
                        <input
                          type="checkbox"
                          checked={selectAllChecked}
                          onChange={handleSelectAllUsers}
                          style={{ marginRight: "5px" }}
                        />
                        Select
                      </small>
                    </th>
                    <th style={{ textAlign: "center", width: "17%" }}>
                      <div className="popTableHead" style={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", gap: "4px" }}>
                        <small>User ID</small>
                        <img
                          src={filterIcon}
                          alt="icon"
                          style={{ height: "20px", width: "20px", cursor: "pointer" }}
                          onClick={() => setIsdropDownOpenUserID(!isdropDownOpenUserID)}
                        />
                        {isdropDownOpenUserID && (
                          <div className="dropdown-menu">
                            {Array.from(new Set(dataNew.map((d) => d.UserID))).map(
                              (userid, index) => (
                                <label key={index}>
                                  <input
                                    type="checkbox"
                                    checked={userIDSelected.includes(userid)}
                                    onChange={() =>
                                      setUserIDSelected((prev) =>
                                        prev.includes(userid)
                                          ? prev.filter((v) => v !== userid)
                                          : [ ...prev, userid ]
                                      )
                                    }
                                  />
                                  {userid}
                                </label>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </th>
                    <th style={{ textAlign: "center", width: "18%" }}>
                      <div className="popTableHead" style={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", gap: "4px" }}>
                        <small>Alias</small>
                        <img
                          src={filterIcon}
                          alt="icon"
                          style={{ height: "20px", width: "20px", cursor: "pointer" }}
                          onClick={() => setIsdropDownOpenAlias(!isdropDownOpenAlias)}
                        />
                        {isdropDownOpenAlias && (
                          <div className="dropdown-menu">
                            {Array.from(new Set(dataNew.map((d) => d.Alias))).map(
                              (alias, index) => (
                                <label key={index}>
                                  <input
                                    type="checkbox"
                                    checked={aliasSelected.includes(alias)}
                                    onChange={() =>
                                      setAliasSelected((prev) =>
                                        prev.includes(alias)
                                          ? prev.filter((v) => v !== alias)
                                          : [ ...prev, alias ]
                                      )
                                    }
                                  />
                                  {alias}
                                </label>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </th>
                    <th style={{ textAlign: "center", width: "18%" }}>
                      <div className="popTableHead" style={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", gap: "4px" }}>
                        <small>Multiplier</small>
                      </div>
                    </th>
                    <th style={{ textAlign: "center", width: "20%" }}>
                      <div className="popTableHead" style={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", gap: "4px" }}>
                        <small>Broker</small>
                        <img
                          src={filterIcon}
                          alt="icon"
                          style={{ height: "20px", width: "20px", cursor: "pointer" }}
                          onClick={() => setIsdropDownOpenBroker(!isdropDownOpenBroker)}
                        />
                        {isdropDownOpenBroker && (
                          <div className="dropdown-menu">
                            {Array.from(new Set(dataNew.map((d) => d.Broker))).map(
                              (broker, index) => (
                                <label key={index}>
                                  <input
                                    type="checkbox"
                                    checked={brokerSelected.includes(broker)}
                                    onChange={() =>
                                      setBrokerSelected((prev) =>
                                        prev.includes(broker)
                                          ? prev.filter((v) => v !== broker)
                                          : [ ...prev, broker ]
                                      )
                                    }
                                  />
                                  {broker}
                                </label>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </th>
                    <th style={{ textAlign: "center", width: "20%" }}>
                      <div className="popTableHead" style={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", gap: "4px" }}>
                        <small>Margin</small>
                        <img
                          src={filterIcon}
                          alt="icon"
                          style={{ height: "20px", width: "20px", cursor: "pointer" }}
                          onClick={() => setIsdropDownOpenMargin(!isdropDownOpenMargin)}
                        />
                        {isdropDownOpenMargin && (
                          <div className="dropdown-menu">
                            {Array.from(new Set(dataNew.map((d) => d.Margin))).map(
                              (margin, index) => (
                                <label key={index}>
                                  <input
                                    type="checkbox"
                                    checked={marginSelected.includes(margin)}
                                    onChange={() =>
                                      setMarginSelected((prev) =>
                                        prev.includes(margin)
                                          ? prev.filter((v) => v !== margin)
                                          : [ ...prev, margin ]
                                      )
                                    }
                                  />
                                  {margin}
                                </label>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredDataNew.map((account, index) => (
                    <tr
                      key={index}
                      className={index % 2 === 0 ? "even-row" : "odd-row"}
                      style={{ fontSize: "16px" }} // Matches AccountTable body (assumed)
                    >
                      <td style={{ padding: "15px", textAlign: "center" }}>
                        <input
                          type="checkbox"
                          checked={account.Select}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleCheckboxChange(index);
                          }}
                        />
                      </td>
                      <td style={{ padding: "8px", textAlign: "center" }}>
                        {account.UserID}
                      </td>
                      <td style={{ padding: "8px", textAlign: "center" }}>
                        {account.Alias}
                      </td>
                      <td>
                        <input
                          type="text"
                          value={account.Multiplier}
                          onInput={(e) =>
                            (e.target.value = e.target.value.replace(/[^0-9]/g, ""))
                          }
                          style={{
                            width: "100%",
                            height: "100%",
                            border: "none",
                            textAlign: "center",
                            fontSize: "16px", // Matches body text
                          }}
                          readOnly={!account.Select}
                          onChange={(e) =>
                            handleInputChangeInputs(index, "Multiplier", e.target.value)
                          }
                        />
                      </td>
                      <td style={{ padding: "8px", textAlign: "center" }}>
                        {account.Broker}
                      </td>
                      <td style={{ padding: "8px", textAlign: "center" }}>
                        {account.Margin}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                padding: "10px",
              }}
            >
              <button
                disabled={!isPopUpDataChanged || !dataNew.some((item) => item.Select)}
                style={
                  isPopUpDataChanged && dataNew.some((item) => item.Select)
                    ? {
                      backgroundColor: "#0BDA51",
                      color: "white",
                      padding: "10px",
                      borderRadius: "10px",
                      cursor: "pointer",
                      width: "75px",
                    }
                    : {
                      padding: "10px",
                      borderRadius: "10px",
                      cursor: "default",
                      width: "75px",
                    }
                }
                onClick={handleConfirm}
              >
                Confirm
              </button>
              <button
                onClick={handleCloseModal}
                style={{
                  backgroundColor: "#FF2400",
                  color: "white",
                  padding: "10px",
                  borderRadius: "10px",
                  marginLeft: "25px",
                  width: "75px",
                }}
              >
                Close
              </button>
            </div>
          </div>
        </Draggable>
      </Modal>
      <Modal
        isOpen={showModal}
        onRequestClose={() => setShowModal(false)}
        contentLabel="Error Modal"
        style={{
          overlay: { backgroundColor: "rgba(0, 0, 0, 0.5)", zIndex: 1000 },
          content: {
            width: "300px",
            height: "160px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            background: "white",
            borderRadius: "10px",
            padding: "20px",
          },
        }}
      >
        <p style={{ textAlign: "center", fontSize: "18px", marginBottom: "20px" }}>
          {errorMessage}
        </p>
        <div style={{ flex: 1 }}></div>
        <button
          style={{
            padding: "8px 16px",
            borderRadius: "5px",
            backgroundColor: "#5cb85c",
            color: "white",
            border: "none",
            cursor: "pointer",
          }}
          onClick={() => setShowModal(false)}
        >
          OK
        </button>
      </Modal>
    </div>
  );
}

export default Strategies;