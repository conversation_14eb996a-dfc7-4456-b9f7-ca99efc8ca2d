import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setCollapse } from "../store/slices/collapse";
import filterIcon from "../assets/newFilter.png";
import { FaSearch } from "react-icons/fa";
import { FaSortAlphaDown, FaSortAlphaUp } from 'react-icons/fa';
import { clearMessages } from '../store/slices/consoleMsg.js';
import * as XLSX from "xlsx";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import {
  WarningAmber as AttentionIcon,
  Error as ErrorIcon,
  ReportProblem as WarningIcon,
  Chat as MessagesIcon,
  ShowChart as TradingIcon,
  Delete as ClearLogsIcon,
  FilterList as ClearFilterIcon,
  FileCopy as CopyAllIcon,
  SaveAlt as ExportIcon,
  List as AllLogsIcon
} from "@mui/icons-material";


export const ErrorContainer = forwardRef(({ msgs }, ref) => {
  const dispatch = useDispatch();
  const { collapsed, height } = useSelector((state) => state.collapseReducer);
  const messages = useSelector((state) => state.consoleMsgsReducer.consoleMsgs);
  const containerRef = useRef(null);
  const [ resizing, setResizing ] = useState(false);
  const [ startY, setStartY ] = useState(0);
  const [ startHeight, setStartHeight ] = useState(0);

  const [ tooltipVisible, setTooltipVisible ] = useState(false);
  const [ showFilters, setShowFilters ] = useState({});
  const [ selectedFilters, setSelectedFilters ] = useState({});
  const [ uniqueValues, setUniqueValues ] = useState({});
  const [ filteredMessages, setFilteredMessages ] = useState([]);
  const [ sortConfig, setSortConfig ] = useState({ key: null, direction: "asc" });

  useEffect(() => {
    if (messages.length > 0) {
      const getUniqueValues = (key) =>
        [ ...new Set(messages.map((item) => item[ key ]?.toString().toLowerCase() || "")) ].filter(Boolean);

      setUniqueValues({
        timestamp: getUniqueValues("timestamp"),
        logType: getUniqueValues("logType"),
        user: getUniqueValues("user"),
        strategy: getUniqueValues("strategy"),
        portfolio: getUniqueValues("portfolio"),
        msg: getUniqueValues("msg"),
      });

      setSelectedFilters({
        timestamp: [],
        logType: [],
        user: [],
        strategy: [],
        portfolio: [],
        msg: [],
      });

      setFilteredMessages(messages); // Show all messages initially
    }
  }, [ messages ]);
  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;

      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.startegy &&
        lastMsg.portfolio === Msg.porttfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };


  const [ count, setCount ] = useState({
    attentions: 0,
    errors: 0,
    warnings: 0,
    messages: 0,
    tradings: 0,
  });
  useEffect(() => {
    setCount({
      attentions: messages.filter((msg) => msg.logType === "ATTENTION").length,
      errors: messages.filter((msg) => msg.logType === "ERROR").length,
      warnings: messages.filter((msg) => msg.logType === "WARNING").length,
      messages: messages.filter((msg) => msg.logType === "MESSAGE").length,
      tradings: messages.filter((msg) => msg.logType === "TRADING").length,
    });
  }, [ messages ]);
  useEffect(() => {
    if (containerRef.current) {
      if (containerRef.current.style.overflow !== "hidden") {
        const scrollTimeout = setTimeout(() => {
          if (containerRef.current) {
            containerRef.current.scrollTop = containerRef.current.scrollHeight;
          }
        }, 100);
        return () => clearTimeout(scrollTimeout);
      }
    }
  }, [ containerRef, ]);

  const { height: errorContainerHeight } = useSelector(
    (state) => state.collapseReducer,
  );

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.style.height = collapsed ? "75px" : `${height}px`;

      const maxVisibleMessages = Math.floor((errorContainerHeight - 40 - 35) / 25) - 1;
      containerRef.current.style.overflow =
        messages.length > maxVisibleMessages ? "auto" : "hidden";
    }
  }, [ collapsed, height, errorContainerHeight, messages.length ]); // Add appropriate dependencies


  useImperativeHandle(ref, () => ({
    toggleCollapse() {
      const newHeight = collapsed ? 310 : 75;  // Toggle between collapsed and expanded height
      dispatch(setCollapse({ height: newHeight, collapsed: !collapsed }));
      containerRef.current.style.height = `${newHeight}px`;
      containerRef.current.style.overflow = newHeight < 150 ? "auto" : "hidden";
    },
  }));

  const handleMouseMove = (e) => {
    if (resizing) {
      const newHeight = startHeight - (e.clientY - startY);
      if (newHeight < 75) {
        dispatch(setCollapse({ height: 75, collapsed: true }));
        containerRef.current.style.overflow = "hidden";
      } else if (newHeight > 350) {
        dispatch(setCollapse({ height: 350, collapsed: false }));
      } else {
        containerRef.current.style.height = `${newHeight}px`;
        containerRef.current.style.overflow = newHeight < 150 ? "auto" : "hidden";
      }
    }
  };

  const tableHeaders = [ "timestamp", "logType", "user", "strategy", "portfolio", "msg" ];
  const handleMouseUp = () => {
    setResizing(false);
  };

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [ handleMouseMove, resizing ]);

  const handleDisplay = (type) => {
    if (type === "logs") {
      setFilteredMessages(messages);
    } else if (type === "clear") {
      dispatch(clearMessages());
      setFilteredMessages([]);
      resetCounts();
    } else {
      const filteredData = messages.filter((msg) => msg.logType.toLowerCase() === type.toLowerCase());
      setFilteredMessages(filteredData);
    }
  };
  const resetCounts = () => {
    setCount({
      attentions: 0,
      errors: 0,
      warnings: 0,
      messages: 0,
      tradings: 0,
    });
  };

  const copyTableToClipboard = () => {
    let tabularData =
      "Timestamp \t Logtype \t User \t Strategy \t Portfolio \t Message \t \n";
    let excelData =
      "Timestamp, Logtype, User, Strategy, Portfolio, Message, \n";
    messages.forEach((row) => {
      const rowData = [];
      rowData.push(row[ "timestamp" ] || "");
      rowData.push(row[ "logType" ] || "");
      rowData.push(row[ "user" ] || "");
      rowData.push(row[ "strategy" ] || "");
      rowData.push(row[ "portfolio" ] || "");
      rowData.push(row[ "msg" ] || "");
      tabularData += rowData.join("\t") + "\n";
      excelData += rowData.join(", ") + "\n";
    });
    try {
      const textarea = document.createElement("textarea");
      textarea.value = tabularData;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand("copy");
      document.body.removeChild(textarea);
      setTooltipVisible(true);
      setTimeout(() => {
        setTooltipVisible(false);
      }, 2000);
      handleMsg({
        msg: " Error console Table data successfully copied to clipboard.",
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
      });

    } catch (error) {
      console.error("Failed to copy table data to clipboard:", error);
    }
  };

  useEffect(() => {
    const initialUniqueValues = {};
    tableHeaders.forEach((header) => {
      initialUniqueValues[ header ] = Array.from(
        new Set(
          messages.map((msg) => {
            const value = msg[ header ];
            if (value === null || value === undefined) {
              return "(Blank)";
            }
            return value.toString().toLowerCase();
          })
        )
      );
    });

    setUniqueValues(initialUniqueValues);
  }, [ messages ]);

  useEffect(() => {
    if (!messages.length) return;
    const filtered = messages.filter((message) =>
      Object.entries(selectedFilters).every(([ key, values ]) =>
        values.length === 0 || values.includes(message[ key ]?.toString().toLowerCase())
      )
    );
    setFilteredMessages(filtered);
    const updatedUniqueValues = {};
    tableHeaders.forEach((header) => {
      if (selectedFilters[ header ]?.length > 0) {
        updatedUniqueValues[ header ] = Array.from(
          new Set(
            messages
              .map((msg) => msg[ header ]?.toString().toLowerCase())
          )
        );
      } else {
        updatedUniqueValues[ header ] = Array.from(
          new Set(
            filtered
              .map((msg) => msg[ header ]?.toString().toLowerCase())
          )
        );
      }
    });

    setUniqueValues(updatedUniqueValues);
  }, [ selectedFilters, messages ]);

  const handleSort = (key) => {
    const direction = sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
    setSortConfig({ key, direction });
    const sortedMessages = [ ...filteredMessages ].sort((a, b) => {
      if (a[ key ] < b[ key ]) return direction === "asc" ? -1 : 1;
      if (a[ key ] > b[ key ]) return direction === "asc" ? 1 : -1;
      return 0;
    });
    setFilteredMessages(sortedMessages);
  };

  const toggleFilterPopup = (key) => {
    setShowFilters((prev) => ({ ...prev, [ key ]: !prev[ key ] }));
  };

  const toggleSelectAll = (key) => {
    setSelectedFilters((prev) => {
      const isAllSelected = prev[ key ]?.length === uniqueValues[ key ]?.length;
      return {
        ...prev,
        [ key ]: isAllSelected ? [] : [ ...uniqueValues[ key ] ],
      };
    });
  };

  const toggleFilterValue = (key, value) => {
    setSelectedFilters((prev) => {
      const updatedValues = prev[ key ]?.includes(value)
        ? prev[ key ].filter((item) => item !== value)
        : [ ...(prev[ key ] || []), value ];

      const filtered = messages.filter((message) => {
        const messageValue = message[ key ] == null || message[ key ] === ""
          ? "(Blank)"
          : message[ key ].toString().toLowerCase();
        return updatedValues.length === 0 || updatedValues.includes(messageValue);
      });

      setFilteredMessages(filtered);
      return { ...prev, [ key ]: updatedValues };
    });
  };
  const [ searchTerms, setSearchTerms ] = useState({});
  const handleSearchChange = (key, value) => {
    setSearchTerms((prev) => ({ ...prev, [ key ]: value }));
  };

  const handleMouseDown = (e) => {
    setResizing(true);
    setStartY(e.clientY);
    setStartHeight(containerRef.current.offsetHeight);
  };

  const handleExport = () => {
    const formattedData = messages.map((item) => {
      const row = {};
      tableHeaders.forEach((header) => {
        row[ header ] = item[ header ] || "";
      });
      return row;
    });
    const worksheet = XLSX.utils.json_to_sheet(formattedData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Data");
    XLSX.writeFile(workbook, "data.xlsx");
  }
  const filterRefs = useRef({});

  useEffect(() => {
    const handleClickOutside = (e) => {
      const isOutsideClick = !Object.values(filterRefs.current).some(ref => ref && ref.contains(e.target));
      if (isOutsideClick) {
        setShowFilters({});
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);


  const handleFilterClick = (e, header) => {
    e.stopPropagation();
  };

  const clearFilters = () => {
    setSelectedFilters({});
    setSearchTerms({});
    setFilteredMessages(initialMessages);
  };
  const [ dropdownSortConfig, setDropdownSortConfig ] = useState({});

  const handleDropdownSort = (header, direction) => {
    setDropdownSortConfig((prevConfig) => ({
      ...prevConfig,
      [ header ]: {
        direction,
      },
    }));
  };

  const getSortedDropdownValues = (header) => {
    const values = uniqueValues[ header ] || [];
    const currentConfig = dropdownSortConfig[ header ];

    if (currentConfig?.direction === "desc") {
      return values.sort((a, b) => b.localeCompare(a)); // Z-A sort
    }

    return values.sort((a, b) => a.localeCompare(b)); // A-Z sort
  };
  const logItems = [
    { icon: <AllLogsIcon />, label: "All Logs", onClick: () => handleDisplay("logs") },
    { icon: <AttentionIcon color="warning" />, label: `${count.attentions} Attention`, onClick: () => handleDisplay("ATTENTION") },
    { icon: <ErrorIcon color="error" />, label: `${count.errors} Errors`, onClick: () => handleDisplay("ERROR") },
    { icon: <WarningIcon color="warning" />, label: `${count.warnings} Warnings`, onClick: () => handleDisplay("WARNING") },
    { icon: <MessagesIcon color="info" />, label: `${count.messages} Messages`, onClick: () => handleDisplay("MESSAGE") },
    { icon: <TradingIcon color="primary" />, label: `${count.tradings} Trading`, onClick: () => handleDisplay("TRADING") },
    { icon: <ClearLogsIcon color="secondary" />, label: "Clear Logs", onClick: () => handleDisplay("clear") },
    { icon: <ClearFilterIcon />, label: "Clear Filter", onClick: clearFilters },
    { icon: <CopyAllIcon />, label: "Copy All", onClick: copyTableToClipboard },
    { icon: <ExportIcon />, label: "Export", onClick: handleExport }
  ];


  return (
    <div className="error-container" ref={containerRef} style={{ position: "relative", overflow: "hidden" }}>
      <div style={{ position: "sticky", top: "0px", }}>
        <div
          id="draggable"
          style={{
            cursor: "row-resize",
            background: "#d8e1ff",
            width: "100%",
            height: "8px",
            borderRadius: "10px",
            position: "absolute",
            top: "-5px",
            userSelect: "none",
          }}
          onMouseDown={handleMouseDown}
        ></div>
        <div className="buttons-container" style={{ display: "flex", gap: "10px", background: "#D8E1FF" }}>
          {logItems.map(({ icon, label, onClick }, index) => (
            <div key={index} style={{ display: "flex", alignItems: "center", cursor: "pointer", gap: "5px" }} onClick={onClick}>
              {icon}
              <span>{label}</span>
            </div>
          ))}

          {tooltipVisible && (
            <div
              style={{
                position: "absolute",
                bottom: "120%",
                left: "50%",
                transform: "translateX(-50%)",
                backgroundColor: "#333",
                color: "#fff",
                padding: "5px 10px",
                borderRadius: "4px",
                fontSize: "15px",
                opacity: 0.9,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                transition: "opacity 0.3s",
                zIndex: 999
              }}
            >
              <span style={{ marginRight: "5px" }}>✔</span> Copied
            </div>
          )}
        </div>

      </div>
      <table
        style={{
          width: "100%",
          borderCollapse: "collapse",
          margin: "0",
          tableLayout: "fixed", // Ensures consistent column widths
          padding: "0",
        }}
      >
        <thead>
          <tr>
            {tableHeaders.map((header) => (
              <th
                key={header}
                style={{
                  backgroundColor: "#f4f6fb",
                  color: "#333",
                  fontWeight: "bold",
                  textAlign: "center",
                  padding: "3px 6px",
                  borderBottom: "1px solid #ddd",
                  fontSize: "14px",
                  position: "sticky",
                  top: "40px",
                  lineHeight: "1",
                  width:
                    header === "msg"
                      ? "30%"
                      : header === "timestamp"
                        ? "8%"
                        : header === "portfolio" || header === "strategy"
                          ? "7%"
                          : [ "logType", "user" ].includes(header)
                            ? "5.5%"
                            : "5%",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: "pointer",
                  }}
                  onClick={() => handleSort(header)}
                >
                  <span style={{ marginLeft: "10px" }}>
                    {header.charAt(0).toUpperCase() + header.slice(1)}
                  </span>

                  {sortConfig.key === header && (
                    <span style={{ marginLeft: "4px", height: "20px", width: "25px" }}>
                      {sortConfig.direction === "asc" ? "\u2193" : "\u2191"}
                    </span>
                  )}
                  <img
                    src={filterIcon}
                    alt="Filter"
                    style={{
                      cursor: "pointer",
                      marginLeft: "-2px",
                      width: "25px",
                      height: "25px",
                      opacity: 0.8,
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFilterPopup(header);
                    }}
                  />
                </div>

                {showFilters[ header ] && (
                  <div
                    ref={(el) => (filterRefs.current[ header ] = el)}
                    style={{
                      position: "absolute",
                      backgroundColor: "white",
                      padding: "10px",
                      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      zIndex: 1000,
                      marginTop: "4px",
                      borderRadius: "4px",
                      maxHeight: "300px",
                      overflowY: "auto",
                      width:
                        header === "msg"
                          ? "300px"
                          : header === "timestamp"
                            ? "200px"
                            : [ "logType", "strategy", "user", "portfolio" ].includes(header)
                              ? "120px"
                              : "120px",
                    }}
                    onClick={(e) => handleFilterClick(e, header)}
                  >
                    <div style={{ padding: "5px" }}>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          marginBottom: "8px",
                        }}
                      >
                        <button
                          onClick={() => handleDropdownSort(header, "asc")}
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                            color:
                              dropdownSortConfig[ header ]?.direction === "asc"
                                ? "#007bff"
                                : "#ccc",
                            fontSize: "16px",
                            display: "flex",
                            alignItems: "center",
                          }}
                          title="Sort A to Z"
                        >
                          <FaSortAlphaDown />
                        </button>

                        <button
                          onClick={() => handleDropdownSort(header, "desc")}
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                            color:
                              dropdownSortConfig[ header ]?.direction === "desc"
                                ? "#007bff"
                                : "#ccc",
                            fontSize: "16px",
                            display: "flex",
                            alignItems: "center",
                          }}
                          title="Sort Z to A"
                        >
                          <FaSortAlphaUp />
                        </button>
                      </div>
                    </div>

                    <div style={{ marginBottom: "8px", position: "relative" }}>
                      <input
                        type="text"
                        placeholder="Search..."
                        value={searchTerms[ header ] || ""}
                        onChange={(e) => handleSearchChange(header, e.target.value)}
                        style={{
                          width: "100%",
                          padding: "4px 30px 4px 4px", // Adding right padding to make space for the icon
                          boxSizing: "border-box",
                          borderRadius: "4px",
                          border: "1px solid #ccc",
                        }}
                      />
                      <FaSearch
                        style={{
                          position: "absolute",
                          right: "8px",
                          top: "50%",
                          transform: "translateY(-50%)", // Center the icon vertically
                          fontSize: "16px", // Size of the icon
                          color: "#888", // Icon color
                        }}
                      />
                    </div>

                    {/* Filter Form with Select All */}
                    <form style={{ maxHeight: "120px", overflowY: "auto" }}>
                      <div style={{ marginBottom: "8px" }}>
                        {/* Select All Checkbox */}
                        <label
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginBottom: "6px",
                            fontWeight: "normal",
                            marginTop: "2px",
                            padding: "0px",
                          }}
                        >
                          <input
                            type="checkbox"
                            checked={
                              getSortedDropdownValues(header).length > 0 &&
                              selectedFilters[ header ]?.length ===
                              getSortedDropdownValues(header).length
                            }
                            onChange={() => {
                              if (getSortedDropdownValues(header).length > 0) {
                                toggleSelectAll(header);
                              }
                            }}
                            style={{ marginRight: "6px" }}
                            disabled={getSortedDropdownValues(header).length === 0} // Disable if no data
                          />
                          <span>Select All</span>
                        </label>
                      </div>

                      {[
                        ...getSortedDropdownValues(header)
                          .filter((value) => value === "" || value === undefined) // Empty values first
                          .map((value) => (
                            <div
                              key={value}
                              style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}
                            >
                              <input
                                type="checkbox"
                                checked={selectedFilters[ header ]?.includes(value)}
                                onChange={() => toggleFilterValue(header, value)}
                                style={{ marginRight: "6px" }}
                              />
                              <span style={{ textAlign: "left", fontWeight: "normal" }}>
                                {value === undefined ? "Empty" : ""}
                              </span>
                            </div>
                          )),
                        ...getSortedDropdownValues(header)
                          .filter((value) => value !== "" && value !== undefined) // Non-empty values
                          .map((value) => (
                            <div
                              key={value}
                              style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}
                            >
                              <input
                                type="checkbox"
                                checked={selectedFilters[ header ]?.includes(value)}
                                onChange={() => toggleFilterValue(header, value)}
                                style={{ marginRight: "6px" }}
                              />
                              <span style={{ textAlign: "left", fontWeight: "normal" }}>
                                {value}
                              </span>
                            </div>
                          )),
                      ]}
                    </form>
                  </div>
                )}
              </th>
            ))}
          </tr>
        </thead>


        <tbody>

          {filteredMessages.length > 0 && filteredMessages.map((msg, idx) => (
            <tr
              key={idx}
              style={{
                backgroundColor: idx % 2 === 0 ? "#ffffff" : "#e8e6e6",
                height: "25px", // Consistent row height
              }}
            >
              {tableHeaders.map((key) => (
                <td
                  key={key}
                  style={{
                    textAlign: "left",
                    borderBottom: "1px solid #ddd",
                    fontSize: key === "msg" ? "16px" : "14px",
                    wordBreak: "break-word",
                  }}
                >
                  {msg[ key ] || ""}
                </td>
              ))}
            </tr>
          ))}

          {filteredMessages.length > 0 && filteredMessages.length < 10 && (
            Array.from({ length: 10 - filteredMessages.length }, (_, idx) => (
              <tr
                key={filteredMessages.length + idx} // Ensure unique key for each empty row
                style={{
                  backgroundColor: (filteredMessages.length + idx) % 2 === 0 ? "#ffffff" : "#e8e6e6", // Alternating row colors
                  height: "25px", // Row height, adjust as needed
                }}
              >
                {tableHeaders.map((key) => (
                  <td
                    key={key}
                    style={{
                      textAlign: "left",
                      padding: key === "msg" ? "12px 10px" : "6px 10px",
                      borderBottom: "1px solid #ddd",
                      fontSize: key === "msg" ? "16px" : "14px",
                    }}
                  >
                    {/* Empty cell content */}
                  </td>
                ))}
              </tr>
            ))
          )}

          {filteredMessages.length === 0 && (
            Array.from({ length: 10 }, (_, idx) => (
              <tr
                key={idx}
                style={{
                  backgroundColor: idx % 2 === 0 ? "#ffffff" : "#e8e6e6",
                }}
              >
                {tableHeaders.map((key) => (
                  <td
                    key={key}
                    style={{
                      textAlign: "left", // Left-align all data cells
                      padding: key === "msg" ? "12px 10px" : "6px 10px", // Bigger padding for 'msg'
                      borderBottom: "1px solid #ddd",
                      fontSize: key === "msg" ? "16px" : "14px", // Bigger font size for 'msg'
                    }}
                  >
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
});
