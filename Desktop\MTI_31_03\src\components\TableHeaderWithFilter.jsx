import React from 'react';

const TableHeaderWithFilter = ({
  col,
  columnDisplayNames,
  hasFilter,
  selectedItems,
  handleFilterToggle,
  filterIcon
}) => {
  return (
    <div style={{
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      width: "100%"
    }}>
      {/* Centered text */}
      <div style={{
        fontSize: "14px",
        fontWeight: "600",
        wordBreak: "break-word",
        hyphens: "auto",
        lineHeight: "1",
        margin: "0",
        padding: "0",
        textAlign: "center",
        flex: "1"
      }}>
        {columnDisplayNames[ col ] ||
          col.charAt(0).toUpperCase() + col.slice(1).replace(/([A-Z])/g, " $1")}
      </div>

      {/* Right aligned icons */}
      <div style={{
        display: "flex",
        alignItems: "center",
        gap: "2px",
        marginLeft: "4px"
      }}>
        <img
          src={filterIcon}
          alt="filter"
          style={{
            height: "18px",
            width: "18px",
            cursor: "pointer",
            filter: hasFilter ? "invert(43%) sepia(97%) saturate(1752%) hue-rotate(200deg) brightness(99%) contrast(96%)" : "none"
          }}
          onClick={(e) => handleFilterToggle(col, e)}
        />

        {hasFilter && (
          <span
            style={{
              backgroundColor: "#1976d2",
              color: "white",
              borderRadius: "50%",
              width: "16px",
              height: "16px",
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "10px"
            }}
          >
            {selectedItems}
          </span>
        )}
      </div>
    </div>
  );
};

export default TableHeaderWithFilter;
