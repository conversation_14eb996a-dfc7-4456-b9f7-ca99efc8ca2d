import { useState, useEffect, useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { debounce } from "lodash";
import {
  setBrokers,
  setStrategies,
  setAuth,
  setPortfolios,
  setExpiries,
  setPositions,
  setOrders,
  setConsoleMsgs,
  setProfileImage,
} from "./store/slices";
import { setmasterChildAccounts } from "./store/slices/master_child";
import { setMasterChildPnL } from "./store/slices/masterChildReducer";
import { updateMarketData } from "./store/slices/marketSlice";
import { useNavigate, useLocation } from "react-router-dom";
import { EventSourcePolyfill } from "event-source-polyfill";
import AppRoutes from "./routes";
import Cookies from "universal-cookie";
import { useFetchPortfolios } from "./hooks/useFetchPortfolios";
import { fetchWithAuth } from "./utils/api";

function App() {
  const { pathname } = useLocation();
  const cookies = new Cookies();
  const token = cookies.get("TOKEN");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const fetchPortfolios = useFetchPortfolios();

  const mainUser = cookies.get("USERNAME");
  const status = cookies.get('subscription_type');
  const session_id = cookies.get('session_id');
  const isSubscribed = status && session_id !== "undefined" ? status : null;

  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;
      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.startegy &&
        lastMsg.portfolio === Msg.porttfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };

  const expiries = {
    NIFTY: [],
    FINNIFTY: [],
    BANKNIFTY: [],
  };

  const futExpiries = {
    NIFTY: [],
    FINNIFTY: [],
    BANKNIFTY: [],
  };

  const { brokers: rows } = useSelector((state) => state.brokerReducer);

  const handleFetch = async (url, options = {}) => {
    try {
      const response = await fetchWithAuth(url, options);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Error occurred");
      }
      return response.json();
    } catch (error) {
      console.error(error);
      throw error;
    }
  };


  const fetchAccountDetails = async () => {
    try {
      const response = await handleFetch(`/api/get_user_data/${mainUser}`);
      const brokers = response.broker_credentials.map((data) => ({
        enabled: true,
        mtmAll: "0",
        net: "0",
        availableMargin: data.available_balance || "0.00",
        name: data.display_name,
        userId: data.broker_user_id || "",
        fyersclientId: data.client_id || "",
        broker: data.broker || "",
        secretKey: data.secret_key || "",
        apiKey: data.api_key || "",
        qrCode: data.qr_code || "",
        maxProfit: data.max_profit,
        maxLoss: data.max_loss,
        profitLocking: data.profit_locking
          ? data.profit_locking.split(",").join("~")
          : "~~~",
        reached_profit: data.reached_profit,
        locked_min_profit: data.locked_min_profit,
        qtyByExposure: "0",
        maxLossPerTrade: data.max_loss_per_trade,
        qtyMultiplier: data.user_multiplier,
        mobile: "",
        email: "",
        apiUserDetails: "",
        password: data.password,
        autoLogin: true,
        historicalApi: false,
        inputDisabled: false,
        showPopup: false,
        utilized_Margin: data.utilized_margin,
        maxOpenTrades: data.max_open_trades,
        sqOffTime: data.exit_time,
      }));
      dispatch(
        setBrokers({
          brokers: brokers,
        }),
      );
    } catch (error) {
      console.error("Error fetching account details:", error);
    }
  };
  const fetchStrategy = async () => {
    try {
      const response = await handleFetch(`/api/retrieve_strategy_info/${mainUser}`);
      const strategies = response.strategies.map((item) => ({
        enabled: true,
        logged: false,
        ManualSquareOff: "",
        StrategyLabel: item.strategy_tag,
        PL: "0",
        TradeSize: "0",
        DuplicateSignalPrevention: "0",
        OpenTime: item.open_time || "00:00:00",
        CloseTime: item.close_time || "00:00:00",
        SqOffTime: item.square_off_time || "00:00:00",
        TradingAccount: item.broker_user_id.join(", "),
        Multiplier: item.broker_user_id
          .map((id) => item.multiplier[ id ] || "1")
          .join(", "),
        MaxProfit: item.max_profit || "0",
        MaxLoss: item.max_loss || "0",
        MaxLossWaitTime: "00:00:00",
        ProfitLocking: item.profit_locking
          ? item.profit_locking.split(",").join("~")
          : "~~~",
        reached_profit: item.reached_profit || "0",
        locked_min_profit: item.locked_min_profit || "0",
        DelayBetweenUsers: "0",
        UniqueIDReqforOrder: "",
        CancelPreviousOpenSignal: "",
        StopReverse: "",
        PartMultiExists: "",
        HoldSellSeconds: "00",
        AllowedTrades: item.allowed_trades,
        EntryOrderRetry: item.entry_order_retry,
        EntryRetryCount: item.entry_retry_count,
        EntryRetryWaitSeconds: item.entry_retry_wait,
        ExitOrderRetry: item.exit_order_retry,
        ExitRetryCount: item.exit_retry_count,
        ExitRetryWaitSeconds: item.exit_retry_wait,
        ExitMaxWaitSeconds: item.exit_max_wait,
        SqOffDone: "",
        Delta: "0",
        Theta: "0",
        Vega: "0",
      }));
      dispatch(
        setStrategies({
          strategies: strategies,
        })
      );
    } catch (error) {
      console.error("Error fetching strategies:", error);
    }
  };

  const fetchMasterAccounts = async () => {
    try {
      const MasterChildAccounts = await handleFetch(`/api/fetch_master_child_accounts/${mainUser}`);
      dispatch(
        setmasterChildAccounts({
          masterChildAccounts: MasterChildAccounts,
        }),
      );
    } catch (error) {
      console.error("Error fetching master accounts:", error);
    }
  };

  useEffect(() => {
    // Initialize profile image from localStorage on app startup
    const cachedImage = localStorage.getItem("PROFILE_IMAGE");
    if (cachedImage && cachedImage !== "null" && cachedImage !== "undefined") {
      dispatch(setProfileImage({ profileImage: cachedImage }));
    }

    if (mainUser) {
      dispatch(setAuth({ isAuthenticated: true }));
      fetchPortfolios();
      fetchAccountDetails();
      fetchStrategy();
      fetchExpiries(Object.keys(expiries));
      fetchMasterAccounts();
    } else if (pathname.includes("Register")) {
      navigate("/Register");
    } else {
      navigate("/");
    }
  }, [ mainUser, dispatch ]);

  const fetchExpiries = async (symbols) => {
    try {
      const expiryResponse = await handleFetch(`/api/get_expiry_list_blueprint/${mainUser}`, {
        method: "POST",
        body: JSON.stringify({ symbols }),
      });

      Object.keys(expiries).forEach((key) => {
        expiries[ key ] = expiryResponse[ key ].map((date) => date.toUpperCase());
      });

      const futuresResponse = await handleFetch(`/api/get_future_expiry_list_blueprint/${mainUser}`, {
        method: "POST",
        body: JSON.stringify({ symbols, FUT: "FUTIDX", exch_seg: "NFO" }),
      });

      Object.keys(futExpiries).forEach((key) => {
        futExpiries[ key ] = futuresResponse[ key ].map((date) => date.toUpperCase());
      });

      dispatch(
        setExpiries({
          ...expiries,
          FUTNIFTY: futExpiries[ "NIFTY" ],
          FUTBANKNIFTY: futExpiries[ "BANKNIFTY" ],
          FUTFINNIFTY: futExpiries[ "FINNIFTY" ],
        })
      );
    } catch (error) {
      console.error("Error fetching expiries:", error);
    }
  };

  const [ Broker, setBroker ] = useState({
    angelone: [],
    fyers: [],
    flattrade: [],
    pseudo_account: [],
  });

  useEffect(() => {
    const loggedInRows = rows.filter((row) => row.inputDisabled);
    let updatedBroker = {
      angelone: [],
      fyers: [],
      flattrade: [],
      pseudo_account: [],
    };

    loggedInRows.forEach((row) => {
      if (row.broker === "angelone") {
        updatedBroker.angelone.push(row.userId);
      } else if (row.broker === "fyers") {
        updatedBroker.fyers.push(row.userId);
      } else if (row.broker === "flattrade") {
        updatedBroker.flattrade.push(row.userId);
      } else if (row.broker === "pseudo_account") {
        updatedBroker.pseudo_account.push(row.userId);
      }
    });

    setBroker((prev) => {
      const isEqual = (prevArray, newArray) =>
        prevArray.length === newArray.length &&
        prevArray.every((value, index) => value === newArray[ index ]);

      if (
        !isEqual(prev.angelone, updatedBroker.angelone) ||
        !isEqual(prev.fyers, updatedBroker.fyers) ||
        !isEqual(prev.flattrade, updatedBroker.flattrade) ||
        !isEqual(prev.pseudo_account, updatedBroker.pseudo_account)
      ) {
        return updatedBroker;
      }
      return prev;
    });
  }, [ rows ]);

  const [ sseInitialized, setSseInitialized ] = useState(false);

  // Debounced message processing to prevent UI blocking
  const debouncedUpdatePnL = useCallback(
    debounce((strategyLevelPnL, userLevelPnL, portfolioLevelPnL, masterChildPnL) => {
      updatePnLData(strategyLevelPnL, userLevelPnL, portfolioLevelPnL, masterChildPnL);
    }, 100),
    []
  );

  const debouncedDispatchOrders = useCallback(
    debounce((orders, positions) => {
      dispatch(setOrders({ orders }));
      dispatch(setPositions({ positions }));
    }, 50),
    [ dispatch ]
  );

  const fetchBrokersData = () => {
    if (sseInitialized) return;

    if (!token) {
      console.error("No TOKEN found in cookies.");
      return;
    }

    let eventSource;
    let retryCount = 0;
    const maxRetries = 5;
    const retryDelay = 2000;

    const connectSSE = () => {
      eventSource = new EventSourcePolyfill(`/api/orderbook_stream/${mainUser}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setSseInitialized(true);
      let mergedOrderbook = [];
      let mergedPositions = [];
      const mergeBrokerData = (broker) => {
        broker.forEach((user) => {
          const userKey = Object.keys(user)[ 0 ];
          if (user[ userKey ]?.orderbook) {
            mergedOrderbook = [ ...mergedOrderbook, ...user[ userKey ].orderbook ];
          }
          if (user[ userKey ]?.positions) {
            const positionsWithBrokerId = user[ userKey ].positions.map((position) => ({
              ...position,
              broker_user_id: userKey,
            }));
            mergedPositions = [ ...mergedPositions, ...positionsWithBrokerId ];
          }
        });
      };
      const brokers = [ "angelone", "fyers", "flattrade", "pseudo_account" ];
      eventSource.onmessage = (event) => {
        // Use requestIdleCallback or setTimeout to prevent blocking
        const processMessage = () => {
          try {
            const data = JSON.parse(event.data);
            if (data.status === "success") {
              mergedOrderbook = [];
              mergedPositions = [];

              // Process brokers data in smaller chunks
              brokers.forEach((broker) => {
                if (data.data[ broker ] && data.data[ broker ].length > 0) {
                  mergeBrokerData(data.data[ broker ]);
                }
              });

              // Use debounced dispatch to prevent excessive updates
              debouncedDispatchOrders(mergedOrderbook, mergedPositions);

              const strategyLevelPnL = data.strategy_level_pnl;
              const userLevelPnL = data.user_level_pnl;
              const portfolioLevelPnL = data.portfolio_level_pnl;
              const masterChildPnL = data.masterchild_pnl;

              retryCount = 0;

              const statusUserMapping = {
                userLevelSq: (id) => ({ user: id, strategy: null, portfolio: null }),
                strategyLevelSq: () => ({ user: "", strategy: "strategy_level", portfolio: null }),
                userLevelProfitTrail: (id) => ({ user: id, strategy: null, portfolio: null }),
                strategyLevelProfitTrail: () => ({ user: null, strategy: "strategy_trail", portfolio: null }),
                portfolioLegLevel: () => ({ user: null, strategy: null, portfolio: "portfolio_leg" }),
                portfolioLegLevelSL: () => ({ user: null, strategy: null, portfolio: "portfolio_leg" }),
                portfolioLegLevelPT: () => ({ user: null, strategy: null, portfolio: "portfolio_leg" }),
                portfolioLevelSqTime: () => ({ user: null, strategy: null, portfolio: "portfolio_leg" }),
                maxLossPerTrade: (id) => ({ user: id, strategy: null, portfolio: null }),
              };

              const statuses = {
                userLevelSq: data?.user_maxprofit_maxloss_square_off_status || {},
                strategyLevelSq: data?.strategy_maxprofit_maxloss_square_off_status || {},
                userLevelProfitTrail: data?.user_profit_trailing_square_off_status || {},
                strategyLevelProfitTrail: data?.strategy_profit_trailing_square_off_status || {},
                portfolioLegLevel: data?.portfolio_leg_level_target_square_off_status || {},
                portfolioLegLevelSL: data?.portfolio_leg_level_sl_value_squareoff_status || {},
                portfolioLegLevelPT: data?.portfolio_leg_level_profit_trailing_squareoff_status || {},
                portfolioLevelSqTime: data?.portfolio_square_off_response_status || {},
                maxLossPerTrade: data?.sq_off_maxloss_per_trade_status || {},
              };

              if (!window.processedMessages) {
                window.processedMessages = new Set();
              }
              const processedMessages = window.processedMessages;

              Object.entries(statuses).forEach(([ statusKey, statusData ]) => {
                Object.entries(statusData).forEach(([ id, message ]) => {
                  const uniqueIdentifier = `${statusKey}-${id.trim()}-${message?.trim()}`;
                  if (processedMessages.has(uniqueIdentifier)) {
                    return;
                  }
                  processedMessages.add(uniqueIdentifier);

                  const mapping = statusUserMapping[ statusKey ]
                    ? statusUserMapping[ statusKey ](id)
                    : { user: id, strategy: null, portfolio: null };

                  if (statusKey === "strategyLevelSq" || statusKey === "strategyLevelProfitTrail") {
                    mapping.strategy = `${id}`;
                  }
                  if (statusKey === "portfolioLegLevel" || statusKey === "portfolioLegLevelSL" || statusKey === "portfolioLegLevelPT" || statusKey === "portfolioLevelSqTime") {
                    mapping.portfolio = `${id}`;
                  }

                  handleMsg({
                    msg: `${message || ""}`,
                    logType: "TRADING",
                    timestamp: `${new Date().toLocaleString()}`,
                    user: mapping.user,
                    strategy: mapping?.strategy,
                    portfolio: mapping?.portfolio,
                  });
                });
              });

              // Use debounced PnL update to prevent blocking
              debouncedUpdatePnL(strategyLevelPnL, userLevelPnL, portfolioLevelPnL, masterChildPnL);
            }
          } catch (error) {
            console.error("Error processing SSE message:", error);
          }
        };

        // Use requestIdleCallback for better performance, fallback to setTimeout
        if (window.requestIdleCallback) {
          window.requestIdleCallback(processMessage, { timeout: 100 });
        } else {
          setTimeout(processMessage, 0);
        }
      };

      eventSource.onopen = () => {
        console.log("SSE connection opened successfully");
        retryCount = 0;
      };

      eventSource.onerror = (error) => {
        console.error("SSE error:", {
          error,
          readyState: eventSource.readyState,
          url: eventSource.url,
        });

        if (eventSource.readyState === 2) {
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`Reconnection attempt ${retryCount}/${maxRetries} in ${retryDelay / 1000} seconds`);
            setTimeout(() => {
              eventSource.close();
              connectSSE();
            }, retryDelay);
          } else {
            eventSource.close();
          }
        } else {
          console.log("Error occurred, but connection not fully closed yet.");
        }
      };
    };
    connectSSE();

    return () => {
      if (eventSource) {
        eventSource.close();
        console.log("SSE connection closed on cleanup");
      }
    };
  };
  const updatePnLData = (strategyLevelPnL, userLevelPnL, portfolioLevelPnL, masterChildPnL) => {
    dispatch(async (dispatch, getState) => {
      const { brokers } = getState().brokerReducer;
      const { strategies } = getState().strategyReducer;
      const { portfolios } = getState().portfolioReducer;

      const safeNumber = (value) => (isNaN(Number(value)) || value === undefined || value === null ? 0 : Number(value));

      let hasBrokerUpdates = false;
      let hasStrategyUpdates = false;
      let hasPortfolioUpdates = false;
      let hasMasterChildUpdates = false;

      // Update brokers
      const updatedBrokers = brokers.map((broker) => {
        const userId = broker.userId;
        if (userLevelPnL[ userId ] !== undefined) {
          hasBrokerUpdates = true;
          return {
            ...broker,
            mtmAll: safeNumber(userLevelPnL[ userId ]),
          };
        }
        return { ...broker };
      });

      // Update strategies
      const updatedStrategies = strategies.map((strategy) => {
        const strategyName = strategy.StrategyLabel;
        if (strategyLevelPnL[ strategyName ] !== undefined) {
          hasStrategyUpdates = true;
          return {
            ...strategy,
            PL: safeNumber(strategyLevelPnL[ strategyName ]),
          };
        }
        return strategy;
      });

      // Update portfolios
      const updatedPortfolios = portfolios.map((portfolio) => {
        const portfolioKey = portfolio.portfolio_name;
        const portfolioPnL = portfolioLevelPnL[ portfolioKey ] || {};

        const totalPnl = portfolioPnL.total_pnl !== undefined
          ? safeNumber(portfolioPnL.total_pnl)
          : safeNumber(portfolio.totalPnl);
        const brokerUserPnls = portfolioPnL.broker_user_pnls !== undefined
          ? portfolioPnL.broker_user_pnls
          : portfolio.brokerUserPnls || {};

        if (portfolioPnL.total_pnl !== undefined || portfolioPnL.broker_user_pnls !== undefined) {
          hasPortfolioUpdates = true;
          return {
            ...portfolio,
            totalPnl,
            brokerUserPnls,
          };
        }
        return { ...portfolio };
      });

      const updatedMasterChildPnL = masterChildPnL || {};
      if (Object.keys(updatedMasterChildPnL).length > 0) {
        hasMasterChildUpdates = true;
      }

      // Dispatch updates
      if (hasBrokerUpdates) {
        dispatch(setBrokers({ brokers: updatedBrokers }));
      }
      if (hasStrategyUpdates) {
        dispatch(setStrategies({ strategies: updatedStrategies }));
      }
      if (hasPortfolioUpdates) {
        dispatch(setPortfolios({ portfolios: updatedPortfolios }));
      }
      if (hasMasterChildUpdates) {
        dispatch(setMasterChildPnL({ masterChildPnL: updatedMasterChildPnL }));
      }
    });
  };

  const prevBrokerLength = useRef(0);

  useEffect(() => {
    const brokers = Object.values(Broker).reduce((acc, curr) => [ ...acc, ...curr ], []);
    const currentLength = brokers.length;


    if (currentLength > 0 && !prevBrokerLength.current) {
      fetchBrokersData();
      prevBrokerLength.current = true;
    }
  }, [ Broker ]);

  useEffect(() => {
    const indexMapping = {
      "********": "sensex",
      "********": "nifty50",
      "********": "niftybank",
      "********": "finnifty",
    };

    const client = new WebSocket("ws://122.169.206.202:1818");

    client.onmessage = (message) => {
      // Process WebSocket messages asynchronously to prevent blocking
      const processWebSocketMessage = () => {
        try {
          const data = JSON.parse(message.data);
          if (!data.token) {
            return;
          }
          const token = JSON.parse(data.token);
          const indexName = indexMapping[ token ];

          if (indexName) {
            const lastTradedPrice = parseFloat(data.lastTradedPrice);
            const closePrice = parseFloat(data.closePrice);

            if (!isNaN(lastTradedPrice) && !isNaN(closePrice)) {
              const indexData = {
                c: lastTradedPrice.toFixed(2),
                ch: (lastTradedPrice - closePrice).toFixed(2),
                chp: ((lastTradedPrice - closePrice) / lastTradedPrice * 100).toFixed(2),
              };
              dispatch(updateMarketData({ indexName, indexData }));
            }
          }
        } catch (error) {
          console.error("Error processing WebSocket message:", error);
        }
      };

      // Use setTimeout to prevent blocking the main thread
      setTimeout(processWebSocketMessage, 0);
    };

    client.onclose = async () => {
      console.log("WebSocket closed. Fetching LTP from API...");
    };

    client.onerror = (error) => {
      console.error("WebSocket error:", error);
      client.close();
    };

    return () => {
      client.close();
    };
  }, [ dispatch ]);

  return (
    <>
      <AppRoutes isSubscribed={isSubscribed} />
    </>
  );
}

export default App;
