import React from "react";

const AppearanceSection = () => (
    <div className="appearance-section" style={{
        padding: "1px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box"
    }}>

        <div style={{ marginBottom: "2px" }}>
            <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "2px" }}>Grids Visibility Settings</h3>
            <div style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                gap: "5px",
                background: "#f8fafc",
                padding: "5px",
                borderRadius: "6px"
            }}>
                {[
                    "Show Symbol Mapping Grid", "Show Signals Grid", "Show Order Summary Grid",
                    "Show Order Book Grid", "Show Positions Grid", "Show Holdings Grid",
                    "Show Multi-Leg Grid", "Show Remote Share Grid"
                ].map((item) => (
                    <label key={item} style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>{item}</span>
                    </label>
                ))}
            </div>
        </div>

        <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
            gap: "10px",
            marginBottom: "1px"
        }}>
            <div>
                <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "2px" }}>Notifications</h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Show Notifications</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "14px" }}>Play Sound</span>
                    </label>
                </div>
            </div>

            <div>
                <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "3px" }}>Other Settings</h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Allow Shortcuts (Shift + F1, F2, and F3)</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "3px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Speed Optimizations</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "3px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Show Order Updates in Log Grid</span>
                    </label>
                </div>
            </div>
        </div>

        <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
            gap: "10px",
            marginBottom: "1px"
        }}>
            <div>
                <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "2px" }}>Reset Appearance</h3>
                <div style={{ background: "#f8fafc", padding: "10px", borderRadius: "6px" }}>
                    <label style={{ display: "block", marginBottom: "2px", fontSize: "12px" }}>
                        Reset Appearance to Default
                        <span style={{ color: "#e53e3e", marginLeft: "5px" }}>(Do not reset while trading)</span>
                    </label>
                    <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                        <select style={{
                            flex: 1,
                            padding: "8px",
                            borderRadius: "6px",
                            border: "1px solid #ddd",
                            fontSize: "14px",
                            background: "#fff"
                        }}>
                            <option value="default">Do not reset while trading</option>
                        </select>
                        <button style={{
                            padding: "8px 20px",
                            background: "#d8e1ff",
                            color: "#4661bd",
                            border: "none",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "14px",
                            fontWeight: "600",
                            transition: "background 0.3s ease"
                        }}
                            onMouseEnter={(e) => e.target.style.background = "#c7d2fe"}
                            onMouseLeave={(e) => e.target.style.background = "#d8e1ff"}
                        >
                            Reset
                        </button>
                    </div>
                </div>
            </div>

            <div>
                <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "5px" }}>Advanced Settings</h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Show Symbol Mapping Grid</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>BETA Tester</span>
                    </label>
                </div>
            </div>
        </div>

        <div style={{ marginBottom: "10px" }}>
            <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "5px" }}>Proxy Settings</h3>
            <div style={{ background: "#f8fafc", padding: "10px", borderRadius: "6px" }}>
                <div style={{ display: "flex", flexDirection: "row", gap: "5px", marginBottom: "5px" }}>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="radio" name="proxy" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>No Proxy</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="radio" name="proxy" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>User System Proxy Settings</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="radio" name="proxy" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Manual Proxy Configuration</span>
                    </label>
                </div>
                <div style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
                    gap: "5px"
                }}>
                    {[ "Proxy IP", "Port", "User", "Password" ].map((field) => (
                        <label key={field} style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                            <span style={{ fontSize: "12px" }}>{field}</span>
                            <input
                                type={field === "Password" ? "password" : "text"}
                                style={{
                                    width: "100%",
                                    padding: "8px",
                                    borderRadius: "6px",
                                    border: "1px solid #ddd",
                                    fontSize: "12px"
                                }}
                            />
                        </label>
                    ))}
                </div>
            </div>
        </div>

        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "10px"
        }}>
            <button style={{
                background: "#4661bd",
                color: "white",
                border: "none",
                padding:"5px 10px",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "background 0.3s ease"
            }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Save Settings
            </button>
            <button style={{
                background: "#fff",
                color: "#666",
                border: "1px solid #ddd",
                padding:"5px 10px",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "all 0.3s ease"
            }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default AppearanceSection;