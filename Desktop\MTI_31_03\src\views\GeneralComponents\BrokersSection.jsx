import React from "react";

const BrokersSection = ({
    selectedBroker,
    setSelectedBroker,
    brokers,
    apiKey,
    setApiKey,
    apiSecret,
    setApiSecret,
    onSave,
    onCancel
}) => (
    <div className="brokers-section" style={{
        padding: "30px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box"
    }}>
        <h2 style={{
            color: "#4661bd",
            fontSize: "24px",
            marginBottom: "25px",
            fontWeight: "600"
        }}>Options Portfolio Execution Default Settings
        </h2>

        <div style={{ marginBottom: "30px" }}>
            <label style={{ display: "block", fontSize: "14px", marginBottom: "8px", fontWeight: "600" }}>
                Select Broker
            </label>
            <select
                value={selectedBroker}
                onChange={(e) => setSelectedBroker(e.target.value)}
                style={{
                    width: "300px",
                    padding: "8px",
                    borderRadius: "6px",
                    border: "1px solid #ddd",
                    fontSize: "14px",
                    background: "#fff"
                }}
            >
                <option value="">Select a broker</option>
                {brokers.map((broker) => (
                    <option key={broker.id} value={broker.id}>
                        {broker.name}
                    </option>
                ))}
            </select>
            <p style={{ fontSize: "14px", color: "#666", marginTop: "8px" }}>
                Choose your preferred broker for trading execution
            </p>
        </div>


        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "15px"
        }}>
            <button
                onClick={onSave}
                style={{
                    padding: "12px 25px",
                    background: "#4661bd",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "600",
                    transition: "background 0.3s ease"
                }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Save Settings
            </button>
            <button
                onClick={onCancel}
                style={{
                    padding: "12px 25px",
                    background: "#fff",
                    color: "#666",
                    border: "1px solid #ddd",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "600",
                    transition: "all 0.3s ease"
                }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default BrokersSection;