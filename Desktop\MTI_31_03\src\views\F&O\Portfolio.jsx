import React, { useState, useRef, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
  OptimizedMarketIndex,
  OptimizedLeftNav,
  OptimizedRightNav,
  OptimizedTopNav,
  OptimizedErrorContainer,
} from "../../components/Layout/OptimizedComponenets";
import "../../styles.css";
import Modal from "react-modal";
import Cookies from "universal-cookie";
import { useDispatch, useSelector } from "react-redux";
import { useFetchPortfolios } from "../../hooks/useFetchPortfolios";
import { setPortfolios, setAllSeq, setAllVis, setConsoleMsgs } from "../../store/slices";
import { IconButton, Tooltip } from "@mui/material";
import {
  PlayCircleOutline, Close, Edit, Delete, ContentCopy, TaskAlt,
  Replay, AttachMoney, SignalCellularAlt, Autorenew, Cancel
} from "@mui/icons-material";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import { fetchWithAuth } from "../../utils/api";
import TableHeaderWithFilter from "../../components/TableHeaderWithFilter";
import filterIcon from "../../assets/newFilter.png";


const useClickOutside = (ref, callback) => {
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        callback();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [ ref, callback ]);
};

const cookies = new Cookies();

const styles = `
  .middle-main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .main-table {
    flex: 1;
    overflow: auto;
    height: calc(92vh - 100px);
    position: relative;
  }
  .table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }
  .table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #D8E1FF;
  }
  .table th {
    font-size: 13px;
    padding: 4px 3px;
    text-align: center;
    border-bottom: 1px solid #ddd;
    white-space: normal;
    vertical-align: middle;
    height: auto;
    line-height: 1.1;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    min-width: 70px; /* Increased minimum width for better appearance */
    word-break: break-word;
    hyphens: auto;
  }
  .table td {
    padding: 4px;
    width: auto;
    text-align: center;
    vertical-align: middle;
  }
  .table tbody tr:nth-child(even),
  .table tbody tr:nth-child(even) input,
  .table tbody tr:nth-child(even) select {
    background-color: #E8E6E6;
  }
  .table tbody tr:nth-child(odd),
  .table tbody tr:nth-child(odd) input,
  .table tbody tr:nth-child(odd) select {
    background-color: #FFFFFF;
  }
  .filter-icon {
    margin-left: 2px;
    cursor: pointer;
    font-size: 16px;
    vertical-align: middle;
  }
  .tooltip-container {
    position: relative;
    display: inline-block;
    margin: 0 2px;
  }
`;

function Portfolio() {
  const errorContainerRef = useRef(null);
  const fetchPortfolios = useFetchPortfolios();
  const { collapsed } = useSelector((state) => state.collapseReducer);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const mainUser = cookies.get("USERNAME");
  const token = cookies.get("TOKEN");
  const { portfolios: portfolioDetails } = useSelector((state) => state.portfolioReducer);
  const { brokers } = useSelector((state) => state.brokerReducer);
  const { orders } = useSelector((state) => state.orderBookReducer);
  const { placeOrderStart } = useSelector((state) => state.placeOrderStartReducer);
  const allSeqState = useSelector((state) => state.allSeqReducer);
  const allVisState = useSelector((state) => state.allVisReducer);
  const { executedPortfolios } = useSelector((state) => state.executedPortfolioReducer);

  const [ msgs, setMsgs ] = useState([]);
  const [ showConfirmDeleteModal, setShowConfirmDeleteModal ] = useState(false);
  const [ portfolioToDelete, setPortfolioToDelete ] = useState("");
  const [ isTableOpen, setTableOpen ] = useState(false);
  const [ isTableOpen1, setTableOpen1 ] = useState(false);
  const [ isPlusClicked, setIsPlusClicked ] = useState({});
  const [ isPlusClicked1, setIsPlusClicked1 ] = useState({});
  const [ subTableData, setsubTableData ] = useState([]);
  const [ executedPortfolioNames, setExecutedPortfolioNames ] = useState([]);
  const [ isModalOpen, setIsModalOpen ] = useState(false);
  const [ selectedItem, setSelectedItem ] = useState(null);
  const [ newCopy, setNewCopy ] = useState(false);
  const [ portfolioName, setPortfolioName ] = useState("");
  const [ selectedPortfolio, setSelectedPortfolio ] = useState({});
  const [ error, setError ] = useState("");
  const [ openPortIndex, setOpenPortIndex ] = useState(null);
  const [ selectedBrokerPnls, setSelectedBrokerPnls ] = useState({});

  const filterPopupRef = useRef(null);
  useClickOutside(filterPopupRef, () => setFilterPopup(null));
  const [ filters, setFilters ] = useState({});
  const [ filterPopup, setFilterPopup ] = useState(null);
  const [ tempFilters, setTempFilters ] = useState({});
  const [ filteredRows, setFilteredRows ] = useState([]);
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 });

  const portfolioCols = [
    "Enabled", "Status", "Portfolio Name", "PNL", "Symbol", "Execute/Sq Off", "Edit", "Delete",
    "Make Copy", "Mark As Completed", "Reset", "Pay Off", "Chat", "Re Execute", "Part Entry/Exit",
    "Current Value", "Value Per Lot", "Underlying LTP", "Positional Portfolio", "Product",
    "Strategy", "Entry Price", "Combined Premuim", "Per Lot Premuim", "Start Time", "End Time",
    "SqOff Time", "Range End Time", "Delta", "Theta", "Vega", "Remarks", "Message",
  ];
  const [ portfolioColVis, setPortfolioColVis ] = useState(() => {
    const initialVisibility = {};
    portfolioCols.forEach(col => {
      initialVisibility[ col ] = true;
    });
    return initialVisibility;
  });

  const [ portfolioColsSelectedALL, setPortfolioColsSelectedALL ] = useState(false);
  const [ portfolioSeq, setPortfolioSeq ] = useState(allSeqState.portfolioSeq);


  const handleMsg = (msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;
      const lastMsg = previousConsoleMsgs[ 0 ];
      const updatedMsgs = (lastMsg && lastMsg.msg === msg.msg && lastMsg.user === msg.user &&
        lastMsg.strategy === msg.strategy && lastMsg.portfolio === msg.portfolio)
        ? [ msg, ...previousConsoleMsgs.slice(1) ]
        : [ msg, ...previousConsoleMsgs ];
      dispatch(setConsoleMsgs({ consoleMsgs: updatedMsgs }));
    });
    setMsgs(prev => [ ...prev, msg ]);
  };

  const handleClearLogs = () => setMsgs([]);

  useEffect(() => {
    const fetchExecutedPortfolios = async () => {
      try {
        const response = await fetchWithAuth(`/api/get_executed_portfolios/${mainUser}`, {
          method: "POST",
        });
        if (!response.ok) throw new Error("Failed to fetchWithAuth executed portfolios");
        const { ExecutedPortfolios } = await response.json();
        setExecutedPortfolioNames(ExecutedPortfolios.map(ep => ep.portfolio_name));
      } catch (error) {
        console.error("Error fetching executed portfolios:", error);
      }
    };
    if (orders?.length > 0) fetchExecutedPortfolios();
  }, [ orders?.length, mainUser ]);

  const marketData = useSelector((state) => state.marketReducer);

  const mappedPortfolios = useMemo(() => {
    return portfolioDetails.map(item => ({
      "Enabled": item.enabled,
      "Status": item.totalPnl != 0 ? "Completed" : "",
      "Portfolio Name": item.portfolio_name,
      "PNL": item.totalPnl || 0,
      "Symbol": item.stock_symbol,
      "Execute/Sq Off": "0",
      "Edit": "0",
      "Delete": "0",
      "Make Copy": "0",
      "Mark As Completed": "0",
      "Reset": "0",
      "Pay Off": "0",
      "Chat": "0",
      "Re Execute": "0",
      "Part Entry/Exit": "0",
      "Current Value": "0",
      "Value Per Lot": "0",
      "Underlying LTP": item.stock_symbol === "NIFTY" ? marketData?.marketData?.nifty50?.c :
        item.stock_symbol === "BANKNIFTY" ? marketData?.marketData?.niftybank?.c :
          item.stock_symbol === "FINNIFTY" ? marketData?.marketData?.finnifty?.c : "",
      "Positional Portfolio": item.positional_portfolio,
      "Product": item.product_type,
      "Strategy": item.strategy,
      "Entry Price": "0",
      "Combined Premuim": "0",
      "Per Lot Premuim": "0",
      "Start Time": item.start_time,
      "End Time": item.end_time,
      "SqOff Time": item.square_off_time,
      "Range End Time": "00:00:00",
      "Delta": "0",
      "Theta": "0",
      "Vega": "0",
      "Remarks": "",
      "Message": ""
    }));
  }, [ portfolioDetails ]);

  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      const filteredDataResult = mappedPortfolios.filter((row) =>
        Object.keys(filters).every((col) =>
          filters[ col ]?.length > 0 ? filters[ col ].includes(row[ col ]) : true
        )
      );
      setFilteredRows(filteredDataResult);
    } else {
      setFilteredRows(mappedPortfolios);
    }
  }, [ mappedPortfolios, filters ]);

  // Get unique values for a column, considering whether it's the first filter or not
  const getDynamicUniqueValues = (column) => {
    // For the first filter or if no filters are applied yet, show all available values from mappedPortfolios
    const isFirstFilter = Object.keys(filters).length === 0 ||
      (Object.keys(filters).length === 1 && filters[ column ]);

    // Use the original data (mappedPortfolios) for the first filter column
    // For subsequent filters, use the already filtered data
    const sourceData = isFirstFilter ? mappedPortfolios : filteredRows;

    return Array.from(
      new Set(sourceData.map((row) => row[ column ]))
    ).filter(Boolean);
  };

  const handleFilterToggle = (column, event) => {
    // Add safety check for event and event.target
    if (!event || !event.target) {
      console.warn('handleFilterToggle called without valid event or event.target');
      return;
    }

    try {
      const { top, left, height } = event.target.getBoundingClientRect();
      setFilterPopup(filterPopup === column ? null : column);
      setPopupPosition({ top: top + height, left });
      setTempFilters(filters);
    } catch (error) {
      console.error('Error in handleFilterToggle:', error);
      // Fallback: still toggle the filter popup but without positioning
      setFilterPopup(filterPopup === column ? null : column);
      setTempFilters(filters);
    }
  };

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) => selectedOptions.includes(opt));

    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [ column ]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [ column ]: [ ...currentOptions ] }));
    }
  };

  const handleApplyFilter = () => {
    const newFilters = { ...filters, [ filterPopup ]: tempFilters[ filterPopup ] || [] };
    setFilters(newFilters);
    setFilterPopup(null);
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  const portfolioColSelectALL = () => {
    const newValue = !portfolioColsSelectedALL;
    setPortfolioColsSelectedALL(newValue);

    const updatedVisibility = {};
    portfolioCols.forEach(col => {
      updatedVisibility[ col ] = !newValue;
    });

    setPortfolioColVis(updatedVisibility);

    if (!newValue) {
      setPortfolioSeq(portfolioCols);
    } else {
      setPortfolioSeq([]);
    }
  };
  useEffect(() => {
    dispatch(
      setAllVis({
        ...allVisState,
        portfolioVis: portfolioColVis,
      })
    );
  }, [ portfolioColVis ]);


  useEffect(() => {
    dispatch(
      setAllSeq({
        ...allSeqState,
        portfolioSeq: portfolioSeq,
      })
    );
  }, [ portfolioSeq ]);


  const handleDelete = (portfolioName) => {
    setShowConfirmDeleteModal(true);
    setPortfolioToDelete(portfolioName);
  };

  const handleDeleteAll = async (portfolioName) => {
    try {
      const response = await fetchWithAuth(`/api/delete_portfolio/${mainUser}/${portfolioName}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error(await response.json());
      fetchPortfolios();
      handleMsg({
        msg: "Portfolio deleted Successfully",
        logType: "MESSAGE",
        timestamp: `${new Date().toLocaleString()}`,
        portfolio: portfolioName,
        color: "red",
      });
    } catch (error) {
      console.error("Error deleting portfolio:", error);
    } finally {
      setShowConfirmDeleteModal(false);
    }
  };

  const handleEdit = (portfolio) => {
    const params = JSON.stringify({ ...portfolio, margin: "0" });
    navigate(`/Edit-Portfolio/${params}`, { ...portfolio, margin: "0" });
  };

  const handlePlusClick = (index) => {
    const isCurrentlyOpen = isPlusClicked[ index ];

    if (isCurrentlyOpen) {
      setTableOpen(false);
      setOpenPortIndex(null);
      setSelectedBrokerPnls({});
      setIsPlusClicked(Object.fromEntries(Object.keys(isPlusClicked).map(key => [ key, false ])));
    } else {
      const brokerPnls = portfolioDetails[ index ].brokerUserPnls || {};
      setTableOpen(true);
      setOpenPortIndex(index);
      setSelectedBrokerPnls({ ...brokerPnls });
      setIsPlusClicked({
        ...Object.fromEntries(Object.keys(isPlusClicked).map(key => [ key, false ])),
        [ index ]: true,
      });
      if (Object.keys(brokerPnls).length === 0) {
        console.warn('No brokerUserPnls data for portfolio at index', index);
      }
    }
  };

  useEffect(() => {
    if (openPortIndex !== null) {
      const brokerPnls = portfolioDetails[ openPortIndex ].brokerUserPnls || {};
      setSelectedBrokerPnls({ ...brokerPnls });
    }
  }, [ openPortIndex, portfolioDetails ]);

  const handlePlusClick1 = (index) => {
    setIsPlusClicked1(prev => ({
      ...Object.fromEntries(Object.keys(prev).map(key => [ key, false ])),
      [ index ]: !prev[ index ],
    }));
    setTableOpen1(Object.values({ ...isPlusClicked1, [ index ]: !isPlusClicked1[ index ] }).includes(true));
  };

  const handleSqOffClick = async (item) => {
    const brokerIds = item.Strategy_accounts_id.split(",");
    const brokerDetailsMap = Object.fromEntries(brokers.filter(b => brokerIds.includes(b.userId)).map(b => [ b.userId, b.broker ]));
    for (const brokerId of brokerIds) {
      if (brokerDetailsMap[ brokerId ]) {
        await handleSqOff(item.portfolio_name, brokerId, brokerDetailsMap[ brokerId ]);
      }
    }
  };

  useEffect(() => {
    if (openPortIndex !== "" && new Set(Object.values(isPlusClicked)).size > 0) {
      setsubTableData([ { ...portfolioDetails[ openPortIndex ] } ]);
    } else {
      setsubTableData([ { ...portfolioDetails[ openPortIndex ], brokerDetails: [] } ]);
    }
  }, [ isPlusClicked ]);


  const handleSqOff = async (portfolioName, brokerId, brokerDetail) => {
    const currentTime = new Date();
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();
    if (!((currentHours === 9 && currentMinutes >= 15) || (currentHours > 9 && currentHours < 15) || (currentHours === 15 && currentMinutes <= 30))) {
      handleMsg({
        msg: "Order not placed as current time is outside the allowed time window.",
        logType: "INFO",
        timestamp: `${new Date().toLocaleString()}`,
        color: "red",
      });
      return;
    }
    try {
      const response = await fetchWithAuth(`/api/square_off_portfolio_level/${mainUser}/${portfolioName}/${brokerDetail}/${brokerId}`, {
        method: "POST",
      });
      if (!response.ok) throw new Error((await response.json())?.message || "Failed to square off");
      const responseData = await response.json();
      handleMsg({
        msg: responseData.message,
        logType: "MESSAGE",
        timestamp: `${new Date().toLocaleString()}`,
        portfolio: portfolioName,
        user: brokerId,
      });
    } catch (error) {
      handleMsg({
        msg: `Error: ${error.message}`,
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
      });
    }
  };

  const openModal = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  const placeOrderOptionsQTP = async (item) => {
    const currentTime = new Date();
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();
    if (!((currentHours === 9 && currentMinutes >= 15) || (currentHours > 9 && currentHours < 15) || (currentHours === 15 && currentMinutes <= 30))) {
      handleMsg({
        msg: "Order not placed as current time is outside the allowed time window.",
        logType: "INFO",
        timestamp: `${new Date().toLocaleString()}`,
        color: "red",
      });
      closeModal();
      return;
    }
    if (!placeOrderStart) {
      handleMsg({
        msg: "To place an Order, Start the Trading.",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
        color: "red",
      });
      closeModal();
      return;
    }
    const { portfolio_name, Strategy_accounts_id, start_time, end_time, strategy } = item;
    const brokerIds = Strategy_accounts_id.split(",");
    for (const userId of brokerIds) {
      const user = brokers.find(u => u.userId === userId);
      if (!user || !user.inputDisabled) {
        handleMsg({
          msg: `Login the ${userId}, to place an order in this account.`,
          logType: "WARNING",
          timestamp: `${new Date().toLocaleString()}`,
          user: userId,
          strategy,
          portfolio: portfolio_name,
          color: "red",
        });
        closeModal();
        return;
      }
      const apiEndpoints = {
        fyers: [ `/api/place_order/fyers/${mainUser}/${portfolio_name}/${userId}` ],
        angelone: [ `/api/angelone_options_place_order/${mainUser}/${portfolio_name}/${userId}` ],
        flattrade: [ `/api/flatrade_place_order/${mainUser}/${portfolio_name}/${userId}` ],
        pseudo_account: [ `/api/pseudo_placeorder/${mainUser}/${portfolio_name}/${userId}` ],
      }[ user.broker ] || [];
      const currentTimeStr = currentTime.toLocaleTimeString("en-US", { hour12: false });
      const startTime = start_time === "00:00:00" ? null : start_time;
      const endTime = end_time === "00:00:00" ? null : end_time;
      if ((startTime === null || currentTimeStr >= startTime) && (endTime === null || currentTimeStr <= endTime)) {
        for (const apiEndpoint of apiEndpoints) {
          try {
            const requestBody = { qtp_lots: 1 };
            if (user.broker === "pseudo_account") requestBody.underlying_price = JSON.parse(localStorage.getItem("marketIndexDetails"));
            const res = await fetchWithAuth(apiEndpoint, {
              method: "POST",
              body: JSON.stringify(requestBody),
            });
            const responseData = await res.json();
            if (res.ok) {
              responseData.messages.forEach(message =>
                handleMsg({
                  msg: message.message,
                  logType: "TRADING",
                  timestamp: `${new Date().toLocaleString()}`,
                  user: userId,
                  strategy,
                  portfolio: portfolio_name,
                })
              );
            } else {
              handleMsg({
                msg: responseData[ 0 ].message,
                logType: "MESSAGE",
                timestamp: `${new Date().toLocaleString()}`,
                user: userId,
                strategy,
                portfolio: portfolio_name,
              });
            }
          } catch (error) {
            handleMsg({
              msg: error.message,
              logType: "ERROR",
              timestamp: `${new Date().toLocaleString()}`,
              user: userId,
              strategy,
              portfolio: portfolio_name,
            });
          }
        }
      } else {
        handleMsg({
          msg: `Order not placed for ${userId} as current time is outside the allowed time window (Start: ${startTime || "Not specified"}, End: ${endTime || "Not specified"}).`,
          logType: "INFO",
          timestamp: `${new Date().toLocaleString()}`,
          user: userId,
          strategy,
          portfolio: portfolio_name,
          color: "red",
        });
      }
    }
    closeModal();
  };

  const handleReexecute = async (item) => {
    const isExecuted = executedPortfolioNames.includes(item.portfolio_name);
    if (!isExecuted) {
      handleMsg({
        msg: `Portfolio "${item.portfolio_name}" is not completed and cannot be re-executed.`,
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
      });
      return;
    }
    const brokerIds = item.Strategy_accounts_id.split(",");
    const loggedInUser = brokers.find(user => brokerIds.includes(user.userId) && user.inputDisabled);
    if (!loggedInUser) {
      handleMsg({
        msg: "Login required to ReExecute.",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
        user: brokerIds.join(", "),
      });
      return;
    }
    let portfolioName = item.portfolio_name;
    const reExecutionMatch = portfolioName.match(/\(re_(\d+)\)$/);
    if (reExecutionMatch && parseInt(reExecutionMatch[ 1 ], 10) > 0) {
      handleMsg({
        msg: `You cannot reexecute for this portfolio name "${portfolioName}" as it was already re-executed.`,
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
      });
      return;
    }
    portfolioName = reExecutionMatch ? portfolioName.replace(/\(re_\d+\)$/, `(re_${parseInt(reExecutionMatch[ 1 ], 10) + 1})`) : `${portfolioName}(re_1)`;
    const updatedItem = { ...item, portfolio_name: portfolioName };
    try {
      const response = await fetchWithAuth(`/api/store_portfolio/${mainUser}`, {
        method: "POST",
        body: JSON.stringify(updatedItem),
      });
      if (!response.ok) throw new Error((await response.json()).message);
      fetchPortfolios();
      handleMsg({
        msg: `Portfolio re-executed successfully: ${portfolioName}`,
        logType: "SUCCESS",
        timestamp: `${new Date().toLocaleString()}`,
      });
      handlechangebox(portfolioName, false);
      const brokerDetailsMap = Object.fromEntries(brokers.filter(b => brokerIds.includes(b.userId)).map(b => [ b.userId, b.broker ]));
      for (const brokerId of brokerIds) {
        if (brokerDetailsMap[ brokerId ]) {
          await handleReexecuteWithBroker(portfolioName, brokerId, brokerDetailsMap[ brokerId ], updatedItem.strategy);
        }
      }
    } catch (error) {
      handleMsg({
        msg: `You cannot reexecute for the reexecuted portfolio: ${error.message}`,
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
      });
    }
  };


  const handleReexecuteWithBroker = async (portfolioName, brokerId, broker, strategyName) => {
    const user = brokers.find(u => u.userId === brokerId);
    if (!user || !user.inputDisabled) {
      handleMsg({
        msg: "Login required to place an order in this account.",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
        user: brokerId,
        strategy: strategyName,
        portfolio: portfolioName,
      });
      return;
    }
    const optionType = portfolioDetails.find(p => p.portfolio_name === portfolioName)?.legs?.some(leg => leg.option_type === "FUT") ? "FUT" : null;
    const apiEndpoints = {
      fyers: [ `/api/${optionType === "FUT" ? "fyers_futures" : "place_order"}/fyers/${mainUser}/${portfolioName}/${brokerId}` ],
      angelone: [ `/api/${optionType === "FUT" ? "angelone_future" : "angelone_options"}/angelone/${mainUser}/${portfolioName}/${brokerId}` ],
      flattrade: [ `/api/${optionType === "FUT" ? "flatrade_future" : "flatrade"}/flattrade/${mainUser}/${portfolioName}/${brokerId}` ],
      pseudo_account: [ `/api/pseudo_placeorder/${mainUser}/${portfolioName}/${brokerId}` ],
    }[ broker ] || [];
    try {
      const response = await fetchWithAuth(apiEndpoints[ 0 ], {
        method: "POST",
        body: JSON.stringify({ qtp_lots: 1 }),
      });
      const responseData = await response.json();
      if (response.ok) {
        responseData.messages.forEach(message =>
          handleMsg({
            msg: message.message,
            logType: "TRADING",
            timestamp: `${new Date().toLocaleString()}`,
            user: brokerId,
            strategy: strategyName,
            portfolio: portfolioName,
          })
        );
      } else {
        handleMsg({
          msg: responseData[ 0 ].message,
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
          user: brokerId,
          strategy: strategyName,
          portfolio: portfolioName,
        });
      }
    } catch (error) {
      handleMsg({
        msg: error.message,
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
        user: brokerId,
        strategy: strategyName,
        portfolio: portfolioName,
      });
    }
  };

  const handleOpenCopy = (item) => {
    setSelectedPortfolio(item);
    setNewCopy(true);
  };

  const handleCloseCopy = () => {
    setNewCopy(false);
    setPortfolioName("");
    setError("");
  };

  const handlenewMakeCopy = async () => {
    if (portfolioDetails.some(p => p.portfolio_name === portfolioName)) {
      setError("Portfolio name already exists");
      return;
    }
    setError("");
    const updatedItem = { ...selectedPortfolio, portfolio_name: portfolioName || selectedPortfolio.portfolio_name };
    try {
      const response = await fetchWithAuth(`/api/store_portfolio/${mainUser}`, {
        method: "POST",
        body: JSON.stringify(updatedItem),
      });
      if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
      fetchPortfolios();
    } catch (error) {
      console.error("Error making copy:", error);
    } finally {
      handleCloseCopy();
    }
  };

  const handlechangebox = async (portfolioName, enabled) => {
    try {
      const response = await fetchWithAuth(`/api/enable_portfolio/${mainUser}/${portfolioName}`, {
        method: "POST",
        body: JSON.stringify({ enable_status: !enabled ? "True" : "False" }),
      });
      if (response.ok) {
        const res = await response.json();
        fetchPortfolios();
        handleMsg({
          msg: res.message,
          logType: "MESSAGE",
          timestamp: `${new Date().toLocaleString()}`,
          portfolio: portfolioName,
        });
      }
    } catch (error) {
      console.error("Error enabling/disabling portfolio:", error);
    }
  };

  // Helper function to create table headers with filters
  const createFilterableHeader = (colName, hasColSpan = false) => {
    if (!portfolioColVis[ colName ]) return null;

    const hasFilter = filters[ colName ] && filters[ colName ].length > 0;
    const selectedItems = filters[ colName ]?.length || 0;

    return (
      <th
        colSpan={hasColSpan ? "2" : "1"}
        style={{
          fontSize: "15px",
          padding: "4px 3px",
          textAlign: "center",
          backgroundColor: hasFilter ? "#f0f7ff" : "inherit",
          borderBottom: hasFilter ? "2px solid #1976d2" : "inherit",
          height: "auto",
          verticalAlign: "middle"
        }}
      >
        <div style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          padding: "0",
          margin: "0",
          gap: "4px",
          width: "100%"
        }}>
          <TableHeaderWithFilter
            col={colName}
            columnDisplayNames={{}}
            hasFilter={hasFilter}
            selectedItems={selectedItems}
            handleFilterToggle={handleFilterToggle}
            filterIcon={filterIcon}
          />
        </div>
      </th>
    );
  };

  // Simple header without filter
  const createSimpleHeader = (colName, hasColSpan = false) => {
    if (!portfolioColVis[ colName ]) return null;

    return (
      <th
        colSpan={hasColSpan ? "2" : "1"}
        style={{
          fontSize: "15px",
          padding: "4px 3px",
          textAlign: "center",
          height: "auto",
          verticalAlign: "middle",
          minWidth: "70px",
          wordBreak: "break-word",
          hyphens: "auto"
        }}
      >
        <div style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          padding: "0",
          margin: "0",
          width: "100%"
        }}>
          <div style={{
            fontSize: "14px",
            fontWeight: "600",
            wordBreak: "break-word",
            hyphens: "auto",
            lineHeight: "1",
            margin: "0",
            padding: "0"
          }}>
            {colName}
          </div>
        </div>
      </th>
    );
  };

  const portfolioTH = {
    "Enabled": createSimpleHeader("Enabled", true),
    "Status": createSimpleHeader("Status"),
    "Portfolio Name": createFilterableHeader("Portfolio Name"),
    "PNL": createSimpleHeader("PNL"),
    "Symbol": createFilterableHeader("Symbol"),
    "Execute/Sq Off": createSimpleHeader("Execute/Sq Off"),
    "Edit": createSimpleHeader("Edit"),
    "Delete": createSimpleHeader("Delete"),
    "Make Copy": createSimpleHeader("Make Copy"),
    "Mark As Completed": createSimpleHeader("Mark As Completed"),
    "Reset": createSimpleHeader("Reset"),
    "Pay Off": createSimpleHeader("Pay Off"),
    "Chat": createSimpleHeader("Chat"),
    "Re Execute": createSimpleHeader("Re Execute"),
    "Part Entry/Exit": createSimpleHeader("Part Entry/Exit"),
    "Current Value": createSimpleHeader("Current Value"),
    "Value Per Lot": createSimpleHeader("Value Per Lot"),
    "Underlying LTP": createSimpleHeader("Underlying LTP"),
    "Positional Portfolio": createSimpleHeader("Positional Portfolio"),
    "Product": createFilterableHeader("Product"),
    "Strategy": createFilterableHeader("Strategy"),
    "Entry Price": createSimpleHeader("Entry Price"),
    "Combined Premuim": createSimpleHeader("Combined Premuim"),
    "Per Lot Premuim": createSimpleHeader("Per Lot Premuim"),
    "Start Time": createSimpleHeader("Start Time"),
    "End Time": createSimpleHeader("End Time"),
    "SqOff Time": createSimpleHeader("SqOff Time"),
    "Range End Time": createSimpleHeader("Range End Time"),
    "Delta": createSimpleHeader("Delta"),
    "Theta": createSimpleHeader("Theta"),
    "Vega": createSimpleHeader("Vega"),
    "Remarks": createSimpleHeader("Remarks"),
    "Message": createSimpleHeader("Message"),
  };
  const [ hoverData, setHoverData ] = useState(null);

  const portfolioTD = (item, index) => {
    const isExecuted = executedPortfolioNames.includes(item[ "Portfolio Name" ]);
    const isExecuted1 = executedPortfolios.some(ep => ep.portfolio_name === item[ "Portfolio Name" ]);
    const squareOffPortfolios = executedPortfolios.some(ep => ep.portfolio_name === item[ "Portfolio Name" ] && ep.square_off);

    return {
      "Enabled": portfolioColVis[ "Enabled" ] && (
        <td colSpan="2" style={{ textAlign: "right", }}>
          {item[ "Portfolio Name" ] && (
            <>
              <span
                style={{ fontSize: "30px", fontWeight: "bold", marginRight: "25px", cursor: "pointer", width: "20px" }}
                onClick={() => handlePlusClick(index)}
              >
                {isPlusClicked[ index ] ? "-" : "+"}
              </span>
              <input
                type="checkbox"
                checked={item[ "Enabled" ]}
                style={{ transform: "scale(1.5)", margin: "10px", }}
                onChange={() => handlechangebox(item[ "Portfolio Name" ], item[ "Enabled" ])}
              />
            </>
          )}
        </td>
      ),
      "Status": portfolioColVis[ "Status" ] && <td style={{ padding: "6px" }}>{item[ "Status" ]}</td>,
      "Portfolio Name": portfolioColVis[ "Portfolio Name" ] && (
        <td
          style={{
            padding: "6px",
            maxWidth: "150px",
            minWidth: "110px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            cursor: "default"
          }}
          onMouseEnter={(e) => {
            if (e.target.scrollWidth > e.target.clientWidth) {
              setHoverData({
                content: item[ "Portfolio Name" ],
                x: e.clientX + 10,
                y: e.clientY + 10,
              });
            }
          }}
          onMouseLeave={() => setHoverData(null)}
        >
          {item[ "Portfolio Name" ]}
        </td>
      ),
      "PNL": portfolioColVis[ "PNL" ] && <td style={{ padding: "6px", minWidth: "100px", color: item[ "PNL" ] >= 0 ? "green" : "red" }}>{item[ "PNL" ]}</td>,
      "Symbol": portfolioColVis[ "Symbol" ] && <td style={{ padding: "6px", minWidth: "110px", }}>{item[ "Symbol" ]}</td>,
      "Execute/Sq Off": portfolioColVis[ "Execute/Sq Off" ] && (
        <td style={{ textAlign: "center", minWidth: "100px", }}>
          {isExecuted ? (
            <Tooltip title="Sq Off">
              <IconButton
                color="error"
                style={{ cursor: squareOffPortfolios ? "not-allowed" : "pointer", opacity: squareOffPortfolios ? 0.5 : 1 }}
                onClick={() => !squareOffPortfolios && handleSqOffClick(portfolioDetails[ index ])}
              >
                <Close fontSize="medium" style={{ color: "red" }} />
              </IconButton>
            </Tooltip>
          ) : (
            <Tooltip title="Execute">
              <IconButton color="success" onClick={() => openModal(portfolioDetails[ index ])}>
                <PlayCircleOutline fontSize="medium" />
              </IconButton>
            </Tooltip>
          )}
        </td>
      ),
      "Edit": portfolioColVis[ "Edit" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Edit">
            <IconButton color="primary" onClick={() => handleEdit(portfolioDetails[ index ])}>
              <Edit />
            </IconButton>
          </Tooltip>
        </td>
      ),
      "Delete": portfolioColVis[ "Delete" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Delete">
            <IconButton color="error" onClick={() => !isExecuted1 && handleDelete(item[ "Portfolio Name" ])} disabled={isExecuted}>
              <Delete />
            </IconButton>
          </Tooltip>
        </td>
      ),
      "Make Copy": portfolioColVis[ "Make Copy" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Make Copy">
            <IconButton onClick={() => !isExecuted1 && handleOpenCopy(portfolioDetails[ index ])} disabled={isExecuted}>
              <ContentCopy />
            </IconButton>
          </Tooltip>
        </td>
      ),
      "Mark As Completed": portfolioColVis[ "Mark As Completed" ] && (
        <td style={{ textAlign: "center", minWidth: "100px", }}>
          <Tooltip title="Mark As Completed">
            <IconButton color="primary"><TaskAlt /></IconButton>
          </Tooltip>
        </td>
      ),
      "Reset": portfolioColVis[ "Reset" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Reset">
            <IconButton color="primary"><Replay /></IconButton>
          </Tooltip>
        </td>
      ),
      "Pay Off": portfolioColVis[ "Pay Off" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Pay Off">
            <IconButton><AttachMoney /></IconButton>
          </Tooltip>
        </td>
      ),
      "Chat": portfolioColVis[ "Chat" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Chat">
            <IconButton color="primary"><SignalCellularAlt /></IconButton>
          </Tooltip>
        </td>
      ),
      "Re Execute": portfolioColVis[ "Re Execute" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Re Execute">
            <IconButton color="secondary" onClick={() => !isExecuted1 && handleReexecute(portfolioDetails[ index ])} disabled={isExecuted1}>
              <Autorenew />
            </IconButton>
          </Tooltip>
        </td>
      ),
      "Part Entry/Exit": portfolioColVis[ "Part Entry/Exit" ] && (
        <td style={{ textAlign: "center" }}>
          <Tooltip title="Part Entry">
            <IconButton color="primary"><Cancel /></IconButton>
          </Tooltip>
        </td>
      ),
      "Current Value": portfolioColVis[ "Current Value" ] && <td style={{ padding: "6px" }}>{item[ "Current Value" ]}</td>,
      "Value Per Lot": portfolioColVis[ "Value Per Lot" ] && <td style={{ padding: "6px" }}>{item[ "Value Per Lot" ]}</td>,
      "Underlying LTP": portfolioColVis[ "Underlying LTP" ] && <td style={{ padding: "6px", minWidth: "100px", }}>{item[ "Underlying LTP" ]}</td>,
      "Positional Portfolio": portfolioColVis[ "Positional Portfolio" ] && <td style={{ padding: "6px" }}>{item[ "Positional Portfolio" ]}</td>,
      "Product": portfolioColVis[ "Product" ] && <td style={{ padding: "6px",minWidth: "110px", }}>{item[ "Product" ]}</td>,
      "Strategy": portfolioColVis[ "Strategy" ] && (
        <td
          style={{
            padding: "6px",
            minWidth: "110px",
            maxWidth: "150px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            cursor: "default"
          }}
          onMouseEnter={(e) => {
            if (e.target.scrollWidth > e.target.clientWidth) {
              setHoverData({
                content: item[ "Strategy" ],
                x: e.clientX + 10,
                y: e.clientY + 10,
              });
            }
          }}
          onMouseLeave={() => setHoverData(null)}
        >
          {item[ "Strategy" ]}
        </td>
      ),
      "Entry Price": portfolioColVis[ "Entry Price" ] && <td style={{ padding: "6px" }}>{item[ "Entry Price" ]}</td>,
      "Combined Premuim": portfolioColVis[ "Combined Premuim" ] && <td style={{ padding: "6px", minWidth: "100px", }}>{item[ "Combined Premuim" ]}</td>,
      "Per Lot Premuim": portfolioColVis[ "Per Lot Premuim" ] && <td style={{ padding: "6px", textAlign: "center" }}>{item[ "Per Lot Premuim" ]}</td>,
      "Start Time": portfolioColVis[ "Start Time" ] && <td style={{ padding: "6px", textAlign: "center" }}>{item[ "Start Time" ]}</td>,
      "End Time": portfolioColVis[ "End Time" ] && <td style={{ padding: "6px", textAlign: "center" }}>{item[ "End Time" ]}</td>,
      "SqOff Time": portfolioColVis[ "SqOff Time" ] && <td style={{ padding: "6px", textAlign: "center" }}>{item[ "SqOff Time" ]}</td>,
      "Range End Time": portfolioColVis[ "Range End Time" ] && <td style={{ padding: "6px", textAlign: "center" }}>{item[ "Range End Time" ]}</td>,
      "Delta": portfolioColVis[ "Delta" ] && <td style={{ padding: "6px" }}>{item[ "Delta" ]}</td>,
      "Theta": portfolioColVis[ "Theta" ] && <td style={{ padding: "6px" }}>{item[ "Theta" ]}</td>,
      "Vega": portfolioColVis[ "Vega" ] && <td style={{ padding: "6px" }}>{item[ "Vega" ]}</td>,
      "Remarks": portfolioColVis[ "Remarks" ] && <td style={{ padding: "6px" }}>{item[ "Remarks" ]}</td>,
      "Message": portfolioColVis[ "Message" ] && <td style={{ padding: "6px" }}>{item[ "Message" ]}</td>,
    };
  };

  const hasColumnData = (row, column) => {
    return row[ column ] !== undefined && row[ column ] !== "";
  };

  return (
    <div>
      <style>{styles}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav
            pageCols={portfolioCols}
            colsSelectedAll={portfolioColsSelectedALL}
            setColsSelectedALL={setPortfolioColsSelectedALL}
            selectAll={portfolioColSelectALL}
            colVis={portfolioColVis}
            setColVis={setPortfolioColVis}
            setSeq={setPortfolioSeq}
          />
          <div className="main-table">
            <table className="table">
              <thead style={{ position: "sticky", top: "0px", zIndex: 10, backgroundColor: "#D8E1FF" }}>
                <tr>{portfolioSeq.map((colName, idx) => <React.Fragment key={idx}>{portfolioTH[ colName ]}</React.Fragment>)}</tr>
              </thead>
              <tbody className="tabletbody">
                {filteredRows.filter(row => Object.values(row).some(val => val !== "")).map((item, index) => (
                  <tr
                    key={index}
                    style={{
                      backgroundColor: index % 2 === 0 ? "#FFFFFF" : "#E8E6E6"
                    }}
                  >
                    {portfolioSeq.map((colName, idx) => (
                      <React.Fragment key={idx} >
                        {hasColumnData(item, colName) ? portfolioTD(item, index)[ colName ] : <td style={{ padding: "4px", verticalAlign: "middle" }}></td>}
                      </React.Fragment>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>

            {isTableOpen && (
              <div>
                <table className="table1">
                  <thead style={{ position: "sticky", top: 0, zIndex: "10", backgroundColor: "#D8E1FF" }}>
                    <tr>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "50px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>SNO</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "50px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>ID</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>SqOff</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Idle</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Execute</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "100px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Part Entry/Exit</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "120px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Exchange Symbol</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "90px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Transaction</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Lots</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "90px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Target Type</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "90px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Target Value</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "100px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Profit Locking</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>SL Type</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>SL Value</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "80px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Trailing SL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>SL Wait</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "80px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>On Target</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>On SL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle", minWidth: "70px", fontWeight: "600", wordBreak: "break-word", hyphens: "auto" }}>Remarks</th>
                    </tr>
                  </thead>
                  <tbody>
                    {subTableData.length > 0 ? subTableData.map((item, index) => (
                      item?.legs && item.legs.length > 0 ? item.legs.map((leg, legIndex) => (
                        <tr
                          key={legIndex}
                          style={{
                            backgroundColor: legIndex % 2 === 0 ? "#FFFFFF" : "#E8E6E6"
                          }}
                        >
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{index + 1}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.id || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.sqOff || "00:00:00"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.idle || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.execute || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.partEntryExit || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{`${leg?.expiry_date?.slice(0, 5) || ""}, ${leg?.option_type || ""}, ${leg?.strike || ""}`}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{leg?.transaction_type || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{leg?.lots || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.targetType || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.targetValue || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.profitLocking || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.slType || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.slValue || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.trailingSL || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.slWait || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.onTarget || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.onSL || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.remarks || ""}</td>
                        </tr>
                      )) : (
                        <tr
                          style={{
                            backgroundColor: index % 2 === 0 ? "#FFFFFF" : "#E8E6E6"
                          }}
                        >
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{index + 1}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.id || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.sqOff || "00:00:00"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.idle || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.execute || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.partEntryExit || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>-</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>-</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>-</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.targetType || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.targetValue || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.profitLocking || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.slType || ""}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.slValue || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.trailingSL || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.slWait || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.onTarget || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.onSL || "0"}</td>
                          <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{item?.remarks || ""}</td>
                        </tr>
                      )
                    )) : (
                      <tr><td colSpan="19" style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>No data available.</td></tr>
                    )}
                  </tbody>
                </table>

                <table className="table2">
                  <thead style={{ position: "sticky", top: 0, zIndex: "10", backgroundColor: "#D8E1FF" }}>
                    <tr>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Option Portfolio</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>User ID</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>User Alias</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>SqOff</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Mark As Completed</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Part Entry/Exit</th>
                      {/* <th>Exchange Symbol</th> */}
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Avg Execution Price</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>PNL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>CE PNL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>PE PNL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Max PNL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Max PNL Time</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Min PNL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Min PNL Time</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Target</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>SL</th>
                      <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Message</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.keys(selectedBrokerPnls).length > 0 ? (
                      Object.keys(selectedBrokerPnls).map((brokerKey, idx) => {
                        const brokerData = selectedBrokerPnls[ brokerKey ] || {};
                        if (typeof brokerData !== 'object' || Array.isArray(brokerData)) {
                          return null;
                        }
                        return (
                          <tr
                            key={idx}
                            style={{
                              backgroundColor: idx % 2 === 0 ? "#FFFFFF" : "#E8E6E6"
                            }}
                          >
                            <td style={{ padding: "4px", verticalAlign: "middle" }}>
                              <div style={{ display: "flex", alignItems: "center", paddingRight: "15px" }}>
                                <span
                                  style={{ fontSize: "24px", fontWeight: "bold", cursor: "pointer", paddingLeft: "10px", paddingRight: "10px" }}
                                  onClick={() => handlePlusClick1(idx)}
                                >
                                  {isPlusClicked1[ idx ] ? "-" : "+"}
                                </span>
                                {brokerData?.optionPortfolio || ""}
                              </div>
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{brokerKey || ""}</td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}></td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}></td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{brokerData?.markAsCompleted || ""}</td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{brokerData?.partEntryExit || ""}</td>
                            {/* <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{brokerData?.symbol || ""}</td> */}
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>
                              {/* {brokerData?.transaction_type === "SELL" */}
                              {/* ? `${brokerData?.sell_price || "0"}` */}
                              {/* : `${brokerData?.buy_price || "0"}`} */}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle", color: brokerData?.PNL < 0 ? "red" : "green" }}>
                              {brokerData?.PNL || "0"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>
                              {brokerData?.cePNL || "0"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>
                              {brokerData?.pePNL || "0"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle", color: brokerData?.max_pnl < 0 ? "red" : "green" }}>
                              {brokerData?.max_pnl || "0"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>
                              {brokerData?.max_pnl_time || "00:00:00"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle", color: brokerData?.min_pnl < 0 ? "red" : "green" }}>
                              {brokerData?.min_pnl || "0"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>
                              {brokerData?.min_pnl_time || "00:00:00"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>
                              {brokerData?.target || "0"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>
                              {brokerData?.sl || "0"}
                            </td>
                            <td style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>{brokerData?.message || ""}</td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr><td colSpan="17" style={{ padding: "4px", textAlign: "center", verticalAlign: "middle" }}>No data available</td></tr>
                    )}
                  </tbody>
                </table>

                {isTableOpen && isTableOpen1 && (
                  <table className="table3">
                    <thead style={{ position: "sticky", top: 0, zIndex: "10", backgroundColor: "#D8E1FF" }}>
                      <tr>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>SNO</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>SqOff Leg</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Part Entry/Exit</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Exchange Symbol</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>LTP</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>PNL</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Txn</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Lots</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Leg Qty</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Total Entry Qty</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Avg Entry Price</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Entry Filled Qty</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Avg Exit Price</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Exit Filled Qty</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Status</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>Target</th>
                        <th style={{ fontSize: "15px", padding: "4px 3px", textAlign: "center", height: "auto", verticalAlign: "middle" }}>SL</th>
                      </tr>
                    </thead>
                    <tbody>
                      {subTableData.map((item, index) => (
                        <tr
                          key={index}
                          style={{
                            backgroundColor: index % 2 === 0 ? "#FFFFFF" : "#E8E6E6"
                          }}
                        >
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{index + 1}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.sqOffLeg || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.partEntryExit || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.exchangeSymbol || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.ltp || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.pnl || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.txn || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.lots || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.legQty || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.totalEntryQty || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.avgEntryPrice || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.entryFilledQty || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.avgExitPrice || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.exitFilledQty || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.status || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.target || ""}</td>
                          <td style={{ textAlign: "center", padding: "4px", verticalAlign: "middle" }}>{item?.sl || ""}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            )}

            <Modal
              isOpen={showConfirmDeleteModal}
              onRequestClose={() => setShowConfirmDeleteModal(false)}
              style={{
                overlay: { backgroundColor: "rgba(0, 0, 0, 0.5)", zIndex: 1000 },
                content: {
                  width: "300px",
                  height: "150px",
                  margin: "auto",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  borderRadius: "10px",
                },
              }}
            >
              <p style={{ textAlign: "center", fontSize: "18px", marginBottom: "20px" }}>
                If you proceed, you can't retrieve '{portfolioToDelete}' portfolio details?
              </p>
              <div style={{ display: "flex", gap: "10px" }}>
                <button
                  style={{
                    padding: "8px 16px",
                    borderRadius: "5px",
                    backgroundColor: "#d9534f",
                    color: "white",
                    border: "none",
                    width: "75px", // Matches previous buttons
                    cursor: "pointer", // Added
                  }}
                  onClick={() => handleDeleteAll(portfolioToDelete)}
                >
                  Confirm
                </button>
                <button
                  style={{
                    padding: "8px 16px",
                    borderRadius: "5px",
                    backgroundColor: "#5cb85c",
                    color: "white",
                    border: "none",
                    width: "75px", // Matches previous buttons
                    cursor: "pointer", // Added pointer cursor
                  }}
                  onClick={() => setShowConfirmDeleteModal(false)}
                >
                  Cancel
                </button>
              </div>
            </Modal>

            <Modal
              isOpen={isModalOpen}
              onRequestClose={closeModal}
              style={{
                overlay: { backgroundColor: "rgba(0, 0, 0, 0.5)", zIndex: 1000 },
                content: {
                  width: "300px",
                  height: "150px",
                  margin: "auto",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  borderRadius: "10px",
                },
              }}
            >
              <p style={{ textAlign: "center", fontSize: "18px", marginBottom: "20px" }}>
                Do you need to place Order?
              </p>
              <div style={{ display: "flex", gap: "10px" }}>
                <button
                  style={{
                    padding: "8px 16px",
                    borderRadius: "5px",
                    backgroundColor: "#d9534f",
                    color: "white",
                    border: "none",
                    cursor: "pointer",
                    width: "75px", // Matches the width from Draggable buttons
                  }}
                  onClick={() => placeOrderOptionsQTP(selectedItem)}
                >
                  YES
                </button>
                <button
                  style={{
                    padding: "8px 16px",
                    borderRadius: "5px",
                    backgroundColor: "#5cb85c",
                    color: "white",
                    border: "none",
                    cursor: "pointer",
                    width: "75px", // Matches the width from Draggable buttons
                  }}
                  onClick={closeModal}
                >
                  NO
                </button>
              </div>
            </Modal>

            <Modal
              isOpen={newCopy}
              onRequestClose={handleCloseCopy}
              style={{
                overlay: { backgroundColor: "rgba(0, 0, 0, 0.5)", zIndex: 1000 },
                content: {
                  width: "530px",
                  height: "auto",
                  margin: "auto",
                  padding: "0",
                  borderRadius: "10px",
                  top: "50%",
                  transform: "translateY(-50%)",
                },
              }}
            >
              <div
                style={{
                  backgroundColor: "#D8E1FF",
                  color: "black",
                  padding: "10px",
                  textAlign: "center",
                  borderTopLeftRadius: "10px",
                  borderTopRightRadius: "10px",
                  fontWeight: "bold",
                }}
              >
                New Options Portfolio
              </div>
              <div style={{ padding: "15px 30px" }}>
                <p style={{ fontWeight: "bold" }}>Enter Name for New Options Portfolio</p>
                <p style={{ color: "red" }}>{error}</p>
                <input
                  type="text"
                  placeholder="Portfolio Name"
                  style={{
                    height: "35px",
                    width: "300px",
                    paddingLeft: "10px",
                    marginTop: "10px",
                  }}
                  value={portfolioName}
                  onChange={(e) => setPortfolioName(e.target.value)}
                />
              </div>
              <div style={{ display: "flex", justifyContent: "flex-end", padding: "10px" }}>
                <button
                  style={{
                    marginRight: "20px",
                    borderRadius: "5px",
                    width: "75px", // Changed from "15%" to "75px" to match previous buttons
                    backgroundColor: "red",
                    color: "white",
                    border: "none",
                    cursor: "pointer", // Added pointer cursor
                  }}
                  onClick={handleCloseCopy}
                >
                  Cancel
                </button>
                <button
                  style={{
                    borderRadius: "5px",
                    width: "75px", // Changed from "15%" to "75px" to match previous buttons
                    backgroundColor: "green",
                    color: "white",
                    border: "none",
                    cursor: "pointer", // Added pointer cursor
                  }}
                  onClick={handlenewMakeCopy}
                >
                  Confirm
                </button>
              </div>
            </Modal>

          </div>
          <div className="add_collapse">
            <button className="hiddenbutton button">Add</button>
            <button
              style={{ zIndex: "0" }}
              onClick={() => errorContainerRef.current.toggleCollapse()}
              className="button"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>
          <OptimizedErrorContainer ref={errorContainerRef} msgs={msgs} handleClearLogs={handleClearLogs} />
        </div>
        <OptimizedRightNav />
      </div>
      {hoverData && (
        <div
          className="hover-box"
          style={{
            position: "absolute",
            left: hoverData.x,
            top: hoverData.y,
            backgroundColor: "black",
            color: "white",
            padding: "5px",
            borderRadius: "4px",
            fontSize: "12px",
            zIndex: 1000,
            pointerEvents: "none",
            whiteSpace: "nowrap",
            boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
          }}
        >
          {hoverData.content}
        </div>
      )}
      {filterPopup && filterPopup !== "action" && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "3px 0 3px 3px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            zIndex: 1000,
            minWidth: "130px",
            maxWidth: "170px"
          }}
        >
          <div style={{ paddingRight: "1px" }}>
            <div style={{
              borderBottom: "1px solid #e0e0e0",
              paddingBottom: "1px",
              marginBottom: "1px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            }}>
              <span style={{ fontWeight: "bold", color: "#1976d2" }}>
                Filter: {filterPopup}
              </span>
              <span style={{
                fontSize: "12px",
                color: "#666",
                backgroundColor: "#f5f5f5",
                padding: "2px 6px",
                borderRadius: "4px"
              }}>
                {getDynamicUniqueValues(filterPopup).length} items
              </span>
            </div>

            {getDynamicUniqueValues(filterPopup).length > 10 && (
              <div style={{ marginBottom: "1px", paddingRight: "1px" }}>
                <input
                  type="text"
                  placeholder="Search..."
                  style={{
                    width: "100%",
                    padding: "6px 10px",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                    fontSize: "14px"
                  }}
                />
              </div>
            )}

            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "1px 1px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "1px",
                backgroundColor: "#f8f9fa",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[ filterPopup ] &&
                  getDynamicUniqueValues(filterPopup).every((opt) =>
                    tempFilters[ filterPopup ].includes(opt)
                  )
                }
                onChange={() => handleSelectAll(filterPopup)}
                style={{ marginRight: "8px" }}
              />
              <span>Select All</span>
            </label>

            <div
              style={{
                maxHeight: "150px",
                overflowY: "auto",
                margin: "0",
                scrollbarWidth: "thin",
                scrollbarColor: "#888 #f1f1f1",
                border: "1px solid #eee",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              {getDynamicUniqueValues(filterPopup).length > 0 ? (
                getDynamicUniqueValues(filterPopup).map((item) => {
                  const isSelected = tempFilters[ filterPopup ]?.includes(item) || false;
                  return (
                    <div
                      key={item}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "1px 2px",
                        cursor: "pointer",
                        margin: "0",
                        borderBottom: "1px solid #f0f0f0",
                        backgroundColor: isSelected ? "#f0f7ff" : "transparent",
                        transition: "background-color 0.2s"
                      }}
                      onClick={() => handleFilterChange(filterPopup, item)}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => { }}
                        style={{ marginRight: "8px" }}
                      />
                      <span style={{
                        color: isSelected ? "#1976d2" : "#444",
                        fontWeight: isSelected ? "500" : "normal"
                      }}>
                        {item || "(Empty)"}
                      </span>
                    </div>
                  );
                })
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "10px",
                    color: "#666",
                    fontStyle: "italic",
                    minHeight: "40px",
                    justifyContent: "center"
                  }}
                >
                  <span>No options available</span>
                </div>
              )}
            </div>

            <div style={{
              fontSize: "12px",
              color: "#666",
              margin: "1px 0",
              display: "flex",
              justifyContent: "space-between",
              marginRight: "1px"
            }}>
              <span>
                {tempFilters[ filterPopup ]?.length || 0} of {getDynamicUniqueValues(filterPopup).length} selected
              </span>
              {tempFilters[ filterPopup ]?.length > 0 && (
                <span
                  style={{ color: "#1976d2", cursor: "pointer" }}
                  onClick={() => setTempFilters({ ...tempFilters, [ filterPopup ]: [] })}
                >
                  Clear selection
                </span>
              )}
            </div>
          </div>

          <div
            style={{
              marginTop: "3px",
              display: "flex",
              gap: "10px",
              justifyContent: "center",
              paddingRight: "3px"
            }}
          >
            <button
              onClick={handleCancelFilter}
              style={{
                padding: "3px 3px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                background: "#f8f9fa",
                cursor: "pointer",
                color: "#333",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilter}
              style={{
                padding: "3px 3px",
                border: "none",
                borderRadius: "4px",
                background: "#1976d2",
                color: "white",
                cursor: "pointer",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Apply Filter
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Portfolio;