import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    marketData: {
        sensex: { c: "0", ch: "0", chp: "0" },
        nifty50: { c: "0", ch: "0", chp: "0" },
        niftybank: { c: "0", ch: "0", chp: "0" },
        finnifty: { c: "0", ch: "0", chp: "0" },
    },
};

const marketSlice = createSlice({
    name: 'market',
    initialState,
    reducers: {
        updateMarketData: (state, action) => {
            const { indexName, indexData } = action.payload;
            state.marketData[ indexName ] = indexData;
        },
    },
});

export const { updateMarketData } = marketSlice.actions;
export default marketSlice.reducer;
