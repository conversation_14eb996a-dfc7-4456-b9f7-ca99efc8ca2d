import { useDispatch, useSelector } from "react-redux";
import { setPortfolios } from "../store/slices/portfolio";
import Cookies from "universal-cookie";
import { fetchWithAuth } from "../utils/api";

export const useFetchPortfolios = () => {
    const dispatch = useDispatch();
    const cookies = new Cookies();
    const existingPortfolios = useSelector((state) => state.portfolioReducer.portfolios);

    const fetchPortfolios = async () => {
        const mainUser = cookies.get("USERNAME");

        try {
            const response = await fetchWithAuth(`/api/get_portfolio/${mainUser}`);

            const data = await response.json();
            const newPortfolios = data[ "Portfolio details" ];

            const mergedPortfolios = newPortfolios.map((newPortfolio) => {
                const existingPortfolio = existingPortfolios?.find(
                    ep => ep.portfolio_name === newPortfolio.portfolio_name
                );

                const basePortfolio = {
                    ...newPortfolio,
                    totalPnl: "0.00",
                    brokerUserPnls: [],
                };

                if (existingPortfolio) {
                    return {
                        ...basePortfolio,
                        totalPnl: existingPortfolio.totalPnl || "0.00",
                        brokerUserPnls: existingPortfolio.brokerUserPnls || [],
                    };
                }

                return basePortfolio;
            });

            dispatch(
                setPortfolios({
                    portfolios: mergedPortfolios
                })
            );

        } catch (error) {
            console.error("Error fetching portfolios:", error);
        }
    };

    return fetchPortfolios;
};
