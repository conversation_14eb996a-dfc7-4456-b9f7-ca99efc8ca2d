.strategy-selector-container {
  display: flex;
  align-items: center;
  gap: 0;
  width: 180px;
  font-family: "Roboto", sans-serif;
}

.strategy-label {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.strategy-selector {
  position: relative;
  width: 150px;
  margin-right: 20px;
}

.dropdown-button {
  width: 110%;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 0;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: border-color 0.2s ease;
  margin-right: 10px;
}

.dropdown-button:hover {
  border-color: #666;
}

.dropdown-arrow {
  font-size: 15px;
}

.dropdown-menu::-webkit-scrollbar {
  width: 8px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #aaa;
  border-radius: 4px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #888;
}

.category {
  border-bottom: 1px solid #eee;
}

.category:last-child {
  border-bottom: none;
}

.category-header {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0;
  background-color: #f9f9f9;
}

.category-header:hover {
  background-color: #f0f0f0;
}

.category-icon {
  font-size: 12px;
  color: #666;
}

.sub-options {
  background-color: #fff;
}

.option {
  font-size: 12px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.option:hover {
  background-color: #e6f0fa;
}

.option::before {
  content: "├─ ";
  color: #999;
}
.option.selected {
  background-color: #e6f7ff;
  font-weight: bold;
  border-left: 3px solid #d8e1ff;
}

.option:hover:not(.selected) {
  background-color: #f0f0f0;
}
