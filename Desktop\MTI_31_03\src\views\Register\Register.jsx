import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import google from "../../assets/google.png";
import apple from "../../assets/apple.png";
import { useNavigate } from "react-router-dom";
import { fetchWithAuth } from "../../utils/api";
import "./Register.css";

const Register = () => {
  const navigate = useNavigate();

  const [ name, setName ] = useState("");
  const [ email, setEmail ] = useState("");
  const [ mobile, setMobile ] = useState("");
  const [ username, setUsername ] = useState("");
  const [ password, setPassword ] = useState("");
  const [ showPassword, setShowPassword ] = useState(false);
  const [ nameError, setNameError ] = useState("");
  const [ emailError, setEmailError ] = useState("");
  const [ mobileError, setMobileError ] = useState("");
  const [ usernameFormatError, setUsernameFormatError ] = useState("");
  const [ passwordError, setPasswordError ] = useState("");
  const [ globalError, setGlobalError ] = useState("");

  const isValidName = (name) => {
    const usernamePattern = /^[A-Za-z\s]+$/;
    return usernamePattern.test(name);
  };

  const isValidMobile = (number) => {
    const mobilePattern = /^[0-9]{10}$/;
    return mobilePattern.test(number);
  };

  const isValidEmail = (email) => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  };

  const isValidUsername = (username) => {
    const usernamePattern = /^(?![0-9])(?=.*[a-z])(?=.*\d)[a-z\d]{8,}$/i;
    return usernamePattern.test(username);
  };

  const isValidPassword = (password) => {
    const passwordPattern = /^.{8,}$/;
    return passwordPattern.test(password);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const notifyError = (errMsg, field) => {
    if (field === "name") {
      setNameError(errMsg);
    } else if (field === "email") {
      setEmailError(errMsg);
    } else if (field === "mobile") {
      setMobileError(errMsg);
    } else if (field === "username") {
      setUsernameFormatError(errMsg);
    } else if (field === "password") {
      setPasswordError(errMsg);
    } else if (field.length === 0) {
      setGlobalError(errMsg);
    }
  };

  const notifySuccess = (sucMsg) => {
    toast.success(sucMsg, {
      position: "top-center",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "colored",
    });
  };

  const handleSubmit = async () => {
    setGlobalError("");
    try {
      if (!name || !email || !mobile || !username || !password) {
        if (!name) notifyError("*Field is required", "name");
        if (!email) notifyError("*Field is required", "email");
        if (!username) notifyError("*Field is required", "username");
        if (!password) notifyError("*Field is required", "password");
        if (mobile.length !== 10) {
          notifyError("Mobile number must be exactly 10 digits.", "mobile");
        }
        return;
      }

      if (!isValidName(name)) {
        notifyError("Enter a valid Name", "name");
        return;
      }

      if (!isValidEmail(email)) {
        notifyError("Enter a valid email address.", "email");
        return;
      }

      if (!isValidMobile(mobile)) {
        notifyError("Enter a valid 10-digit mobile number.", "mobile");
        return;
      }

      if (!isValidPassword(password)) {
        notifyError("Password must be 8 characters.", "password");
        return;
      }

      if (!isValidUsername(username)) {
        let usernameError = "Username must ";
        usernameError += !/^\D/.test(username) ? "start with an alphabet" : "";
        usernameError += username.length < 8 ? ", be at least 8 characters long" : "";
        usernameError += !/\d/.test(username) ? ", contain at least one digit" : "";
        usernameError += !/[a-z]/.test(username) ? ", contain at least one letter" : "";
        usernameError += !/^[a-z\d]+$/.test(username) ? "consist of lowercase letters and digits only" : "";
        notifyError(usernameError.trim(), "username");
        return;
      }

      const data = { name, email, mobile, username, password };

      const response = await fetchWithAuth("/api/data", {
        method: "POST",
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw {
          message: errorData.message || "Something bad happened. Please try again",
          field: errorData.field || "",
        };
      }

      notifySuccess("Your account created successfully.");
      setName("");
      setEmail("");
      setMobile("");
      setUsername("");
      setPassword("");

      setTimeout(() => {
        navigate("/");
      }, 2000);
    } catch (error) {
      console.error("Error:", error);
      notifyError(error.message || "An error occurred", error.field || "");
    }
  };

  return (
    <div className="register-container">
      <div className="right-container-register">
        <div className="right-container__box-register">
          <div className="right-container-box-register">
            <h2 className="right-container__h2-register">Get Started Now</h2>
          </div>

          {globalError && <span className="error-message">{globalError}</span>}

          <div className="input-container-register">
            <label htmlFor="name" className="right-container__label-register">Name</label>
            <input
              type="text"
              className="right-container__input-register"
              name="name"
              id="name"
              placeholder="Your name"
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                setNameError("");
              }}
            />
            {nameError && <span className="error-message">{nameError}</span>}

            {/* Email */}
            <label htmlFor="email" className="right-container__label">Email address</label>
            <input
              type="email"
              className="right-container__input-register"
              name="email"
              id="email"
              placeholder="Your email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                setEmailError("");
              }}
            />
            {emailError && <span className="error-message">{emailError}</span>}

            {/* Username */}
            <label htmlFor="username" className="right-container__label">User ID</label>
            <input
              type="text"
              className="right-container__input-register"
              name="username"
              id="username"
              placeholder="Your username"
              value={username}
              onChange={(e) => {
                const value = e.target.value.toLocaleLowerCase();
                setUsername(value);
                setUsernameFormatError("");
                if (!isValidUsername(value)) {
                  let usernameError = "Username must ";
                  if (/^\d/.test(value)) usernameError = "Username must start with an alphabet";
                  else if (!/\d/.test(value)) usernameError = "Username must contain at least one digit";
                  else if (!/[a-z]/.test(value)) usernameError = "Username must contain at least one letter";
                  else if (!/^[a-z\d]+$/.test(value)) usernameError = "Username must consist of lowercase letters and digits only";
                  else if (value.length < 8) usernameError = "Username must be at least 8 characters long";
                  setUsernameFormatError(usernameError);
                }
              }}
            />
            {usernameFormatError && <span className="error-message">{usernameFormatError}</span>}

            {/* Mobile */}
            <label htmlFor="mobile" className="right-container__label">Mobile Number</label>
            <input
              type="text"
              className="right-container__input-register"
              name="mobile"
              id="mobile"
              placeholder="Your mobile"
              value={mobile}
              onChange={(e) => {
                setMobile(e.target.value);
                setMobileError("");
                if (!isValidMobile(e.target.value)) {
                  setMobileError("Enter a valid 10-digit mobile number.", "mobile");
                }
              }}
            />
            {mobileError && <span className="error-message">{mobileError}</span>}

            {/* Password */}
            <label htmlFor="password" className="right-container__label">Password</label>
            <div className="password-input-container">
              <input
                type={showPassword ? "text" : "password"}
                className="right-container__input-register-password"
                name="password"
                id="password"
                placeholder="Your password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setPasswordError("");
                  if (!isValidPassword(e.target.value)) {
                    setPasswordError("Password must be 8 characters.", "password");
                  }
                }}
              />
              <div className="password-toggle" onClick={togglePasswordVisibility}>
                {showPassword ? "👁️" : "🔒"}
              </div>
            </div>
            {passwordError && <span className="error-message">{passwordError}</span>}
          </div>

          <div className="checkbox-container">
            <input type="checkbox" className="checkbox" id="termsCheckbox" />
            <div className="IAgreeToTheTermsPolicy">
              <span className="label" style={{ wordWrap: "break-word" }}>I agree to the</span>
              <span
                style={{
                  color: "black",
                  fontSize: 18,
                  fontFamily: "Roboto",
                  fontWeight: "500",
                  textDecoration: "underline",
                  wordWrap: "break-word",
                }}
              >
                terms & policy
              </span>
            </div>
          </div>

          <div style={{ marginBottom: "10px" }} />
          <button className="btn-register" type="submit" onClick={handleSubmit}>
            Signup
          </button>

          <div className="social-sign-in">
            <div
              className="SignInWithGoogle"
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "black",
                fontSize: 14,
                fontFamily: "Roboto",
                fontWeight: "500",
                wordWrap: "break-word",
                border: "1px solid #000",
                borderRadius: "5px",
                width: "190px",
                height: "25px",
              }}
            >
              <img src={google} style={{ marginRight: "4px" }} alt="Google" /> Sign in with Google
            </div>
            <div
              className="SignInWithApple"
              style={{
                color: "black",
                fontSize: 13,
                fontFamily: "Roboto",
                fontWeight: "500",
                wordWrap: "break-word",
                padding: 5,
                display: "flex",
                border: "1px solid #000",
                borderRadius: "5px",
                width: "190px",
                height: "25px",
              }}
            >
              <img src={apple} style={{ marginRight: "4px" }} alt="Apple" /> Sign in with Apple
            </div>
          </div>

          <p className="right-container__bottom-text">
            <span className="have-account">Have an account? </span>
            <span className="sign-in" style={{ marginLeft: 8 }}>
              <Link to="/">Sign in</Link>
            </span>
          </p>
        </div>
      </div>
      <div className="decoration-container">
        <svg width="869" className="illustration-svg" height="657" viewBox="0 0 869 657" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M781.221 596.536H723.71V596.97H781.221V596.536Z" fill="#EBEBEB" />
          <path d="M575.14 601.259H560.051V601.693H575.14V601.259Z" fill="#EBEBEB" />
          <path d="M721.975 580.422H688.652V580.856H721.975V580.422Z" fill="#EBEBEB" />
          <path d="M166.091 583.339H91.0947V583.773H166.091V583.339Z" fill="#EBEBEB" />
          <path d="M192.554 583.339H181.562V583.773H192.554V583.339Z" fill="#EBEBEB" />
          <path d="M390.959 590.667H228.29V591.101H390.959V590.667Z" fill="#EBEBEB" />
          <path d="M413.967 491.151H78.6611C73.1914 491.151 68.7461 486.706 68.7461 481.236V9.91505C68.7461 4.44528 73.1914 0 78.6611 0H413.967C419.437 0 423.882 4.44528 423.882 9.91505V481.236C423.882 486.706 419.437 491.151 413.967 491.151ZM78.6611 0.434109C73.4345 0.434109 69.1802 4.68838 69.1802 9.91505V481.236C69.1802 486.463 73.4345 490.717 78.6611 490.717H413.967C419.194 490.717 423.448 486.463 423.448 481.236V9.91505C423.448 4.68838 419.194 0.434109 413.967 0.434109H78.6611Z" fill="#EBEBEB" />
          <path d="M789.56 491.151H454.254C448.785 491.151 444.339 486.706 444.339 481.236V9.91505C444.339 4.44528 448.785 0 454.254 0H789.56C795.03 0 799.475 4.44528 799.475 9.91505V481.236C799.475 486.706 795.03 491.151 789.56 491.151ZM454.254 0.434109C449.028 0.434109 444.773 4.68838 444.773 9.91505V481.236C444.773 486.463 449.028 490.717 454.254 490.717H789.56C794.787 490.717 799.041 486.463 799.041 481.236V9.91505C799.041 4.68838 794.787 0.434109 789.56 0.434109H454.254Z" fill="#EBEBEB" />
          <path d="M868.218 568.596H0V569.031H868.218V568.596Z" fill="#EBEBEB" />
          <path d="M207.209 451.126C205.75 434.352 188.629 428.083 168.33 429.854C148.048 431.626 132.281 440.794 133.74 457.551L207.209 451.126Z" fill="#F0F0F0" />
          <path d="M167.671 422.18C177.083 421.364 180.087 413.411 179.271 403.982C178.455 394.571 174.114 387.26 164.685 388.077C155.273 388.893 152.269 396.846 153.085 406.274C153.901 415.686 158.242 422.996 167.671 422.18Z" fill="#F0F0F0" />
          <path d="M590.563 93.8022C591.015 98.9941 587.177 103.578 581.985 104.03C576.793 104.481 572.209 100.644 571.758 95.4518C571.306 90.2599 575.144 85.6757 580.336 85.2242C585.528 84.7727 590.112 88.6102 590.563 93.8022Z" fill="#F5F5F5" />
          <path d="M642.779 89.2352C643.231 94.4271 639.393 99.0113 634.201 99.4628C629.009 99.9142 624.425 96.0767 623.974 90.8848C623.522 85.6928 627.36 81.1086 632.552 80.6572C637.744 80.2057 642.328 84.0432 642.779 89.2352Z" fill="#F5F5F5" />
          <path d="M695.011 84.6683C695.462 89.8602 691.625 94.4444 686.433 94.8959C681.241 95.3474 676.657 91.5098 676.205 86.3179C675.754 81.1259 679.591 76.5418 684.783 76.0903C689.975 75.6388 694.559 79.4763 695.011 84.6683Z" fill="#F5F5F5" />
          <path d="M595.216 147.024C595.668 152.216 591.83 156.8 586.638 157.251C581.446 157.703 576.862 153.865 576.411 148.673C575.959 143.482 579.797 138.897 584.989 138.446C590.181 137.994 594.765 141.832 595.216 147.024Z" fill="#F5F5F5" />
          <path d="M647.432 142.457C647.884 147.649 644.046 152.233 638.854 152.685C633.662 153.136 629.078 149.299 628.626 144.107C628.175 138.915 632.012 134.33 637.204 133.879C642.396 133.428 646.981 137.265 647.432 142.457Z" fill="#F5F5F5" />
          <path d="M699.663 137.891C700.115 143.083 696.277 147.667 691.085 148.119C685.893 148.57 681.309 144.733 680.858 139.541C680.406 134.349 684.244 129.765 689.436 129.313C694.628 128.862 699.212 132.699 699.663 137.891Z" fill="#F5F5F5" />
          <path d="M599.869 200.244C600.321 205.436 596.483 210.021 591.291 210.472C586.1 210.924 581.515 207.086 581.064 201.894C580.612 196.702 584.45 192.118 589.642 191.666C594.834 191.215 599.418 195.053 599.869 200.244Z" fill="#F5F5F5" />
          <path d="M652.101 195.677C652.552 200.869 648.714 205.454 643.523 205.905C638.331 206.357 633.746 202.519 633.295 197.327C632.843 192.135 636.681 187.551 641.873 187.099C647.065 186.648 651.649 190.485 652.101 195.677Z" fill="#F5F5F5" />
          <path d="M704.317 191.112C704.768 196.304 700.931 200.888 695.739 201.339C690.547 201.791 685.963 197.953 685.511 192.761C685.06 187.57 688.897 182.985 694.089 182.534C699.281 182.082 703.865 185.92 704.317 191.112Z" fill="#F5F5F5" />
          <path d="M693.031 194.185L585.737 203.58L680.859 87.985L583.393 96.5109C582.247 96.6151 581.257 95.7643 581.153 94.6356C581.048 93.4895 581.899 92.4998 583.028 92.3956L690.322 83.0015L595.2 198.596L692.667 190.07C693.813 189.966 694.802 190.817 694.907 191.945C695.011 193.092 694.16 194.081 693.031 194.185Z" fill="#E6E6E6" />
          <path d="M694.676 400.508C694.329 399.484 693.409 398.72 692.332 398.65C687.418 398.32 683.251 396.48 679.969 393.18C675.68 388.874 673.162 382.31 672.745 374.531C672.676 373.072 671.46 371.944 670.002 371.944C668.387 371.944 667.119 373.298 667.206 374.896C667.709 384.029 670.783 391.826 676.044 397.105C680.142 401.22 685.438 403.634 691.464 404.155C691.568 404.155 691.707 404.172 691.863 404.189C693.86 404.346 695.371 402.401 694.711 400.508H694.676Z" fill="#F0F0F0" />
          <path d="M689.119 433.848C666.112 431.834 649.511 416.154 644.25 391.67C643.92 390.107 644.91 388.579 646.49 388.301C647.949 388.041 649.355 388.978 649.668 390.42C654.373 412.438 669.15 426.537 689.606 428.326C712.04 430.288 736.125 415.528 740.431 378.334C740.605 376.823 741.924 375.712 743.435 375.851C744.981 375.99 746.144 377.362 745.953 378.907C741.82 413.983 718.76 436.47 689.102 433.865L689.119 433.848Z" fill="#F0F0F0" />
          <path d="M645.71 367.273C644.165 367.134 643.001 365.762 643.192 364.217C647.325 329.141 670.385 306.654 700.043 309.259C726.02 311.533 743.524 330.981 746.267 360.275C746.406 361.82 745.208 363.175 743.663 363.262C742.152 363.348 740.867 362.202 740.728 360.709C738.228 334.315 722.634 316.829 699.575 314.798C677.14 312.836 653.055 327.595 648.749 364.79C648.575 366.3 647.256 367.412 645.745 367.273H645.71Z" fill="#F0F0F0" />
          <path d="M690.25 418.968C685.978 418.586 681.967 417.596 678.268 415.981C676.775 415.321 676.15 413.515 676.931 412.091C677.609 410.841 679.137 410.303 680.439 410.876C683.895 412.404 687.732 413.289 691.847 413.515C708.864 414.488 726.819 402.437 728.573 373.334C729.65 355.397 723.52 340.915 712.042 333.986C710.809 333.24 710.358 331.694 710.983 330.392C711.695 328.933 713.535 328.395 714.925 329.229C728.191 337.234 735.328 353.591 734.112 373.647C732.411 401.725 714.907 420.392 691.517 419.054C691.083 419.037 690.649 419.002 690.215 418.968H690.25Z" fill="#F0F0F0" />
          <path d="M665.523 401.654C664.186 402.575 662.345 402.141 661.529 400.734C656.597 392.225 654.305 381.234 655.017 369.443C656.632 342.581 672.746 324.331 694.608 323.984C696.223 323.949 697.543 325.321 697.456 326.935C697.369 328.394 696.206 329.523 694.747 329.557C678.529 329.87 662.241 342.129 660.574 369.791C659.931 380.522 661.98 390.437 666.374 398.025C667.103 399.275 666.721 400.856 665.523 401.672V401.654Z" fill="#F0F0F0" />
          <path d="M703.479 399.38C702.906 398.095 703.41 396.602 704.608 395.873C711.901 391.48 716.398 382.242 716.468 371.129C716.52 362.221 713.95 354.685 709.192 349.909C705.528 346.228 700.788 344.37 695.092 344.388C686.532 344.405 679.603 349.093 675.766 357.359C675.158 358.661 673.665 359.269 672.31 358.783C670.782 358.227 670.053 356.456 670.747 354.98C675.505 344.839 684.413 338.866 695.092 338.831C702.212 338.831 708.445 341.297 713.134 346.002C718.951 351.837 722.111 360.779 722.042 371.181C721.955 384.308 716.433 395.317 707.421 400.7C705.997 401.551 704.156 400.908 703.479 399.398V399.38Z" fill="#F0F0F0" />
          <path d="M683.479 378.96C681.899 379.602 680.128 378.682 679.729 377.032C679.538 376.286 679.399 375.504 679.277 374.705C677.697 364.322 683.288 355.275 692.266 353.66C696.902 352.826 701.035 353.92 704.195 356.733C705.48 357.879 705.376 359.928 703.987 360.918L703.813 361.04C702.754 361.786 701.382 361.63 700.392 360.779C698.5 359.164 696.121 358.609 693.255 359.112C686.414 360.328 683.809 367.447 684.782 373.855C684.868 374.48 684.99 375.087 685.129 375.66C685.459 377.015 684.782 378.421 683.479 378.942V378.96Z" fill="#F0F0F0" />
          <path d="M692.663 389.655C692.663 389.655 692.663 389.655 692.646 389.655C690.736 389.482 689.677 387.346 690.701 385.713L690.944 385.331C691.465 384.515 692.368 384.064 693.341 384.133C694.157 384.185 695.042 384.133 695.945 383.977C701.919 382.9 704.662 377.344 704.61 371.683C704.61 370.277 705.582 369.061 706.972 368.835C708.604 368.592 710.149 369.808 710.167 371.457C710.34 380.522 704.992 388.006 696.918 389.447C695.459 389.707 694.035 389.777 692.681 389.655H692.663Z" fill="#F0F0F0" />
          <path d="M693.896 379.394C690.945 379.134 688.739 377.137 687.923 374.289C687.454 372.639 688.843 371.042 690.545 371.198L691.188 371.25C692.177 371.337 692.994 372.014 693.323 372.952C693.48 373.403 693.792 373.803 694.365 373.855C695.702 373.976 695.928 372.049 695.963 371.667C695.98 371.389 696.05 370.33 695.581 369.705C695.129 369.097 694.834 368.385 694.886 367.621C695.095 365.155 698.22 364.148 699.8 366.076C701.051 367.604 701.693 369.739 701.485 372.153C701.085 376.703 697.873 379.741 693.862 379.394H693.896Z" fill="#F0F0F0" />
          <path d="M641.662 317.246C640.134 317.108 639.005 315.771 639.144 314.242L641.246 290.158L665.33 292.259C666.858 292.398 667.987 293.735 667.848 295.263C667.709 296.791 666.372 297.92 664.844 297.781L646.299 296.166L644.684 314.711C644.545 316.239 643.208 317.368 641.68 317.229L641.662 317.246Z" fill="#F0F0F0" />
          <path d="M756.146 327.266C754.618 327.127 753.489 325.79 753.628 324.262L755.243 305.717L736.698 304.102C735.17 303.963 734.041 302.626 734.18 301.098C734.319 299.57 735.656 298.441 737.184 298.58L761.268 300.681L759.167 324.766C759.028 326.294 757.691 327.422 756.163 327.284L756.146 327.266Z" fill="#F0F0F0" />
          <path d="M747.62 456.716L723.535 454.615C722.007 454.476 720.879 453.139 721.017 451.611C721.156 450.083 722.493 448.955 724.021 449.093L742.567 450.708L744.181 432.163C744.32 430.635 745.657 429.506 747.185 429.645C748.714 429.784 749.842 431.121 749.703 432.649L747.602 456.734L747.62 456.716Z" fill="#F0F0F0" />
          <path d="M651.683 448.312L627.599 446.211L629.7 422.126C629.839 420.598 631.176 419.47 632.704 419.608C634.232 419.747 635.361 421.084 635.222 422.613L633.607 441.158L652.152 442.773C653.68 442.911 654.809 444.249 654.67 445.777C654.531 447.305 653.194 448.433 651.666 448.294L651.683 448.312Z" fill="#F0F0F0" />
          <path d="M129.765 396.203C128.828 396.29 127.994 395.596 127.907 394.658L126.605 379.846L141.417 378.544C142.354 378.457 143.188 379.151 143.275 380.089C143.362 381.027 142.667 381.86 141.729 381.947L130.338 382.937L131.328 394.328C131.415 395.266 130.72 396.099 129.783 396.186L129.765 396.203Z" fill="#E0E0E0" />
          <path d="M200.158 390.056C199.221 390.143 198.387 389.449 198.3 388.511L197.311 377.12L185.92 378.11C184.982 378.197 184.148 377.502 184.062 376.564C183.975 375.627 184.669 374.793 185.607 374.706L200.419 373.404L201.721 388.216C201.808 389.154 201.113 389.987 200.176 390.074L200.158 390.056Z" fill="#E0E0E0" />
          <path d="M207.45 453.8L192.638 455.102C191.701 455.189 190.867 454.495 190.78 453.557C190.693 452.619 191.388 451.786 192.326 451.699L203.717 450.709L202.727 439.318C202.64 438.381 203.335 437.547 204.272 437.46C205.21 437.373 206.044 438.068 206.13 439.006L207.433 453.817L207.45 453.8Z" fill="#E0E0E0" />
          <path d="M148.446 458.958L133.634 460.26L132.332 445.449C132.245 444.511 132.939 443.677 133.877 443.591C134.815 443.504 135.648 444.198 135.735 445.136L136.725 456.527L148.116 455.537C149.053 455.45 149.887 456.145 149.974 457.083C150.061 458.02 149.366 458.854 148.428 458.941L148.446 458.958Z" fill="#E0E0E0" />
          <path d="M198.96 149.334L142.3 154.283L141.137 141.016C139.939 127.264 150.149 115.091 163.901 113.893C177.654 112.695 189.826 122.905 191.025 136.658L191.702 144.368L199.88 143.656L199.203 135.946C197.606 117.679 181.439 104.117 163.19 105.715C144.922 107.312 131.361 123.478 132.958 141.728L134.122 155.064C127.523 156.349 122.835 162.427 123.425 169.268L127.471 215.527C128.114 222.837 134.556 228.255 141.883 227.612L205.315 222.056C212.626 221.413 218.044 214.971 217.401 207.643L213.355 161.385C212.713 154.074 206.27 148.657 198.943 149.299L198.96 149.334ZM174.893 184.983L176.282 200.906C176.508 203.528 174.581 205.82 171.959 206.063C169.336 206.289 167.044 204.361 166.801 201.739L165.412 185.816C163.328 184.531 161.852 182.343 161.609 179.721C161.227 175.328 164.474 171.439 168.885 171.057C173.278 170.675 177.168 173.922 177.55 178.332C177.776 180.954 176.699 183.368 174.876 185L174.893 184.983Z" fill="#EBEBEB" />
          <path d="M295.957 176.144L290.227 166.802C287.119 167.236 284.584 167.236 281.476 166.802L275.745 176.144L274.651 175.849C270.953 174.859 267.393 173.383 264.059 171.456L263.069 170.883L265.5 160.742C265.691 159.926 266.525 159.422 267.341 159.613C268.157 159.804 268.661 160.638 268.47 161.454L266.577 169.355C269.06 170.709 271.647 171.786 274.339 172.584L279.93 163.468L280.937 163.642C284.184 164.18 287.484 164.18 290.731 163.642L291.738 163.468L297.329 172.584C300.003 171.786 302.608 170.709 305.091 169.355L302.608 158.971L303.441 158.38C305.716 156.748 307.73 154.821 309.467 152.633C309.988 151.99 310.908 151.834 311.568 152.32C312.263 152.824 312.436 153.813 311.898 154.491C310.196 156.644 308.234 158.571 306.046 160.256L308.599 170.9L307.609 171.473C304.275 173.4 300.715 174.876 297.034 175.866L295.94 176.161L295.957 176.144ZM260.847 155.758C260.36 155.203 259.892 154.647 259.458 154.057L248.813 156.609L248.24 155.619C246.313 152.285 244.837 148.726 243.847 145.027L243.552 143.933L252.894 138.203C252.685 136.64 252.581 135.216 252.581 133.827C252.581 132.438 252.685 131.014 252.894 129.451L243.552 123.721L243.847 122.627C244.837 118.928 246.313 115.369 248.24 112.035L248.813 111.045L259.458 113.598C261.246 111.271 263.312 109.204 265.639 107.416L263.712 99.3588C263.521 98.5427 264.024 97.7092 264.84 97.5182C265.657 97.3272 266.49 97.8307 266.681 98.6469L269.077 108.683L268.244 109.291C265.57 111.219 263.243 113.545 261.316 116.22L260.725 117.053L250.324 114.57C248.987 117.053 247.893 119.64 247.111 122.332L256.228 127.923L256.071 128.93C255.776 130.719 255.637 132.316 255.637 133.827C255.637 135.338 255.776 136.935 256.071 138.724L256.245 139.731L247.129 145.322C247.928 147.996 249.004 150.601 250.341 153.084L260.742 150.601L261.333 151.435C261.906 152.216 262.496 152.98 263.139 153.709C263.677 154.317 263.66 155.22 263.069 155.793L263.017 155.845C262.409 156.435 261.42 156.383 260.864 155.741L260.847 155.758ZM322.907 156.609L318.983 155.671C318.166 155.48 317.663 154.647 317.854 153.831C318.045 153.015 318.878 152.511 319.694 152.702L321.396 153.101C322.733 150.618 323.827 148.031 324.609 145.34L315.492 139.748L315.666 138.741C315.961 136.953 316.1 135.355 316.1 133.844C316.1 132.334 315.961 130.736 315.666 128.948L315.51 127.941L324.626 122.349C323.827 119.675 322.751 117.07 321.414 114.587L311.012 117.07L310.405 116.237C308.477 113.563 306.15 111.236 303.476 109.309L302.643 108.701L305.126 98.2996C302.643 96.9451 300.055 95.8686 297.364 95.0698L291.773 104.186L290.765 104.03C287.518 103.509 284.219 103.509 280.972 104.03L279.965 104.186L274.373 95.0698C274.026 95.174 273.679 95.2782 273.332 95.3997C272.55 95.6602 271.717 95.2434 271.439 94.4794C271.144 93.6806 271.56 92.7777 272.377 92.4999C273.141 92.2568 273.905 92.031 274.669 91.8227L275.763 91.5275L281.493 100.869C284.601 100.435 287.119 100.435 290.245 100.869L295.975 91.5275L297.069 91.8227C300.767 92.8124 304.327 94.2884 307.644 96.2158L308.633 96.7889L306.081 107.433C308.408 109.222 310.474 111.288 312.263 113.615L322.907 111.062L323.48 112.052C325.407 115.386 326.883 118.946 327.873 122.644L328.168 123.738L318.826 129.469C319.035 131.031 319.139 132.473 319.139 133.844C319.139 135.216 319.035 136.657 318.826 138.22L328.168 143.95L327.873 145.044C326.883 148.743 325.407 152.303 323.48 155.637L322.907 156.626V156.609ZM285.435 148.726C277.673 148.5 271.213 142.04 270.987 134.279C270.762 126.465 276.614 119.936 284.15 119.05C285.053 118.946 285.869 119.658 285.869 120.578C285.869 121.342 285.296 122.002 284.532 122.089C277.881 122.835 272.88 129.139 274.269 136.223C275.172 140.825 278.888 144.541 283.49 145.444C291.043 146.92 297.694 141.137 297.694 133.844C297.694 131.396 296.947 129.034 295.558 127.055C295.124 126.43 295.245 125.579 295.818 125.075C296.513 124.468 297.572 124.589 298.093 125.353C299.83 127.836 300.75 130.806 300.75 133.862C300.75 142.214 293.839 148.986 285.435 148.743V148.726Z" fill="#F0F0F0" />
          <path d="M188.073 303.253L184.375 297.227C182.378 297.505 180.728 297.505 178.731 297.227L175.032 303.253L174.321 303.062C171.942 302.419 169.632 301.464 167.479 300.231L166.837 299.866L168.399 293.32C168.521 292.782 169.059 292.469 169.597 292.591C170.136 292.712 170.448 293.251 170.327 293.789L169.094 298.894C170.691 299.762 172.376 300.457 174.112 300.978L177.724 295.091L178.384 295.195C180.485 295.543 182.621 295.543 184.704 295.195L185.364 295.091L188.976 300.978C190.713 300.457 192.397 299.762 193.994 298.894L192.379 292.174L192.918 291.792C194.394 290.733 195.696 289.5 196.807 288.076C197.137 287.659 197.728 287.555 198.162 287.868C198.613 288.198 198.717 288.84 198.37 289.274C197.259 290.663 195.991 291.914 194.585 292.99L196.234 299.866L195.592 300.231C193.439 301.481 191.147 302.436 188.75 303.062L188.038 303.253H188.073ZM165.395 290.09C165.083 289.743 164.788 289.361 164.492 288.996L157.616 290.646L157.251 290.004C156.001 287.85 155.046 285.558 154.421 283.162L154.23 282.45L160.255 278.751C160.117 277.744 160.047 276.824 160.047 275.921C160.047 275.018 160.117 274.098 160.255 273.091L154.23 269.392L154.421 268.68C155.063 266.301 156.019 263.992 157.251 261.839L157.616 261.196L164.492 262.846C165.638 261.335 166.993 259.998 168.486 258.852L167.236 253.643C167.114 253.122 167.427 252.583 167.965 252.462C168.486 252.34 169.024 252.653 169.146 253.191L170.691 259.668L170.153 260.067C168.434 261.3 166.923 262.811 165.673 264.547L165.291 265.086L158.571 263.471C157.703 265.068 157.008 266.753 156.487 268.489L162.374 272.101L162.27 272.761C162.079 273.924 161.992 274.949 161.992 275.921C161.992 276.893 162.079 277.935 162.27 279.081L162.374 279.741L156.487 283.353C157.008 285.089 157.703 286.774 158.571 288.371L165.291 286.756L165.673 287.295C166.038 287.798 166.437 288.302 166.837 288.771C167.184 289.17 167.166 289.743 166.802 290.108L166.767 290.142C166.368 290.524 165.743 290.49 165.378 290.073L165.395 290.09ZM205.49 290.646L202.954 290.038C202.416 289.917 202.104 289.378 202.225 288.84C202.347 288.302 202.885 287.989 203.423 288.111L204.517 288.371C205.385 286.774 206.08 285.089 206.601 283.353L200.714 279.741L200.819 279.081C201.01 277.918 201.096 276.893 201.096 275.921C201.096 274.949 201.01 273.907 200.819 272.761L200.714 272.101L206.601 268.489C206.08 266.753 205.385 265.068 204.517 263.471L197.797 265.086L197.415 264.547C196.182 262.828 194.672 261.318 192.935 260.067L192.397 259.668L194.012 252.948C192.414 252.08 190.73 251.385 188.993 250.864L185.382 256.751L184.722 256.647C182.621 256.299 180.485 256.299 178.384 256.647L177.724 256.751L174.112 250.864C173.886 250.934 173.661 251.003 173.435 251.073C172.931 251.229 172.393 250.968 172.219 250.465C172.028 249.944 172.306 249.354 172.827 249.197C173.313 249.041 173.817 248.885 174.303 248.763L175.015 248.572L178.714 254.598C180.728 254.32 182.343 254.32 184.357 254.598L188.056 248.572L188.768 248.763C191.147 249.406 193.456 250.361 195.609 251.594L196.252 251.958L194.602 258.834C196.113 259.981 197.45 261.335 198.596 262.828L205.472 261.179L205.837 261.821C207.087 263.974 208.042 266.266 208.667 268.663L208.858 269.375L202.833 273.073C202.972 274.08 203.041 275.001 203.041 275.904C203.041 276.807 202.972 277.727 202.833 278.734L208.858 282.433L208.667 283.145C208.025 285.524 207.07 287.833 205.837 289.986L205.472 290.629L205.49 290.646ZM181.284 285.558C176.265 285.419 172.098 281.235 171.942 276.234C171.803 271.181 175.571 266.961 180.45 266.405C181.041 266.336 181.561 266.805 181.561 267.395C181.561 267.899 181.197 268.315 180.693 268.368C176.404 268.854 173.174 272.917 174.06 277.501C174.65 280.47 177.047 282.867 180.016 283.457C184.895 284.412 189.184 280.679 189.184 275.973C189.184 274.393 188.698 272.865 187.795 271.58C187.517 271.181 187.587 270.625 187.952 270.295C188.403 269.896 189.08 269.982 189.428 270.469C190.539 272.066 191.147 273.994 191.147 275.956C191.147 281.356 186.684 285.715 181.249 285.576L181.284 285.558Z" fill="#E6E6E6" />
          <g filter="url(#filter0_d_823_10589)">
            <path d="M434.109 646.059C620.051 646.059 770.787 637.258 770.787 626.403C770.787 615.547 620.051 606.746 434.109 606.746C248.167 606.746 97.4316 615.547 97.4316 626.403C97.4316 637.258 248.167 646.059 434.109 646.059Z" fill="#F5F5F5" />
          </g>
          <g filter="url(#filter1_d_823_10589)">
            <path d="M631.107 157.791H574.568L534.578 614.908H591.117C607.196 614.908 621.417 601.832 622.824 585.753L657.709 186.963C659.115 170.884 647.186 157.808 631.107 157.808V157.791Z" fill="#407BFF" />
            <path d="M619.578 157.791H428.223C412.143 157.791 397.922 170.866 396.515 186.946L361.63 585.736C360.224 601.815 372.153 614.89 388.232 614.89H579.588C595.667 614.89 609.888 601.815 611.295 585.736L646.18 186.946C647.586 170.866 635.657 157.791 619.578 157.791Z" fill="#407BFF" />
            <path opacity="0.3" d="M619.578 157.791H428.223C412.143 157.791 397.922 170.866 396.515 186.946L361.63 585.736C360.224 601.815 372.153 614.89 388.232 614.89H579.588C595.667 614.89 609.888 601.815 611.295 585.736L646.18 186.946C647.586 170.866 635.657 157.791 619.578 157.791Z" fill="white" />
            <path opacity="0.4" d="M618.76 167.133H568.316C565.347 167.133 562.551 169.078 561.44 171.943C557.36 182.483 547.028 189.707 536.036 189.707H506.204C495.195 189.707 486.131 182.483 483.908 171.943C483.301 169.095 480.852 167.133 477.883 167.133H427.439C416.5 167.133 406.845 176.006 405.89 186.946L371.005 585.736C370.05 596.675 378.142 605.548 389.082 605.548H580.437C591.376 605.548 601.031 596.675 601.986 585.736L636.871 186.946C637.826 176.006 629.734 167.133 618.795 167.133H618.76Z" fill="white" />
            <path d="M571.081 383.771H437.167C433.19 383.771 430.256 380.559 430.603 376.582L431.28 368.924C431.628 364.948 435.135 361.736 439.094 361.736H573.008C576.985 361.736 579.919 364.948 579.572 368.924L578.895 376.582C578.547 380.559 575.04 383.771 571.081 383.771Z" fill="white" />
            <path d="M568.198 416.833H434.284C430.308 416.833 427.373 413.62 427.72 409.644L428.397 401.986C428.745 398.01 432.252 394.797 436.211 394.797H570.125C574.102 394.797 577.036 398.01 576.689 401.986L576.012 409.644C575.665 413.62 572.157 416.833 568.198 416.833Z" fill="white" />
            <path opacity="0.1" d="M530.393 174.616C530.011 179.027 526.121 182.587 521.728 182.587C517.335 182.587 514.07 179.01 514.452 174.616C514.834 170.206 518.724 166.646 523.117 166.646C527.51 166.646 530.775 170.223 530.393 174.616Z" fill="black" />
            <path opacity="0.1" d="M526.78 174.617C526.572 177.031 524.454 178.975 522.04 178.975C519.626 178.975 517.855 177.031 518.064 174.617C518.272 172.203 520.39 170.259 522.804 170.259C525.218 170.259 526.989 172.203 526.78 174.617Z" fill="black" />
            <g opacity="0.8">
              <path d="M595.202 187.899C593.917 187.899 592.962 186.858 593.066 185.555L593.448 181.284C593.569 179.999 594.698 178.939 595.983 178.939C597.268 178.939 598.223 179.981 598.119 181.284L597.737 185.555C597.615 186.84 596.487 187.899 595.202 187.899Z" fill="white" />
              <path d="M613.886 187.901C612.601 187.901 611.646 186.859 611.75 185.557L612.618 175.537C612.722 174.252 613.868 173.193 615.153 173.193C616.438 173.193 617.393 174.235 617.289 175.537L616.421 185.557C616.317 186.842 615.171 187.901 613.886 187.901Z" fill="white" />
              <path d="M604.546 187.901C603.261 187.901 602.306 186.859 602.411 185.557L603.036 178.42C603.157 177.135 604.286 176.076 605.571 176.076C606.856 176.076 607.811 177.118 607.707 178.42L607.082 185.557C606.977 186.842 605.831 187.901 604.546 187.901Z" fill="white" />
            </g>
            <g opacity="0.8">
              <path d="M435.222 187.901H423.484C422.755 187.901 422.216 187.311 422.286 186.582L422.841 180.278C422.911 179.549 423.553 178.959 424.283 178.959H436.021C436.75 178.959 437.289 179.549 437.219 180.278L436.664 186.582C436.594 187.311 435.952 187.901 435.222 187.901Z" fill="white" />
              <path d="M440.657 186.199C439.928 186.199 439.39 185.609 439.459 184.88L439.72 181.997C439.789 181.268 440.432 180.677 441.161 180.677C441.89 180.677 442.428 181.268 442.359 181.997L442.099 184.88C442.029 185.609 441.387 186.199 440.657 186.199Z" fill="white" />
            </g>
            <path d="M549.013 333.375C572.38 311.006 574.71 275.52 554.219 254.114C533.727 232.708 498.172 233.488 474.806 255.857C451.439 278.225 449.109 313.712 469.601 335.118C490.093 356.524 525.647 355.743 549.013 333.375Z" fill="white" />
            <path d="M512.111 292.469C497.369 292.469 484.936 298.078 483.877 310.25L482.696 323.76H536.056L537.237 310.25C538.296 298.078 526.853 292.469 512.111 292.469Z" fill="#407BFF" />
            <path d="M512.597 286.896C519.438 286.896 522.581 281.357 523.189 274.515C523.779 267.674 521.609 262.135 514.767 262.135C507.926 262.135 504.783 267.674 504.175 274.515C503.585 281.357 505.755 286.896 512.597 286.896Z" fill="#407BFF" />
            <path d="M564.968 372.344C561.321 372.344 558.265 373.733 557.987 376.737L557.692 380.071H570.871L571.167 376.737C571.427 373.733 568.597 372.344 564.968 372.344Z" fill="#407BFF" />
            <path d="M565.088 370.973C566.773 370.973 567.554 369.601 567.71 367.917C567.867 366.233 567.311 364.861 565.627 364.861C563.942 364.861 563.161 366.233 563.005 367.917C562.848 369.601 563.404 370.973 565.088 370.973Z" fill="#407BFF" />
            <path d="M524.386 449.894H472.275C466.181 449.894 461.683 444.962 462.221 438.868C462.76 432.773 468.125 427.841 474.203 427.841H526.313C532.408 427.841 536.906 432.773 536.367 438.868C535.829 444.962 530.463 449.894 524.386 449.894Z" fill="#407BFF" />
            <path d="M518.102 572.781H462.327C455.903 572.781 451.145 567.572 451.7 561.13L455.26 520.445C455.816 514.02 461.494 508.793 467.919 508.793H523.693C530.118 508.793 534.876 514.003 534.32 520.445L530.76 561.13C530.205 567.554 524.526 572.781 518.102 572.781Z" fill="#407BFF" />
            <g opacity="0.6">
              <path d="M500.913 530.744C501.243 526.872 498.396 523.729 494.523 523.729C490.651 523.729 487.248 526.872 486.9 530.744C486.692 533.054 487.647 535.068 489.262 536.353L488.029 550.366C487.821 552.658 489.522 554.533 491.832 554.533C494.141 554.533 496.156 552.675 496.364 550.366L497.597 536.353C499.42 535.068 500.722 533.054 500.931 530.744H500.913Z" fill="white" />
            </g>
            <path d="M524.194 517.286H517.005L518.759 497.161C519.818 485.075 510.841 475.229 498.738 475.229C486.635 475.229 475.938 485.075 474.879 497.161L473.125 517.286H465.937L467.69 497.161C469.097 481.099 483.301 468.041 499.363 468.041C515.425 468.041 527.337 481.099 525.93 497.161L524.177 517.286H524.194Z" fill="#407BFF" />
            <path d="M565.854 403.167L566.027 401.239C566.201 399.26 564.743 397.662 562.763 397.662C560.783 397.662 559.047 399.26 558.873 401.239L558.7 403.167C557.918 403.167 557.224 403.809 557.154 404.59L556.703 409.73C556.633 410.512 557.224 411.154 558.005 411.154H565.159C565.941 411.154 566.635 410.512 566.705 409.73L567.156 404.59C567.226 403.809 566.635 403.167 565.854 403.167ZM562.676 398.548C564.169 398.548 565.263 399.763 565.142 401.239L564.968 403.167H559.585L559.759 401.239C559.88 399.746 561.2 398.548 562.693 398.548H562.676Z" fill="#407BFF" />
            <path d="M458.158 405.232L455.657 404.711C455.362 404.642 455.206 404.347 455.327 404.052L456.369 401.586C456.595 401.065 455.953 400.666 455.553 401.082L453.66 403.027C453.435 403.253 453.07 403.253 452.896 403.027L451.351 401.082C451.021 400.666 450.309 401.048 450.448 401.586L451.056 404.052C451.125 404.347 450.917 404.659 450.622 404.711L448.034 405.232C447.479 405.337 447.409 406.118 447.948 406.24L450.448 406.76C450.743 406.83 450.899 407.125 450.778 407.42L449.736 409.886C449.51 410.407 450.153 410.806 450.552 410.39L452.445 408.445C452.671 408.219 453.035 408.219 453.209 408.445L454.754 410.39C455.084 410.806 455.796 410.424 455.657 409.886L455.05 407.42C454.98 407.125 455.188 406.813 455.484 406.76L458.071 406.24C458.627 406.135 458.696 405.354 458.158 405.232Z" fill="#263238" />
            <path d="M474.586 405.232L472.086 404.711C471.79 404.642 471.634 404.347 471.756 404.052L472.797 401.586C473.023 401.065 472.381 400.666 471.981 401.082L470.089 403.027C469.863 403.253 469.498 403.253 469.325 403.027L467.779 401.082C467.449 400.666 466.737 401.048 466.876 401.586L467.484 404.052C467.553 404.347 467.345 404.659 467.05 404.711L464.463 405.232C463.907 405.337 463.837 406.118 464.376 406.24L466.876 406.76C467.171 406.83 467.328 407.125 467.206 407.42L466.164 409.886C465.939 410.407 466.581 410.806 466.98 410.39L468.873 408.445C469.099 408.219 469.463 408.219 469.637 408.445L471.183 410.39C471.513 410.806 472.224 410.424 472.086 409.886L471.478 407.42C471.408 407.125 471.617 406.813 471.912 406.76L474.499 406.24C475.055 406.135 475.124 405.354 474.586 405.232Z" fill="#263238" />
            <path d="M491.011 405.232L488.511 404.711C488.216 404.642 488.059 404.347 488.181 404.052L489.223 401.586C489.448 401.065 488.806 400.666 488.407 401.082L486.514 403.027C486.288 403.253 485.923 403.253 485.75 403.027L484.204 401.082C483.875 400.666 483.163 401.048 483.301 401.586L483.909 404.052C483.979 404.347 483.77 404.659 483.475 404.711L480.888 405.232C480.332 405.337 480.263 406.118 480.801 406.24L483.301 406.76C483.597 406.83 483.753 407.125 483.631 407.42L482.59 409.886C482.364 410.407 483.006 410.806 483.406 410.39L485.298 408.445C485.524 408.219 485.889 408.219 486.062 408.445L487.608 410.39C487.938 410.806 488.65 410.424 488.511 409.886L487.903 407.42C487.834 407.125 488.042 406.813 488.337 406.76L490.924 406.24C491.48 406.135 491.55 405.354 491.011 405.232Z" fill="#263238" />
            <path d="M507.458 405.232L504.957 404.711C504.662 404.642 504.506 404.347 504.627 404.052L505.669 401.586C505.895 401.065 505.252 400.666 504.853 401.082L502.96 403.027C502.734 403.253 502.37 403.253 502.196 403.027L500.651 401.082C500.321 400.666 499.609 401.048 499.748 401.586L500.356 404.052C500.425 404.347 500.217 404.659 499.921 404.711L497.334 405.232C496.779 405.337 496.709 406.118 497.247 406.24L499.748 406.76C500.043 406.83 500.199 407.125 500.078 407.42L499.036 409.886C498.81 410.407 499.453 410.806 499.852 410.39L501.745 408.445C501.97 408.219 502.335 408.219 502.509 408.445L504.054 410.39C504.384 410.806 505.096 410.424 504.957 409.886L504.349 407.42C504.28 407.125 504.488 406.813 504.783 406.76L507.371 406.24C507.926 406.135 507.996 405.354 507.458 405.232Z" fill="#263238" />
            <path d="M523.88 405.232L521.38 404.711C521.085 404.642 520.928 404.347 521.05 404.052L522.092 401.586C522.318 401.065 521.675 400.666 521.276 401.082L519.383 403.027C519.157 403.253 518.793 403.253 518.619 403.027L517.074 401.082C516.744 400.666 516.032 401.048 516.171 401.586L516.778 404.052C516.848 404.347 516.639 404.659 516.344 404.711L513.757 405.232C513.201 405.337 513.132 406.118 513.67 406.24L516.171 406.76C516.466 406.83 516.622 407.125 516.501 407.42L515.459 409.886C515.233 410.407 515.875 410.806 516.275 410.39L518.168 408.445C518.393 408.219 518.758 408.219 518.932 408.445L520.477 410.39C520.807 410.806 521.519 410.424 521.38 409.886L520.772 407.42C520.703 407.125 520.911 406.813 521.206 406.76L523.794 406.24C524.349 406.135 524.419 405.354 523.88 405.232Z" fill="#263238" />
            <path d="M540.309 405.232L537.808 404.711C537.513 404.642 537.357 404.347 537.478 404.052L538.52 401.586C538.746 401.065 538.103 400.666 537.704 401.082L535.811 403.027C535.586 403.253 535.221 403.253 535.047 403.027L533.502 401.082C533.172 400.666 532.46 401.048 532.599 401.586L533.207 404.052C533.276 404.347 533.068 404.659 532.773 404.711L530.185 405.232C529.63 405.337 529.56 406.118 530.098 406.24L532.599 406.76C532.894 406.83 533.05 407.125 532.929 407.42L531.887 409.886C531.661 410.407 532.304 410.806 532.703 410.39L534.596 408.445C534.822 408.219 535.186 408.219 535.36 408.445L536.905 410.39C537.235 410.806 537.947 410.424 537.808 409.886L537.2 407.42C537.131 407.125 537.339 406.813 537.635 406.76L540.222 406.24C540.777 406.135 540.847 405.354 540.309 405.232Z" fill="#263238" />
            <path d="M500.702 369.271H469.845C467.935 369.271 466.234 370.834 466.077 372.744C465.904 374.654 467.328 376.217 469.238 376.217H500.094C502.004 376.217 503.706 374.654 503.862 372.744C504.036 370.834 502.612 369.271 500.702 369.271Z" fill="#263238" />
            <path d="M532.027 369.271H514.125C512.215 369.271 510.513 370.834 510.357 372.744C510.183 374.654 511.607 376.217 513.517 376.217H531.42C533.33 376.217 535.031 374.654 535.188 372.744C535.361 370.834 533.937 369.271 532.027 369.271Z" fill="#263238" />
            <path d="M555.659 526.68C544.025 531.334 533.832 538.106 526.851 545.798C522.753 542.308 516.936 539.894 509.973 538.783L509.157 538.662L508.792 542.76L509.244 542.933C516.12 545.538 521.729 550.157 525.445 556.286L526.035 557.241L526.678 556.217C533.832 544.756 544.268 535.171 556.058 529.198L556.458 528.99L556.701 526.263L555.676 526.68H555.659Z" fill="#263238" />
          </g>
          <path opacity="0.2" d="M400.489 184.792C398.318 188.613 396.895 192.867 396.495 197.382L370.64 492.958H450.655L466.734 431.332L461.438 369.706L477.517 308.079L472.221 246.453L488.301 184.827H400.506L400.489 184.792Z" fill="black" />
          <path d="M465.281 229.053L481.36 167.427H279.013L262.951 229.053L268.23 290.696L252.168 352.322L257.447 413.948L241.385 475.592H443.714L459.794 413.948L454.498 352.322L470.577 290.696L465.281 229.053Z" fill="#407BFF" />
          <path opacity="0.7" d="M465.281 229.053L481.36 167.427H279.013L262.951 229.053L268.23 290.696L252.168 352.322L257.447 413.948L241.385 475.592H443.714L459.794 413.948L454.498 352.322L470.577 290.696L465.281 229.053Z" fill="white" />
          <path opacity="0.5" d="M465.281 229.053H262.952L279.014 167.427H481.361L465.281 229.053Z" fill="white" />
          <path opacity="0.5" d="M454.5 352.323H252.171L268.233 290.696H470.58L454.5 352.323Z" fill="white" />
          <path opacity="0.5" d="M443.714 475.592H241.385L257.447 413.948H459.794L443.714 475.592Z" fill="white" />
          <path opacity="0.2" d="M438.207 254.821C436.471 255.359 434.248 255.689 431.817 255.689C428.24 255.689 425.115 254.977 423.326 253.918C422.84 253.622 422.302 253.484 421.763 253.484C421.26 253.484 420.774 253.622 420.305 253.883C420.27 253.9 420.235 253.9 420.201 253.935C420.201 253.935 420.166 253.952 420.131 253.97C419.471 254.352 418.638 254.699 417.665 254.96C415.998 255.428 413.932 255.706 411.709 255.706C411.102 255.706 410.511 255.689 409.938 255.654C408.202 255.533 406.622 255.255 405.319 254.838C405.094 254.768 404.868 254.716 404.642 254.716C403.062 254.612 401.655 255.828 401.655 257.495V259.37H323.012C316.917 259.37 311.986 264.302 311.986 270.396V281.51C311.986 287.604 316.917 292.536 323.012 292.536H412.491C412.682 292.536 412.873 292.519 413.081 292.501C414.054 293.3 415.043 294.099 416.12 294.898C417.388 295.87 418.742 296.842 420.148 297.867C421.121 298.579 422.475 298.579 423.448 297.867C434.7 289.706 441.923 284.08 441.923 268.608V257.495C441.923 255.585 440.065 254.282 438.259 254.855L438.207 254.821Z" fill="black" />
          <path d="M410.738 253.221H321.277C315.187 253.221 310.25 258.158 310.25 264.247V275.343C310.25 281.433 315.187 286.37 321.277 286.37H410.738C416.828 286.37 421.764 281.433 421.764 275.343V264.247C421.764 258.158 416.828 253.221 410.738 253.221Z" fill="#407BFF" />
          <path d="M430.099 249.557C426.522 249.557 423.397 248.845 421.608 247.786C420.636 247.195 419.455 247.195 418.483 247.786C416.694 248.845 413.551 249.557 409.991 249.557C407.56 249.557 405.338 249.227 403.601 248.689C401.778 248.116 399.938 249.435 399.938 251.328V262.441C399.938 277.913 407.161 283.539 418.413 291.7C419.386 292.412 420.74 292.412 421.712 291.7C432.965 283.539 440.188 277.913 440.188 262.441V251.328C440.188 249.418 438.33 248.116 436.524 248.689C434.788 249.227 432.565 249.557 430.134 249.557H430.099Z" fill="#407BFF" />
          <path opacity="0.3" d="M430.099 249.557C426.522 249.557 423.397 248.845 421.608 247.786C420.636 247.195 419.455 247.195 418.483 247.786C416.694 248.845 413.551 249.557 409.991 249.557C407.56 249.557 405.338 249.227 403.601 248.689C401.778 248.116 399.938 249.435 399.938 251.328V262.441C399.938 277.913 407.161 283.539 418.413 291.7C419.386 292.412 420.74 292.412 421.712 291.7C432.965 283.539 440.188 277.913 440.188 262.441V251.328C440.188 249.418 438.33 248.116 436.524 248.689C434.788 249.227 432.565 249.557 430.134 249.557H430.099Z" fill="white" />
          <path opacity="0.2" d="M418.483 247.786C416.694 248.845 413.551 249.557 409.991 249.557C407.56 249.557 405.338 249.227 403.601 248.689C401.778 248.116 399.938 249.436 399.938 251.328V262.442C399.938 277.913 407.161 283.539 418.413 291.701C418.899 292.048 419.49 292.239 420.063 292.239V247.352C419.524 247.352 418.986 247.491 418.5 247.786H418.483Z" fill="white" />
          <path d="M436.178 259.42C428.902 262.424 422.686 266.817 418.605 271.801C415.758 269.543 411.851 267.981 407.266 267.269L406.728 267.182V269.839L407.041 269.943C411.642 271.627 415.532 274.614 418.293 278.59L418.727 279.215L419.091 278.538C423.068 271.124 429.284 264.907 436.577 261.035L436.82 260.896V259.125L436.178 259.385V259.42Z" fill="#263238" />
          <path d="M477.781 184.755H470.106C465.852 184.755 462.691 181.299 463.073 177.045L463.751 169.37C464.115 165.116 467.883 161.66 472.137 161.66H479.813C484.067 161.66 487.227 165.116 486.845 169.37L486.168 177.045C485.803 181.299 482.035 184.755 477.781 184.755Z" fill="#407BFF" />
          <path d="M480.977 169.353L479.31 167.53L475.108 171.385L471.601 167.53L469.604 169.353L473.129 173.208L468.944 177.063L470.611 178.903L474.796 175.048L478.321 178.903L480.3 177.063L476.775 173.208L480.977 169.353Z" fill="white" />
          <path d="M306.258 250.987C310.703 255.831 314.957 260.832 319.159 265.903C323.361 270.956 327.459 276.096 331.505 281.288C331.679 281.513 331.61 281.426 331.662 281.496C331.679 281.531 331.679 281.583 331.714 281.6C331.766 281.652 331.783 281.739 331.835 281.774C331.922 281.895 332.026 281.999 332.148 282.121C332.391 282.347 332.738 282.503 333.103 282.572C333.294 282.607 333.485 282.625 333.693 282.607C333.797 282.607 333.902 282.59 334.006 282.59C334.058 282.59 334.11 282.555 334.162 282.555C334.249 282.52 334.197 282.59 334.44 282.451L338.781 280.089C344.615 276.999 350.502 273.977 356.579 271.268L361.667 277.572C356.771 282.086 351.665 287.712 346.508 291.845L342.619 294.901C342.514 294.988 342.098 295.266 341.837 295.439C341.542 295.63 341.247 295.839 340.934 296.012C340.309 296.36 339.667 296.707 338.989 296.968C337.652 297.541 336.228 297.905 334.787 298.131C331.887 298.565 328.883 298.27 326.07 297.263C324.664 296.759 323.309 296.082 322.076 295.231C321.451 294.814 320.861 294.346 320.288 293.859C319.993 293.616 319.732 293.356 319.472 293.095L318.777 292.401C314.176 287.695 309.644 282.937 305.198 278.075C300.753 273.23 296.36 268.334 292.158 263.281L306.24 250.987H306.258Z" fill="#B55B52" />
          <path d="M354.219 275.106L358.265 262.1L368.927 267.414C368.927 267.414 367.381 276.895 359.62 278.544L354.219 275.106Z" fill="#B55B52" />
          <path d="M362.535 258.506L372.155 261.145L368.925 267.414L358.263 262.1L362.535 258.506Z" fill="#B55B52" />
          <path d="M250.206 254.529C249.042 260.051 247.636 265.521 246.142 270.956C244.666 276.408 243.051 281.826 241.367 287.209L240.742 289.24L240.429 290.248C240.36 290.473 240.325 290.525 240.256 290.664C240.065 291.168 239.943 291.724 239.926 292.314C239.891 293.477 240.169 294.762 240.933 295.874C241.002 296.047 241.593 296.69 242.062 297.193L243.503 298.756L246.351 301.934C248.226 304.087 250.084 306.24 251.855 308.498L245.812 315.495C243.312 314.072 240.916 312.543 238.519 310.998L234.96 308.654L233.206 307.456C232.615 307.039 232.129 306.761 231.174 305.962C227.875 303.167 225.409 299.19 224.506 294.762C224.037 292.557 223.933 290.265 224.194 287.99C224.263 287.452 224.367 286.809 224.454 286.341L224.645 285.299L225.01 283.215C225.982 277.641 227.024 272.084 228.188 266.58C229.351 261.058 230.584 255.554 232.077 250.101L250.24 254.494L250.206 254.529Z" fill="#B55B52" />
          <path d="M245.932 308.029L253.103 304.365L256.263 316.537C256.263 316.537 250.516 318.743 246.071 315.912L245.949 308.029H245.932Z" fill="#B55B52" />
          <path d="M262.986 300.475L263.316 311.64L255.311 316.884L252.532 304.608L262.986 300.475Z" fill="#B55B52" />
          <path d="M286.409 197.122C286.583 197.99 287.173 198.581 287.746 198.477C288.302 198.355 288.632 197.574 288.441 196.705C288.25 195.837 287.66 195.247 287.104 195.351C286.548 195.473 286.236 196.254 286.409 197.122Z" fill="#263238" />
          <path d="M287.104 198.598C287.104 198.598 290.073 203.165 292.713 205.127C291.48 206.864 288.754 206.742 288.754 206.742L287.104 198.616V198.598Z" fill="#A02724" />
          <path d="M282.293 192.974C282.171 192.974 282.05 192.922 281.946 192.835C281.737 192.644 281.72 192.314 281.911 192.106C283.856 190.005 286.2 190.387 286.304 190.404C286.582 190.456 286.773 190.717 286.721 190.995C286.669 191.272 286.408 191.463 286.13 191.411C286.13 191.411 284.22 191.133 282.675 192.8C282.571 192.905 282.432 192.974 282.293 192.974Z" fill="#263238" />
          <path d="M261.368 209.227C263.278 217.197 265.188 231.783 258.364 237.097C258.364 237.097 261.038 245.171 279.201 245.171C299.17 245.171 288.751 239.962 288.751 239.962C277.847 237.843 278.142 228.38 280.035 222.181L261.385 209.209L261.368 209.227Z" fill="#B55B52" />
          <path d="M256.23 238.572C255.032 236.367 253.729 233.832 256.438 233.033C259.425 232.165 278.578 230.55 284.204 232.182C282.467 235.256 285.402 239.041 285.402 239.041L256.23 238.572Z" fill="#263238" />
          <path d="M272.1 611.176H284.619L285.87 585.963H273.332L272.1 611.176Z" fill="#B55B52" />
          <path d="M192.59 607.704L205.283 608.363L212.993 583.532L200.3 582.855L192.59 607.704Z" fill="#B55B52" />
          <g filter="url(#filter2_d_823_10589)">
            <path d="M204.9 607.357L192.954 604.77C192.52 604.683 192.068 604.892 191.894 605.308L187.744 614.303C187.31 615.241 187.883 616.335 188.89 616.526C193.075 617.359 195.159 617.55 200.42 618.696C203.65 619.391 210.96 621.179 215.423 622.135C219.799 623.072 221.431 618.818 219.695 618.019C211.881 614.442 208.616 611.317 206.515 608.382C206.133 607.861 205.56 607.496 204.935 607.357H204.9Z" fill="#263238" />
          </g>
          <g filter="url(#filter3_d_823_10589)">
            <path d="M283.96 609.909H272.065C271.614 609.909 271.232 610.221 271.145 610.655L268.992 620.327C268.766 621.334 269.53 622.289 270.555 622.272C274.844 622.203 281.043 621.942 286.426 621.942C292.729 621.942 298.181 622.289 305.561 622.289C310.024 622.289 311.274 617.775 309.399 617.358C300.873 615.5 293.927 615.292 286.565 610.76C285.766 610.273 284.88 609.909 283.943 609.909H283.96Z" fill="#263238" />
          </g>
          <path d="M235.669 240.778C226.205 245.275 223.653 272.52 223.653 272.52L247.043 283.32C247.043 283.32 252.842 274.76 253.294 261.32C253.763 247.237 245.341 236.159 235.686 240.76L235.669 240.778Z" fill="#263238" />
          <path opacity="0.4" d="M248.364 257.586C244.145 264.132 244.544 274.29 246.003 282.851L247.045 283.337C247.045 283.337 252.862 274.776 253.296 261.336C253.313 260.503 253.313 259.669 253.296 258.853L248.364 257.586Z" fill="black" />
          <path d="M300.944 240.118C300.944 240.118 312.526 253.211 301.586 334.216H243.346C242.947 325.273 248.556 281.671 239.943 239.545C239.943 239.545 249.927 237.548 259.443 237.079C266.892 236.715 277.658 236.489 284.205 237.079C292.835 237.861 300.944 240.101 300.944 240.101V240.118Z" fill="#407BFF" />
          <path opacity="0.6" d="M300.944 240.118C300.944 240.118 312.526 253.211 301.586 334.216H243.346C242.947 325.273 248.556 281.671 239.943 239.545C239.943 239.545 249.927 237.548 259.443 237.079C266.892 236.715 277.658 236.489 284.205 237.079C292.835 237.861 300.944 240.101 300.944 240.101V240.118Z" fill="#FAFAFA" />
          <path opacity="0.2" d="M306.029 264.878L305.803 264.687L294.221 258.505C293.04 276.339 301.01 287.521 305.751 292.401C306.411 280.975 306.393 271.963 306.011 264.878H306.029Z" fill="black" />
          <path opacity="0.2" d="M285.87 585.963L285.227 598.951H272.69L273.333 585.963H285.87Z" fill="black" />
          <path opacity="0.2" d="M200.3 582.855L212.993 583.532L209.017 596.33L196.323 595.67L200.3 582.855Z" fill="black" />
          <path d="M257.34 201.134C260.136 211.57 261.247 217.821 267.689 222.162C277.378 228.691 289.447 221.189 289.811 210.128C290.159 200.179 285.522 184.776 274.322 182.675C263.296 180.609 254.544 190.698 257.34 201.134Z" fill="#B55B52" />
          <path d="M280.94 334.233C280.94 334.233 268.577 417.443 261.805 448.612C254.373 482.75 214.539 595.688 214.539 595.688L195.056 591.469C195.056 591.469 220.026 499.437 226.26 451.703C228.691 433.088 231.625 403.065 235.341 378.026C238.936 353.785 243.364 334.233 243.364 334.233H280.94Z" fill="#263238" />
          <path d="M219.209 589.159L215.58 596.208L193.648 591.468L195.489 584.87L219.209 589.159Z" fill="#407BFF" />
          <path opacity="0.6" d="M219.209 589.159L215.58 596.208L193.648 591.468L195.489 584.87L219.209 589.159Z" fill="white" />
          <path opacity="0.6" d="M273.126 363.874C258.835 366.67 260.346 419.961 262.065 447.362C266.18 427.983 272.223 390.719 276.339 364.256C275.384 363.787 274.324 363.631 273.126 363.874Z" fill="black" />
          <path d="M301.583 334.233C301.583 334.233 307.4 415.637 306.202 448.838C304.969 483.375 288.594 596.452 288.594 596.452H270.153C270.153 596.452 269.476 485.39 268.66 451.477C267.757 414.508 263.294 334.233 263.294 334.233H301.565H301.583Z" fill="#263238" />
          <path d="M292.155 589.088L291.113 596.746H268.679L268.071 589.904L292.155 589.088Z" fill="#407BFF" />
          <path opacity="0.6" d="M292.155 589.088L291.113 596.746H268.679L268.071 589.904L292.155 589.088Z" fill="white" />
          <path d="M294.154 255.363C294.154 277.207 305.788 286.202 305.788 286.202L324.681 267.726C324.681 267.726 318.256 256.665 309.418 246.49C298.495 233.935 294.154 241.298 294.154 255.363Z" fill="#263238" />
          <path opacity="0.2" d="M289.152 214.087C288.666 215.424 287.415 217.699 284.255 218.602C279.636 219.922 279.584 223.829 279.584 223.829L279.636 223.898C284.029 222.44 287.693 218.811 289.152 214.087Z" fill="black" />
          <path d="M284.255 180.384C288.144 175.47 282.153 175.244 282.153 175.244C283.716 169.392 276.701 172.535 276.701 172.535C272.499 166.736 270.12 173.785 270.12 173.785C265.761 168.871 264.963 176.894 264.963 176.894C260.587 170.66 258.364 179.655 258.364 179.655C254.336 173.178 251.87 181.617 251.87 181.617C243.552 178.647 247.512 187.278 247.512 187.278C240.41 187.469 246.105 192.973 246.105 192.973C237.597 192.99 245.098 197.87 245.098 197.87C238.343 202.298 246.817 203.496 246.817 203.496C241.312 212.109 250.776 208.914 250.776 208.914C246.939 217.439 255.447 213.689 255.447 213.689C254.891 221.919 259.649 219.679 261.941 218.151C262.375 219.801 263.209 221.572 264.737 221.763C267.706 222.145 268.279 216.554 268.279 216.554C268.279 216.554 271.04 218.69 273.332 217.526C275.642 216.363 274.774 213.428 274.774 213.428C274.774 213.428 276.545 214.748 278.16 213.48C279.149 212.716 279.149 207.177 277.899 205.232L282.414 203.461C282.414 203.461 285.765 202.992 284.272 198.564C282.779 194.137 284.046 190.646 284.046 190.646C289.151 188.25 284.966 185.385 284.966 185.385C290.87 181.565 284.272 180.349 284.272 180.349L284.255 180.384Z" fill="#263238" />
          <path d="M275.107 201.569C275.819 204.312 277.625 206.656 279.5 208.063C282.33 210.164 284.449 208.098 284.24 204.885C284.067 202.003 282.365 197.418 279.24 196.568C276.166 195.717 274.291 198.426 275.089 201.569H275.107Z" fill="#B55B52" />
          <defs>
            <filter id="filter0_d_823_10589" x="91.4316" y="604.746" width="685.355" height="51.3129" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
              <feOffset dy="4" />
              <feGaussianBlur stdDeviation="3" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
              <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_823_10589" />
              <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_823_10589" result="shape" />
            </filter>
            <filter id="filter1_d_823_10589" x="355.518" y="155.791" width="308.304" height="469.117" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
              <feOffset dy="4" />
              <feGaussianBlur stdDeviation="3" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
              <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_823_10589" />
              <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_823_10589" result="shape" />
            </filter>
            <filter id="filter2_d_823_10589" x="181.598" y="602.751" width="44.7793" height="29.5181" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
              <feOffset dy="4" />
              <feGaussianBlur stdDeviation="3" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
              <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_823_10589" />
              <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_823_10589" result="shape" />
            </filter>
            <filter id="filter3_d_823_10589" x="262.952" y="607.909" width="53.2881" height="24.3807" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
              <feOffset dy="4" />
              <feGaussianBlur stdDeviation="3" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
              <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_823_10589" />
              <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_823_10589" result="shape" />
            </filter>
          </defs>
        </svg>

        <svg width="330" className="background-svg" viewBox="0 0 650 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="650" height="12000" fill="#8BC8FF" />
          <mask id="mask0_823_10389" maskUnits="userSpaceOnUse" x="0" y="0" width="650" height="1080">
            <rect width="650" height="1080" fill="#8BC8FF" />
          </mask>
          <g mask="url(#mask0_823_10389)">
            <path d="M681.937 1014.42C680.115 345.448 159.038 80.1603 -102.183 31.1133L-115.916 -68.0273L681.937 -79.6289V1014.42C681.943 1016.77 681.943 1019.11 681.937 1021.46V1014.42Z" fill="#9ED1FF" />
          </g>
        </svg>
      </div>
      <ToastContainer />
    </div>
  );
};

export default Register;