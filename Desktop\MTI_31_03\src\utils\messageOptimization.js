import { debounce } from 'lodash';

/**
 * Optimized message handler to prevent UI blocking
 * Uses debouncing and async processing for better performance
 */
export const createOptimizedMessageHandler = (dispatch, setConsoleMsgs) => {
  // Debounced dispatch to prevent excessive Redux updates
  const debouncedDispatch = debounce((consoleMsgs) => {
    dispatch(setConsoleMsgs({ consoleMsgs }));
  }, 50);

  return (Msg) => {
    // Process message asynchronously to prevent blocking
    const processMessage = () => {
      try {
        dispatch((dispatch, getState) => {
          const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;
          const lastMsg = previousConsoleMsgs[ 0 ];

          // Check for duplicate messages
          if (
            lastMsg &&
            lastMsg.msg === Msg.msg &&
            lastMsg.user === Msg.user &&
            lastMsg.strategy === Msg.strategy &&
            lastMsg.portfolio === Msg.portfolio
          ) {
            // Update existing message
            const updatedMsgs = [ Msg, ...previousConsoleMsgs.slice(1) ];
            debouncedDispatch(updatedMsgs);
          } else {
            // Add new message
            const updatedMsgs = [ Msg, ...previousConsoleMsgs ];
            debouncedDispatch(updatedMsgs);
          }
        });
      } catch (error) {
        console.error('Error processing message:', error);
      }
    };

    // Use requestIdleCallback for better performance, fallback to setTimeout
    if (window.requestIdleCallback) {
      window.requestIdleCallback(processMessage, { timeout: 50 });
    } else {
      setTimeout(processMessage, 0);
    }
  };
};

/**
 * Throttled Redux dispatch to prevent excessive state updates
 */
export const createThrottledDispatch = (dispatch, delay = 100) => {
  return debounce((action) => {
    dispatch(action);
  }, delay);
};

/**
 * Batch multiple Redux actions to reduce re-renders
 */
export const batchActions = (dispatch, actions) => {
  // Use unstable_batchedUpdates if available (React 18+)
  if (window.ReactDOM && window.ReactDOM.unstable_batchedUpdates) {
    window.ReactDOM.unstable_batchedUpdates(() => {
      actions.forEach(action => dispatch(action));
    });
  } else {
    // Fallback: dispatch actions in next tick
    setTimeout(() => {
      actions.forEach(action => dispatch(action));
    }, 0);
  }
};

/**
 * Performance monitoring for message handlers
 */
export const withPerformanceMonitoring = (handler, name = 'handler') => {
  return (...args) => {
    const start = performance.now();

    try {
      const result = handler(...args);

      const end = performance.now();
      const duration = end - start;

      // Log warning if handler takes too long
      if (duration > 16) { // 16ms = 60fps threshold
        console.warn(`${name} took ${duration.toFixed(2)}ms - consider optimization`);
      }

      return result;
    } catch (error) {
      console.error(`Error in ${name}:`, error);
      throw error;
    }
  };
};

/**
 * Optimized array operations for large datasets
 */
export const optimizedArrayOperations = {
  // Chunked processing for large arrays
  processInChunks: (array, processor, chunkSize = 100) => {
    return new Promise((resolve) => {
      let index = 0;
      const results = [];

      const processChunk = () => {
        const chunk = array.slice(index, index + chunkSize);
        const chunkResults = chunk.map(processor);
        results.push(...chunkResults);

        index += chunkSize;

        if (index < array.length) {
          // Process next chunk in next tick
          setTimeout(processChunk, 0);
        } else {
          resolve(results);
        }
      };

      processChunk();
    });
  },

  // Debounced array updates
  createDebouncedArrayUpdater: (updateFunction, delay = 100) => {
    return debounce(updateFunction, delay);
  }
};
