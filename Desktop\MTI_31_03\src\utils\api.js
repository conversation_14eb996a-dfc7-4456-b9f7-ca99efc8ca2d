import Cookies from "universal-cookie";

/**
 * Utility function to make API calls with authentication token
 * @param {string} url - The API endpoint URL
 * @param {Object} options - Fetch options (method, headers, body, etc.)
 * @returns {Promise} - The fetch promise
 */
export const fetchWithAuth = async (url, options = {}) => {
  const cookies = new Cookies();
  const token = cookies.get("TOKEN");
  
  // Merge headers with Authorization header
  const headers = {
    ...options.headers || {},
    "Content-Type": options.headers?.["Content-Type"] || "application/json",
  };
  
  // Add Authorization header if token exists
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  // Return fetch with merged options
  return fetch(url, {
    ...options,
    headers,
  });
};

/**
 * Utility function to make API calls with authentication token and handle JSON response
 * @param {string} url - The API endpoint URL
 * @param {Object} options - Fetch options (method, headers, body, etc.)
 * @returns {Promise} - The JSON response promise
 */
export const fetchJsonWithAuth = async (url, options = {}) => {
  try {
    const response = await fetchWithAuth(url, options);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return response.json();
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
};
