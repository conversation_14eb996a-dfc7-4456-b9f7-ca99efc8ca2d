.master-account-container {
  font-family: Arial, sans-serif;
}

.main-section {
  display: flex;
  height: 100vh;
}

.middle-main-container {
  flex-grow: 1;
  padding: 20px;
}

.button-group {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.create-btn,
.delete-btn,
.trade-btn,
.sqoff-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.create-btn {
  background-color: #007bff;
  color: white;
}
.delete-btn {
  background-color: #dc3545;
  color: white;
}
.trade-btn {
  background-color: #ffc107;
  color: black;
}
.sqoff-btn {
  background-color: #28a745;
  color: white;
}

.create-btn:hover,
.delete-btn:hover,
.trade-btn:hover,
.sqoff-btn:hover {
  opacity: 0.9;
}

.main-table-container {
  overflow-x: auto;
  max-height: 70vh;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.master-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.master-table th {
  background-color: #f1f1f1;
  padding: 12px;
  text-align: left;
  border-bottom: 2px solid #ddd;
  cursor: pointer;
}

.master-table th:hover .sort-icon {
  color: #007bff;
}

.sort-icon {
  font-size: 10px;
  color: #ccc;
}

.master-table td {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.name-cell img {
  cursor: pointer;
  transition: transform 0.2s;
}

.name-cell img:hover {
  transform: scale(1.1);
}

.expand-icon {
  margin-right: 10px;
  cursor: pointer;
  font-weight: bold;
}

.positive {
  color: #28a745;
}

.negative {
  color: #dc3545;
}

.expanded td {
  background-color: #f9f9f9;
}

.child-table {
  width: 100%;
  border-collapse: collapse;
  margin-left: 20px;
}

.child-table th {
  background-color: #41729f;
  color: white;
  padding: 8px;
  text-align: center;
  border-bottom: 2px solid #ddd;
}

.child-table td {
  padding: 8px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

.collapse-btn {
  margin-top: 10px;
  padding: 10px 20px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.collapse-btn:hover {
  background-color: #5a6268;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  border-radius: 5px;
  width: 341px;
  text-align: center;
}

.modal-select {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.modal-btn {
  background-color: #007bff;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
}

.delete-btn-modal {
  background-color: #f44336;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
}

.cancel-btn {
  background-color: #007bff;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
  margin-left: 40px;
}

.yes-btn {
  background-color: #4caf50;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
}

.no-btn {
  background-color: #f44336;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
  margin-left: 40px;
}

.error-message {
  color: red;
}

.trade-modal-container {
  z-index: 1000;
}

.trade-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.trade-form {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  max-width: 700px;
  margin: auto;
  font-family: Arial, sans-serif;
}

.trade-type,
.order-type,
.product-type,
.price-type,
.order-type-1 {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.symbol-exchange-container {
  display: flex;
  margin-left: 130px;
}

.exchange-container .exchange-select {
  height: 30px;
  border-radius: 5px;
  width: 70px;
}

.symbol-container {
  position: relative;
}

.symbol-input {
  width: 300px;
  padding: 5px;
  font-size: 16px;
  border-radius: 5px;
  margin-left: 20px;
}

.dropdown {
  border: 1px solid #ccc;
  max-height: 150px;
  overflow-y: auto;
  width: 300px;
  text-align: left;
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: absolute;
  z-index: 1000;
  background-color: white;
  margin-left: 24px;
  margin-top: -5px;
}

.dropdown-item {
  cursor: pointer;
  padding: 5px;
  text-align: left;
  width: 100%;
}

.dropdown-item:hover {
  background-color: #f1f1f1;
}

.order-details {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;
}

.qty-label {
  position: relative;
  display: inline-block;
}

.lot-size {
  position: absolute;
  right: 2px;
  color: blue;
  visibility: hidden;
}

.qty-label:hover .lot-size {
  visibility: visible;
}

.qty-input {
  height: 40px;
  width: 120px;
  border-radius: 5px;
  font-size: 18px;
  padding-left: 40px;
}

.price-input,
.trigger-input,
.disclosed-input {
  height: 40px;
  width: 110px;
  border-radius: 5px;
  font-size: 18px;
  padding-left: 20px;
}

.duration {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.quantity-error {
  color: red;
  height: 0px;
}

.split-order {
  margin-top: -70px;
  margin-left: 500px;
  margin-bottom: 15px;
}

.account-selection {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;
}

.account-select {
  height: 25px;
  border-radius: 5px;
  width: 170px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
}

.buy-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 10px;
  border-radius: 5px;
  cursor: pointer;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 10px 10px;
  border-radius: 5px;
  cursor: pointer;
}

.tooltip {
  visibility: hidden;
  width: 220px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: -70%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

.qty-label:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

.tooltip-arrow {
  content: "";
  position: absolute;
  top: -21%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent black transparent;
}
.middle-main-container .main-table table.mini-table tbody tr td {
  padding: 2px 4px !important;
  line-height: 1.2 !important;
  height: auto !important;
  vertical-align: middle !important;
}

.middle-main-container .main-table table.mini-table tbody tr {
  height: auto !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
  border-bottom: none !important;
}

.middle-main-container .main-table table.mini-table tbody tr td span {
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.middle-main-container
  .main-table
  table.mini-table
  tbody
  tr
  td
  span[style*="display: inline-flex"] {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

.middle-main-container
  .main-table
  table.mini-table
  tbody
  tr
  td
  span[style*="display: inline-block"] {
  line-height: 1.2 !important;
  padding: 1px 4px !important;
}

.middle-main-container .main-table table.mini-table td {
  vertical-align: middle !important;
}

.middle-main-container .main-table table.mini-table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
}
