import React from "react";
import MarketIndex from "../MarketIndex";
import LeftNav from "../LeftNav";
import RightNav from "../RightNav";
import { ErrorContainer } from "../ErrorConsole";
import { TopNav } from "../TopNav";

/**
 * Optimized shared components using React.memo() to prevent unnecessary re-renders
 * These components will only re-render when their props actually change
 */

// Memoized MarketIndex - no props, so it will rarely re-render
export const OptimizedMarketIndex = React.memo(MarketIndex);

// Memoized LeftNav - no props, so it will rarely re-render
export const OptimizedLeftNav = React.memo(LeftNav);

// Memoized RightNav - no props, so it will rarely re-render
export const OptimizedRightNav = React.memo(RightNav);

// Memoized ErrorContainer with custom comparison function
export const OptimizedErrorContainer = React.memo(
  React.forwardRef((props, ref) => <ErrorContainer {...props} ref={ref} />),
  (prevProps, nextProps) => {
    // Custom comparison function to prevent unnecessary re-renders
    // Only re-render if msgs prop actually changes
    return (
      prevProps.msgs === nextProps.msgs &&
      prevProps.handleClearLogs === nextProps.handleClearLogs
    );
  }
);

// Memoized TopNav with custom comparison function
export const OptimizedTopNav = React.memo(TopNav, (prevProps, nextProps) => {
  // Custom comparison function for TopNav
  // Only re-render if essential props change
  return (
    prevProps.pageCols === nextProps.pageCols &&
    prevProps.colsSelectedAll === nextProps.colsSelectedAll &&
    prevProps.colVis === nextProps.colVis &&
    prevProps.rows?.length === nextProps.rows?.length &&
    JSON.stringify(prevProps.colVis) === JSON.stringify(nextProps.colVis)
  );
});

OptimizedMarketIndex.displayName = "OptimizedMarketIndex";
OptimizedLeftNav.displayName = "OptimizedLeftNav";
OptimizedRightNav.displayName = "OptimizedRightNav";
OptimizedErrorContainer.displayName = "OptimizedErrorContainer";
OptimizedTopNav.displayName = "OptimizedTopNav";
