import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from '@tailwindcss/vite'


export default defineConfig({
  plugins: [ react(), tailwindcss(), ],
  server: {
    port: 1313,
    host: "0.0.0.0",
    cors: true,
    proxy: {
      "/api": {
        target: "http://***************:80/",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
      "/ws": {
        target: "ws://***************:1818",
        ws: true,
      },
    },
  },
});
