import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import Modal from "react-modal";
import App from "./App.jsx";
import "./index.css";
import { Provider } from "react-redux";
import store from "./store/store.js";

Modal.setAppElement("#root");

ReactDOM.createRoot(document.getElementById("root")).render(
  <BrowserRouter>
    <Provider store={store}>
      <App />
    </Provider>
  </BrowserRouter>,
);
