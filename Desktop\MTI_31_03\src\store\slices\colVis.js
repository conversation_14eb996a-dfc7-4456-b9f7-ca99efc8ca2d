import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  userProfVis: {
    action: true,
    userId: true,
    manualExit: true,
    mtmAll: true,
    availableMargin: true,
    name: true,
    broker: true,
    apiKey: true,
    secretKey: true,
    historicalApi: true,
    qrCode: true,
    sqOffTime: true,
    autoLogin: true,
    password: true,
    maxProfit: true,
    maxLoss: true,
    profitLocking: true,
    qtyByExposure: true,
    qtyOnMaxLossPerTrade: true,
    maxLossPerTrade: true,
    maxOpenTrades: true,
    qtyMultiplier: true,
    mobile: true,
    email: true,
    net: true,
    marketOrders: true,
    enableNRMLSqoff: true,
    enableCNCSqoff: true,
    exitOrderType: true,
    twoFA: true,
    maxLossWait: true,
    tradingAuthorizationReq: true,
    commodityMargin: true,
    apiUserDetails: true,
    utilizedMargin: true,
  },
  strategiesVis: {
    Action: true, //32
    "Manual Exit": true,
    "Strategy Label": true,
    "P L": true,
    "Trade Size": true,
    "Duplicate Signal Prevention": true,
    "Open Time": true,
    "Close Time": true,
    "Sq Off Time": true,
    "Trading Account": true,
    "Max Profit": true,
    "Max Loss": true,
    "Max Loss Wait Time": true,
    "Profit Locking": true,
    "Market Orders": true,
    "Delay Between Users": true,
    "Unique ID Req for Order": true,
    "Cancel Previous Open Signal": true,
    "Stop Reverse": true,
    "Part Multi Exists": true,
    "Hold Sell Seconds": true,
    "Allowed Trades": true,
    "Entry Order Retry": true,
    "Entry Retry Count": true,
    "Entry Retry Wait Seconds": true,
    "Exit Order Retry": true,
    "Exit Retry Count": true,
    "Exit Retry Wait Seconds": true,
    "Exit Max Wait Seconds": true,
    "Sq Off Done": true,
    Delta: true,
    Theta: true,
    Vega: true,
  },
  portfolioVis: {
    Delete: true,
    Enabled: true,
    Status: true,
    "Portfolio Name": true,
    PNL: true,
    Symbol: true,
    "Execute/Sq Off": true,
    Edit: true,
    "Make Copy": true,
    "Mark As Completed": true,
    Reset: true,
    "Pay Off": true,
    Chat: true,
    "Re Execute": true,
    "Part Entry/Exit": true,
    "Current Value": true,
    "Value Per Lot": true,
    "Underlying LTP": true,
    "Positional Portfolio": true,
    Product: true,
    Strategy: true,
    "Entry Price": true,
    "Combined Premuim": true,
    "Per Lot Premuim": true,
    "Start Time": true,
    "End Time": true,
    "SqOff Time": true,
    "Range End Time": true,
    Delta: true,
    Theta: true,
    Vega: true,
    Remarks: true,
    Message: true,
  },
  orderFlowVis: {
    Action: true,
    "Client ID": true,
    "Stock Symbol": true,
    Exchange: true,
    Edit: true,
    "Order Time": true,
    "Trade ID": true,
    Transaction: true,
    "Avg Execution Price": true,
    "Order Size": true,
    "Execution Quantity": true,
    "Trade Type": true,
    "Product Type": true,
    Price: true,
    "Trigger Price": true,
    "Trigger Time": true,
    "Exchange Trade ID": true,
    Instrument: true,
    "Trade Duration": true,
    "Trade Status": true,
    "Display Name": true,
    "Status Message": true,
    Label: true,
  },
  positionsVis: {
    "User ID": true,
    Product: true,
    Exchange: true,
    Symbol: true,
    "Net Qty": true,
    LTP: true,
    "P&L": true,
    "P&L%": true,
    "Buy Qty": true,
    "Buy Avg Price": true,
    "Buy Value": true,
    "Sell Qty": true,
    "Sell Avg Price": true,
    "Sell Value": true,
    "Carry FWD Qty": true,
    "Realized Profit": true,
    "Unrealized Profit": true,
    "User Alias": true,
  },
  holdingsVis: {
    Action: true,
    Exchange: true,
    Symbol: true,
    "Avg Price": true,
    "Buy Value": true,
    LTP: true,
    "Current Value": true,
    "P&L%": true,
    "Collateral Qty": true,
    "T1 Qty": true,
    "Cns Sell  Quantity": true,
    "User ID": true,
    "User Alias": true,
  },
  ordermanagementVis: {
    Action: true,
    "User ID": true,
    "Source Symbol": true,
    "Request ID": true,
    Exchange: true,
    "Exchange Symbol": true,
    LTP: true,
    "P&L": true,
    Product: true,
    "Entry Order Type": true,
    "Entry Order ID": true,
    "Entry Time": true,
    "Entry Txn": true,
    "Entry Qty": true,
    "Entry Filled Qty": true,
    "Entry Exchange Time": true,
    "LTP#1": true,
    "LTP#2": true,
    "Entry Avg Price": true,
    "LTP#3": true,
    "Entry Status": true,
    "Exit Order ID": true,
    "Exit Time": true,
    "Exit Txn": true,
    "Exit Qty": true,
    "Exit Filled Qty": true,
    "LTP#4": true,
    "Exit Exchange Time": true,
    "Exit Avg Price": true,
    "Exit Status": true,
    Target: true,
    SL: true,
    "Break Even": true,
    "Signal Source": true,
    Strategy: true,
    "Signal Status": true,
    "Order Failed": true,
    "User Alies": true,
    Remarks: true,
    "Manual Exit": true,
  },
};

export const visSlice = createSlice({
  name: "vis",
  initialState,
  reducers: {
    setAllVis: (state, action) => {
      state.userProfVis = action.payload.userProfVis || [];
      state.strategiesVis = action.payload.strategiesVis || [];
      state.portfolioVis = action.payload.portfolioVis || [];
      state.orderFlowVis = action.payload.orderFlowVis || [];
      state.positionsVis = action.payload.positionsVis || [];
      state.holdingsVis = action.payload.holdingsVis || [];
      state.ordermanagementVis = action.payload.ordermanagementVis || [];
    },
  },
});

export const { setAllVis } = visSlice.actions;

export default visSlice.reducer;
