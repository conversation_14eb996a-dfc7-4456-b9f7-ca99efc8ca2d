import React from "react";

const AmiBrokerSection = ({
    isAmiBrokerEnabled,
    onSave,
    onCancel
}) => (
    <div className="amibroker-section" style={{
        padding: "20px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box",
        display: "flex",
        flexDirection: "column",
        gap: "20px"
    }}>

        <div style={{ marginBottom: "0" }}>
            <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <input
                    type="checkbox"
                    checked={isAmiBrokerEnabled}
                    style={{ width: "16px", height: "16px" }}
                />
                <span style={{ fontSize: "16px", fontWeight: "600" }}>Enable AmiBroker Integration</span>
            </label>
            <p style={{ fontSize: "13px", color: "#666", marginTop: "8px", marginLeft: "24px" }}>
                Connect to AmiBroker for advanced technical analysis and automated trading
            </p>
        </div>

        <div style={{ marginBottom: "50px" }}>
            <label>Ami Broker Installation Path:</label>
            <input
                type="file"
                value={""}
                style={{
                    width: "300px",
                    padding: "7px",
                    marginLeft: "50px",
                    marginTop: "5px",
                    border: "1px solid #ccc",
                }}
            />
            <button
                style={{
                    padding: "8px 15px",
                    backgroundColor: "#D8E1FF",
                    border: "1px solid #ccc",
                    cursor: "pointer",
                    marginLeft: "50px",
                    borderRadius: "12px",
                    width: "100px",
                    fontWeight: "bold",
                    color: "#000",
                }}
            >
                Search
            </button>
        </div>
        <div style={{ marginBottom: "20px" }}>
            <label>Select AmiBroker Database:</label>
            <select
                style={{
                    width: "300px",
                    padding: "7px",
                    marginTop: "5px",
                    marginLeft: "50px",
                }}
            >
                <option value="C:\\Program Files\\AmiBroker\\Data"></option>
            </select>
        </div>

        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "10px",
            marginTop: "0"
        }}>
            <button
                onClick={onSave}
                style={{
                    padding: "10px 20px",
                    background: "#4661bd",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "12px",
                    fontWeight: "600",
                    transition: "background 0.3s ease"
                }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Configure
            </button>
            <button
                onClick={onCancel}
                style={{
                    padding: "12px 25px",
                    background: "#fff",
                    color: "#666",
                    border: "1px solid #ddd",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "600",
                    transition: "all 0.3s ease"
                }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default AmiBrokerSection;
