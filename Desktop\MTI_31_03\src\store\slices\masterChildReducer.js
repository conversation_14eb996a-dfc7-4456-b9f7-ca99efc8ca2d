import { createSlice } from '@reduxjs/toolkit';

const masterChildSlice = createSlice({
    name: 'masterChild',
    initialState: {
        masterChildPnL: {}, // Store masterchild_pnl data
    },
    reducers: {
        setMasterChildPnL(state, action) {
            state.masterChildPnL = action.payload.masterChildPnL;
        },
    },
});

export const { setMasterChildPnL } = masterChildSlice.actions;
export default masterChildSlice.reducer;