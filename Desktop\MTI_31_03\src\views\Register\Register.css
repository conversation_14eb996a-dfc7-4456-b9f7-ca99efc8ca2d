* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.decoration-container {
  position: relative;
  width: 50%;
  height: 100vh; /* Full height of the viewport */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* Prevents SVGs from overflowing */
}

.background-svg {
  position: absolute;
  top: 0%;
  left: 23%;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: cover;
}

.illustration-svg {
  position: relative;
  z-index: 2;
  max-width: 80%;
  max-height: 80%;
  margin-left: 100px;
}

.right-container-register {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.right-container__box-register {
  width: 100%;
  max-width: 400px;
  padding: 25px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.right-container__h2-register {
  color: black;
  font-size: 22px;
  font-family: "Roboto";
  font-weight: 700;
  text-align: center;
}

.right-container__label-register {
  color: #333;
  font-size: 18px;
  font-family: "Roboto";
  font-weight: 500;
  word-wrap: break-word;
}

.right-container__input-register {
  width: 100%;
  height: 40px;
  border-radius: 10px;
  border: 1px #d9d9d9 solid;
  display: flex;
  align-items: center;
  font-size: 15px;
  font-family: "Roboto";
  font-weight: 500;
  outline: none;
  transition: border-color 0.3s;
}
.right-container__input-register-password {
  width: 100%;
  height: 40px;
  border-radius: 10px;
  border: 1px #d9d9d9 solid;
  display: flex;
  align-items: center;
  font-size: 15px;
  margin-top: -15px;
  font-family: "Roboto";
  font-weight: 500;
  outline: none;
  transition: border-color 0.3s;
}

.right-container__input-register:focus {
  border-color: #007bff;
}

.password-toggle {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
}

/* Error message styling */
.error-message {
  color: red;
  font-size: 12px;
  font-family: "Roboto";
  margin-top: 5px;
}

/* Checkbox container */
.checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.checkbox {
  margin-right: 8px;
}

.IAgreeToTheTermsPolicy {
  display: flex;
  align-items: center;
}

.IAgreeToTheTermsPolicy span {
  margin-right: 5px;
  font-family: "Roboto";
  font-size: 14px;
}

.btn-register {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  background-color: #4a6cf7;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 2px;
}

.btn-register:hover {
  background-color: #4357de;
}

.social-sign-in {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  gap: 10px;
}

.SignInWithGoogle,
.SignInWithApple {
  display: flex;
  align-items: center;
  justify-content: center;
  color: black;
  font-size: 14px;
  font-family: "Roboto";
  font-weight: 500;
  word-wrap: break-word;
  border: 1px solid #000;
  border-radius: 5px;
  width: 190px;
  height: 35px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.SignInWithGoogle:hover,
.SignInWithApple:hover {
  background-color: #f0f0f0; /* Light hover effect */
}

.right-container__bottom-text {
  font-size: 16px;
  font-family: "Roboto";
  font-weight: 500;
  text-align: center;
  margin-top: 20px;
}

.right-container__bottom-text .have-account {
  color: black;
}

.right-container__bottom-text .sign-in {
  color: #0f3dde;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .register-container {
    flex-direction: column;
  }

  .decoration-container {
    display: none; /* Hide the images on smaller screens, similar to Login page */
  }

  .right-container {
    width: 100%;
  }

  .right-container__box {
    max-width: 100%;
  }

  .social-sign-in {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .SignInWithGoogle,
  .SignInWithApple {
    width: 100%;
    max-width: 300px;
  }
}
