import React, { useState, useEffect, useRef } from "react";
import shape from "../assets/shape.png";
import pencil from "../assets/pencil.png";
import minus from "../assets/minus-sign.png";
import plus from "../assets/plus-sign.png";
import Draggable from "react-draggable";
import push from "../assets/push-pin.png";
import reuse from "../assets/reuse.png";
import disk from "../assets/diskette.png";
import recycle from "../assets/recycle-bin.png";
import { Oval } from "react-loader-spinner";
import { useSelector, useDispatch } from "react-redux";
import Cookies from "universal-cookie";
import { setBrokers } from "../store/slices/broker.js";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import { fetchWithAuth } from "../utils/api";
import useClickOutside from "../hooks/useClickOutside";


const cookies = new Cookies();

export const QuickTradePanel = ({
  isOpen,
  handleClose,
  colopen,
  toggleOpen,
  position,
  resetPosition,
  handleDrag,
}) => {

  const inputRef = useRef(null);
  const panelRef = useRef(null);
  const dispatch = useDispatch();

  // Close dropdown when clicking outside the input
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (inputRef.current && !inputRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Close panel when clicking outside
  useClickOutside(panelRef, () => {
    if (isOpen) {
      handleClose();
    }
  });

  const { strategies: Strategydata } = useSelector(
    (state) => state.strategyReducer,
  );

  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;

      const lastMsg = previousConsoleMsgs[0];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.startegy &&
        lastMsg.portfolio === Msg.porttfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [Msg, ...previousConsoleMsgs.slice(1)],
          }),
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [Msg, ...previousConsoleMsgs],
          }),
        );
      }
    });
  };


  const [optionsQTP, setOptionsQTP] = useState({
    exchange: "",
    stock_symbol: "",
    userIds: "",
    lots: "",
    variety: "",
    strategy_tag: "",
    portfolio_name: "",
    quantity: "",
    order_type: "",
    price: "",
  });

  const [symbolsLtp, setSymbolLTP] = useState(0);

  const [filteredData, setFilteredData] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);

  const [broker, setBroker] = useState(null);
  const [rowData, setRowData] = useState(null);
  const mainUser = cookies.get("USERNAME");


  const fetchAccountDetails = async () => {
    try {
      const response = await fetchWithAuth(`/api/get_user_data/${mainUser}`, {
        method: "GET",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Something went wrong. Please try again.");
      }

      const responseData = await response.json();


      const brokerData = responseData.broker_credentials.find(
        (row) => row.broker === "pseudo_account"
      );

      if (!brokerData) {
        throw new Error("Pseudo account not found.");
      }

      const newMargin = brokerData.available_balance ?? "0.00";
      const newUtilizedMargin = brokerData.utilized_margin ?? "0.00";

      const newFilledData = rows.map((account) => {
        if (account.userId === brokerData.broker_user_id) {
          return {
            ...account,
            availableMargin: newMargin,
            utilized_Margin: newUtilizedMargin,
          };
        }
        return account;
      });


      dispatch(
        setBrokers({
          brokers: newFilledData,
        })
      );
    } catch (error) {

    }
  };

  const { brokers: rows } = useSelector((state) => state.brokerReducer);
  useEffect(() => {
    if (!isOpen) {
      setplaceOrderOptionsQTPBtn(false);
      setplaceOrderOptionsQTPBtnSell(false);
      setOptionsQTP({
        exchange: "",
        stock_symbol: "",
        userIds: "",
        lots: "",
        variety: "",
        strategy_tag: "",
        portfolio_name: "",
        quantity: "",
        order_type: "",
        price: "",
      });
      setSymbolLTP(0);
    }
  }, [isOpen]);

  const [updateKey, setUpdateKey] = useState(0);

  const close = () => {
    setOptionsQTP({
      exchange: '',
      stock_symbol: '',
      lots: '',
      variety: '',
      strategy: '',
      userIds: '',
      portfolio_name: '',
      Option_Strategy: '',
    });
    handleClose();
    resetPosition();
    forceUpdate(); // Call forceUpdate after state change
  };

  const forceUpdate = () => {
    setUpdateKey(prev => prev + 1);
  };

  const [placeOrderOptionsQTPBtn, setplaceOrderOptionsQTPBtn] = useState(false);
  const [placeOrderOptionsQTPBtnSell, setplaceOrderOptionsQTPBtnSell] = useState(false);


  const handlePlaceOrderOptionsQTP = async (transactionType) => {
    try {
      const linkedList = Strategydata.filter(
        (strategie) => strategie.StrategyLabel === optionsQTP.strategy_tag,
      );
      if (transactionType === "BUY") {
        setplaceOrderOptionsQTPBtn(true);
      } else if (transactionType === "SELL") {
        setplaceOrderOptionsQTPBtnSell(true);
      }
      const currentTime = new Date();
      const currentHours = currentTime.getHours();
      const currentMinutes = currentTime.getMinutes();

      // if (
      //   !(
      //     (currentHours === 9 && currentMinutes >= 15) ||
      //     (currentHours > 9 && currentHours < 15) ||
      //     (currentHours === 15 && currentMinutes <= 30)
      //   )
      // ) {
      //   handleMsg({
      //     msg: `Order not placed as current time is outside the allowed time window.`,
      //     logType: "INFO",
      //     timestamp: `${new Date().toLocaleString()}`,
      //     color: "red",
      //   });
      //   if (transactionType === "BUY") {
      //     console.log('Setting BUY loading to false');
      //     setplaceOrderOptionsQTPBtn(false);
      //   } else if (transactionType === "SELL") {
      //     console.log('Setting SELL loading to false');
      //     setplaceOrderOptionsQTPBtnSell(false);
      //   }
      //   close();
      //   return;
      // }
      const mapedUserIds = linkedList[0].TradingAccount.split(", ");

      mapedUserIds.map(async (userId, index) => {
        const user = rows.find((user) => user.userId === userId);
        if (user && user.inputDisabled) {
          let apiUrl;
          if (user.broker === "fyers") {
            apiUrl = `/api/fyers_place_equity_order/fyers/${mainUser}/${user.userId}`;
          } else if (user.broker === "angelone") {
            apiUrl = `/api/angelone_place_equity_order/angelone/${mainUser}/${user.userId}`;
          } else if (user.broker === "flattrade") {
            apiUrl = `/api/flattrade_equity_place_order/flattrade/${mainUser}/${user.userId}`;
          } else if (user.broker === "pseudo_account") {
            apiUrl = `/api/pseudo_equity_place_order/pseudo/${mainUser}/${user.userId}`;
          }

          const requestData = {
            symbol: optionsQTP.stock_symbol,
            quantity: Number(optionsQTP.quantity),
            strategy: optionsQTP.strategy_tag,
            transaction_type: transactionType,
            product_type: optionsQTP.variety ? optionsQTP.variety : "NRML",
            order_type: optionsQTP.order_type
              ? optionsQTP.order_type
              : "MARKET",
            limitPrice: Number(optionsQTP.price ? optionsQTP.price : 0),
          };

          const res = await fetchWithAuth(apiUrl, {
            method: "POST",
            body: JSON.stringify(requestData),
          });
          try {
            if (res.ok) {
              const orderPlaceoptionsQTPRes = await res.json();
              fetchAccountDetails();
              if (transactionType === "BUY") {
                setplaceOrderOptionsQTPBtn(false);
              } else if (transactionType === "SELL") {
                setplaceOrderOptionsQTPBtnSell(false);
              }
              close(true);
              handleMsg({
                msg: orderPlaceoptionsQTPRes.message,
                logType: "TRADING",
                timestamp: `${new Date().toLocaleString()}`,
                user: user.userId,
                strategy: optionsQTP.strategy_tag,
              });
            } else {
              if (transactionType === "BUY") {
                setplaceOrderOptionsQTPBtn(false);
              } else if (transactionType === "SELL") {
                setplaceOrderOptionsQTPBtnSell(false);
              }
              const orderPlaceoptionsQTPRes = await res.json();
              console.log(
                orderPlaceoptionsQTPRes,
                "orderPlaceoptionsQTPRes err",
              );

              // handleMsg({
              //   msg: orderPlaceoptionsQTPRes[ 0 ].message,
              //   logType: "MESSAGE",
              //   timestamp: `${new Date().toLocaleString()}`,
              //   user: user.userId,
              //   strategy: optionsQTP.strategy_tag,
              // });
            }
          } catch (e) {
            if (transactionType === "BUY") {
              setplaceOrderOptionsQTPBtn(false);
            } else if (transactionType === "SELL") {
              setplaceOrderOptionsQTPBtnSell(false);
            }
            close(true);
            handleMsg({
              msg: e.message,
              logType: "ERROR",
              timestamp: `${new Date().toLocaleString()}`,
              user: user.userId,
              strategy: optionsQTP.strategy_tag,
            });
          }
        } else {
          handleMsg({
            msg: `Login to ${user.userId} to place an order in this account.`,
            logType: "WARNING",
            timestamp: `${new Date().toLocaleString()}`,
            user: user.userId,
            strategy: optionsQTP.strategy_tag,
          });

          if (
            optionsQTP.userIds.length === 1 ||
            index === optionsQTP.userIds.length - 1
          ) {
            if (transactionType === "BUY") {
              setplaceOrderOptionsQTPBtn(false);
            } else if (transactionType === "SELL") {
              setplaceOrderOptionsQTPBtnSell(false);
            }
            close(true);
          }
        }
      });
    } catch (error) {
    }
  };
  const [symbols, setsymbols] = useState([]);

  useEffect(() => {
    if (isOpen) {
      getSymbols();
    }
  }, [isOpen]);


  const getSymbols = async () => {
    try {
      const mappedUserIds = rows.filter((row) => row.inputDisabled);
      // if (mappedUserIds.length === 0) {
      //   handleMsg({
      //     msg: "Please login at least one broker account",
      //     logType: "WARNING",
      //     timestamp: `${new Date().toLocaleString()}`,
      //   });
      //   setTimeout(close, 500);

      //   return;
      // }
      const brokers = {};
      for (let index = 0; index < mappedUserIds.length; index++) {
        const rowData = mappedUserIds[index];
        if (rowData.inputDisabled) {
          if (!brokers[rowData.broker]) {
            brokers[rowData.broker] = rowData;
          }
        }
      }

      for (const [broker, rowData] of Object.entries(brokers)) {
        let endpoint = "";
        if (broker === "angelone" || broker === "flattrade") {
          endpoint = `/api/angelone_equity_symbols/${mainUser}/${rowData.userId}`;
        } else if (broker === "fyers") {
          endpoint = `/api/fyers_equity_symbols/${mainUser}/${rowData.userId}`;
        }
        if (endpoint) {
          const response = await fetchWithAuth(endpoint, {
            method: "POST",
            body: JSON.stringify({
              exchange: "NSE",
            }),
          });

          if (response.ok) {
            const data = await response.json();
            if (broker === "angelone" || broker === "flattrade") {
              if (
                data.angelone_bse_symbols_data !== undefined &&
                Array.isArray(data.angelone_bse_symbols_data) &&
                data.angelone_bse_symbols_data.length > 0
              ) {
                setsymbols(data.angelone_bse_symbols_data);
              } else {
                setsymbols(data.angelone_nse_equity_symbols_data);
              }
            } else if (broker === "fyers") {
              if (data.fyers_bse_equity_symbols_data != null) {
                setsymbols(data.fyers_bse_equity_symbols_data);
              } else {
                setsymbols(data.fyers_nse_equity_symbols_data);
              }
            }
          }
          setBroker(broker);
          setRowData(rowData);
        }
      }
    } catch (error) {

      setFilteredData([]);
      setShowDropdown(false);
    }
  };

  const { placeOrderStart } = useSelector(
    (state) => state.placeOrderStartReducer,
  );

  useEffect(() => {
    if (optionsQTP.exchange !== "") {
      setOptionsQTP((prev) => ({
        ...prev,
        stock_symbol: "",
        userIds: "",
        lots: "",
        variety: "",
        strategy_tag: "",
        portfolio_name: "",
        quantity: "",
        order_type: "",
        price: "",
      }));
      setSymbolLTP(0);
    }
  }, [optionsQTP.exchange]);

  const filterData = async () => {
    const searchQuery = optionsQTP.stock_symbol.toLowerCase();
    if (searchQuery !== "") {
      try {
        const filteredData = symbols
          .filter(
            (item) =>
              item.Symbol.toLowerCase().includes(searchQuery) &&
              item.Symbol.trim() !== optionsQTP.stock_symbol, // Exclude the selected item
          )
          .map((item) => item.Symbol.trim());

        setFilteredData(filteredData);
        setShowDropdown(true);
      } catch (error) {

        setFilteredData([]);
        setShowDropdown(false);
      }
    } else {
      setFilteredData([]);
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    filterData();
  }, [optionsQTP.stock_symbol]);

  const [selectedItem, setSelectedItem] = useState(null);


  const handleSelectItem = (item) => {
    setSelectedItem(item);
    setOptionsQTP((prev) => ({
      ...prev,
      stock_symbol: item,
    }));

    setShowDropdown(false);
    getSymbolLTP(item);
  };
  const getSymbolLTP = async (item) => {
    try {
      let endpoint = "";
      if (broker === "angelone" || broker === "flattrade") {
        endpoint = `/api/get_equity_price/${mainUser}`;
      } else if (broker === "fyers") {
        endpoint = `/api/get_fyers_equity_price_details/${mainUser}/${rowData.userId}`;
      } else {
        throw new Error("Invalid broker specified");
      }


      const response = await fetchWithAuth(endpoint, {
        method: "POST",
        body: JSON.stringify({
          symbol: item,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSymbolLTP(data.ltp);
      } else {
        setSymbolLTP(0);
      }
    } catch (error) {

      setFilteredData([]);
      setShowDropdown(false);
    }
  };
  const refreshLtp = () => {
    getSymbolLTP(selectedItem)
  }

  const generateDropdownItems = (item) => {
    const input = optionsQTP.stock_symbol.toLowerCase();
    const startIndex = item.toLowerCase().indexOf(input);
    const endIndex = startIndex + input.length;
    return (
      <>
        {item.slice(0, startIndex)}
        <span style={{ backgroundColor: "yellow" }}>
          {item.slice(startIndex, endIndex)}
        </span>
        {item.slice(endIndex)}
      </>
    );
  };
  useEffect(() => {
    setOptionsQTP((prev) => ({
      ...prev,
      exchange: "NSE",
    }));
  }, []);
  const [validationErrors, setValidationErrors] = useState({
    strategy_tag: false,
    quantity: false,
  });

  return (
    <div style={{ zIndex: "1000" }}>
      <Draggable
        handle=".handle"
        defaultPosition={{ x: 0, y: 0 }}
        cancel=".details, select, input"
        position={position}
        onDrag={handleDrag}
      >
        <div
          key={updateKey}
          className="your-modal-button"
          style={{
            display: isOpen ? "block" : "none",
            border: "none",
            top: "10%",
            left: "25%",
            overlay: {
              backgroundColor: "visible",
              zIndex: 1000,
              pointerEvents: "none",
            },
            content: {
              pointerEvents: "auto",
            },
          }}
        >
          <div ref={panelRef} className="containerin handle">
            <div className="title"></div>
            <h2
              style={{
                marginTop: "5px",
                color: "#4661bd",
                fontFamily: "Roboto",
                fontSize: "24px",
              }}
            >
              Stock Trading Panel
            </h2>
            <div className="content">
              <div className="user-details">
                <div className="input-box">
                  <select
                    className="one"
                    defaultValue="NSE"
                    style={{
                      border: "0.5px solid #cacaca",
                      color: "GrayText",
                      paddingLeft: "7px",
                    }}
                    onChange={(e) => {
                      setOptionsQTP((prev) => ({
                        ...prev,
                        exchange: e.target.value,
                      }));
                    }}
                  >
                    <option style={{ color: "black" }} value="BFO">
                      BFO
                    </option>
                    <option style={{ color: "black" }} value="NFO">
                      NFO
                    </option>
                    <option style={{ color: "black" }} value="MCX">
                      MCX
                    </option>
                    <option style={{ color: "black" }} value="NSE">
                      NSE
                    </option>
                    <option style={{ color: "black" }} value="BSE">
                      BSE
                    </option>
                  </select>
                </div>
                <div type="text" className="two" style={{ marginTop: "-55px", display: "flex", flexDirection: "row" }}>
                  <img
                    src={pencil}
                    style={{
                      padding: "5px",
                      height: "38px",
                      width: "36px",
                    }}
                    alt="profile-pic"
                  />
                  <img
                    style={{
                      cursor: "pointer",
                      padding: "5px",
                      height: "38px",
                      width: "36px",
                    }}
                    src={colopen ? plus : minus}
                    onClick={toggleOpen}
                    id="colopen"
                    alt="profile-pic"
                  />
                  <img
                    src={push}
                    style={{
                      padding: "5px",
                      height: "38px",
                      width: "36px",
                                            cursor: "pointer",
                    }}
                    alt="profile-pic"
                  />
                  <img
                    src={shape}
                    style={{
                      padding: "5px",
                      height: "38px",
                      width: "36px",
                      cursor: "pointer",
                    }} onClick={close}
                    alt="profile-pic"
                  />
                </div>
                <div className="input-box5" ref={inputRef}>
                  <input
                    type="text"
                    className="details"
                    style={{
                      color: "black",
                      textTransform: "uppercase",
                      marginBottom: "11px",
                      left: "4px",
                    }}
                    value={optionsQTP.stock_symbol}
                    onChange={(e) => {
                      setOptionsQTP((prev) => ({
                        ...prev,
                        stock_symbol: e.target.value.toUpperCase(),
                      }));
                      setShowDropdown(false);
                    }}
                  // disabled={!optionsQTP.exchange}
                  />
                  {showDropdown && (
                    <div
                      className="dropdown"
                      style={{
                        position: "absolute",
                        top: "175px",
                        left: 16,
                        width: "100PX",
                        zIndex: 0,
                      }}
                    >
                      <div className="dropdown-content">
                        {filteredData.slice(0, 5).map((item, index) => (
                          <div
                            key={index}
                            className="dropdown-item"
                            onClick={() => handleSelectItem(item)}
                          >
                            {generateDropdownItems(item)}
                          </div>
                        ))}
                        {filteredData.length > 5 && (
                          <div className="scroll-bar">
                            {filteredData.slice(5).map((item, index) => (
                              <div
                                key={index}
                                className="dropdown-item"
                                onClick={() => {
                                  handleSelectItem(item);
                                  setShowDropdown(false);
                                }}
                              >
                                {generateDropdownItems(item)}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                <div
                  className="input-box"
                  style={{ alignItems: "top", marginTop: "-50px" }}
                >
                  <div type="text" className="four" style={{  display: "flex", flexDirection: "row", marginTop:"10px"}}>
                    <img
                      src={reuse}
                      style={{
                        padding: "5px",
                        marginBottom: "20px",
                        height: "43px",
                        width: "40px",
                      }}
                      onClick={refreshLtp}
                      alt="profile-pic"
                    />
                    <img
                      src={disk}
                      style={{
                        padding: "9px",
                        marginBottom: "20px",
                        height: "43px",
                        width: "40px",
                      }}
                      alt="profile-pic"
                    />
                    <img
                      src={recycle}
                      style={{
                        paddingTop: "5px",
                        marginBottom: "25px",
                        width: "40px",
                      }}
                      alt="profile-pic"
                    />
                  </div>
                </div>
                <div className="input-box">
                  <select
                    className="five"
                    style={{
                      border: validationErrors.strategy_tag
                        ? "2px solid red"
                        : "0.5px solid #cacaca",
                      color: "GrayText",
                    }}
                    onChange={(e) => {
                      setOptionsQTP((prev) => ({
                        ...prev,
                        strategy_tag: e.target.value,
                      }));
                      setValidationErrors((prev) => ({ ...prev, strategy_tag: false }));
                    }}
                  >
                    <option disabled selected>
                      Strategy
                    </option>
                    {Strategydata.filter((row) => row.enabled)
                      .map((row, index) => (
                        <option key={index} value={row.StrategyLabel}>
                          {row.StrategyLabel}
                        </option>
                      ))}
                  </select>
                </div>
                <div className="input-box">
                  <span
                    style={{
                      padding: "5px",
                      marginLeft: "3px",
                      fontSize: "20px",
                      fontWeight: "400",
                      color: "black",
                      fontFamily: "Roboto",
                    }}
                  >
                    Qty
                  </span>
                  <input
                    onChange={(e) => {
                      setOptionsQTP((prev) => ({
                        ...prev,
                        quantity: e.target.value,
                      }));
                      setValidationErrors((prev) => ({ ...prev, quantity: false }));
                    }}
                    value={optionsQTP.quantity}
                    type="number"
                    className="six"
                    style={{
                      border: validationErrors.quantity
                        ? "2px solid red"
                        : "0.5px solid #cacaca",
                    }}
                  />
                </div>
              </div>

              <div
                className="gender-details"
                id="gender-details"
                style={{
                  border: "1px solid #cacaca",
                  margin: "5px",
                  display: "none",
                  padding: "10px",
                }}
              >
                <span className="gender-title">Optional Parameters</span>

                <div className="user-details" style={{ margin: "5px" }}>
                  <div className="input-box">
                    <select
                      className="fives"
                      style={{
                        border: "0.5px solid #cacaca",
                        color: "GrayText",
                      }}
                      onChange={(e) => {
                        setOptionsQTP((prev) => ({
                          ...prev,
                          variety: e.target.value,
                        }));
                      }}
                    >
                      <option disabled selected>
                        Product
                      </option>
                      <option style={{ color: "black" }} value={"NRML"}>
                        NRML
                      </option>
                      <option style={{ color: "black" }} value={"MIS"}>
                        MIS
                      </option>
                    </select>
                  </div>
                  <div className="input-box">
                    <select
                      className="fives"
                      style={{
                        border: "0.5px solid #cacaca",
                        color: "GrayText",
                      }}
                      onChange={(e) => {
                        setOptionsQTP((prev) => ({
                          ...prev,
                          order_type: e.target.value,
                        }));
                      }}
                    >
                      <option disabled selected>
                        Order Type
                      </option>
                      <option style={{ color: "black" }} value={"MARKET"}>
                        MARKET
                      </option>
                      <option style={{ color: "black" }} value={"LIMIT"}>
                        LIMIT
                      </option>
                    </select>
                  </div>
                  <div className="input-box">
                    <span className="details">Price</span>
                    <input
                      type="number"
                      style={{ width: "65%", WebkitAppearance: "none" }}
                      value={symbolsLtp}
                      readOnly={true}
                    />
                  </div>
                  <div className="input-box">
                    <span className="details" style={{ marginLeft: "-85px" }}>
                      Trigger
                    </span>
                    <input
                      style={{ marginLeft: "-43%", width: "65%" }}
                      type="number"
                      defaultValue={0}
                      onChange={(e) => {
                        setOptionsQTP((prev) => ({
                          ...prev,
                          price: e.target.value,
                        }));
                      }}
                      disabled={optionsQTP.order_type === "MARKET"}
                    />
                  </div>
                </div>

                <div className="user-details-bottom">
                  <div className="input-box">
                    <span className="details">Target</span>
                    <input type="number" defaultValue="0" />
                  </div>
                  <div className="input-box">
                    <span className="details">S</span>
                    <input type="number" defaultValue="0" />
                  </div>
                  <div className="input-box">
                    <span className="details">SL Trail</span>
                    <input type="number" defaultValue="0" />
                  </div>
                </div>
              </div>
              <div className="user-details-button">
                <div className="input-box">
                  {!placeOrderOptionsQTPBtn ? (
                    <button
                      style={{ background: "#618F00" }}
                      onClick={() => {
                        const errors = {};
                        if (!optionsQTP.strategy_tag) errors.strategy_tag = true;
                        if (!optionsQTP.quantity) errors.quantity = true;

                        if (Object.keys(errors).length > 0) {
                          setValidationErrors(errors);
                          handleMsg({
                            msg: "Please fill all mandatory fields before placing an order.",
                            logType: "ERROR",
                            timestamp: `${new Date().toLocaleString()}`,
                          });
                          return;
                        }

                        if (placeOrderStart) {
                          setplaceOrderOptionsQTPBtn(true);
                          handlePlaceOrderOptionsQTP("BUY");
                        } else {
                          close(false);
                          handleMsg({
                            msg: "To place an Order, Start the Trading.",
                            logType: "WARNING",
                            timestamp: `${new Date().toLocaleString()}`,
                          });
                        }
                      }}
                    >
                      LE
                    </button>
                  ) : (
                    <button
                      type="button"
                      style={{ background: "#618F00", cursor: "default" }}
                    >
                      <Oval
                        height="20"
                        width="255"
                        color="white"
                        strokeWidth={3}
                      />
                    </button>
                  )}
                </div>
                <div className="input-box">
                  <button
                    style={{
                      background: "#81430B",
                      color: "white",
                      cursor: "not-allowed",
                      opacity: 0.6,
                    }}
                    disabled
                  >
                    LX
                  </button>
                </div>
                <div className="input-box">
                  <div className="check">
                    <input
                      type="checkbox"
                      style={{ opacity: "1", accentColor: "green" }}
                    />
                    <label
                      htmlFor="legSlCheckbox2"
                      style={{
                        cursor: "pointer",
                        border: "2px solid green",
                        marginLeft: "-20.5px",
                        height: "21.3px",
                        width: "21.9px",
                        marginTop: "-25px",
                      }}
                    ></label>
                  </div>
                </div>
                <div className="input-box">
                  {!placeOrderOptionsQTPBtnSell ? (
                    <button
                      onClick={() => {
                        const errors = {};

                        if (!optionsQTP.strategy_tag) {
                          errors.strategy_tag = true;
                        }
                        if (!optionsQTP.quantity) {
                          errors.quantity = true;
                        }

                        if (Object.keys(errors).length > 0) {
                          setValidationErrors(errors); // Update state to show red borders
                          handleMsg({
                            msg: "Please select a Strategy and enter Quantity before placing an order.",
                            logType: "ERROR",
                            timestamp: `${new Date().toLocaleString()}`,
                          });
                          return;
                        }

                        if (placeOrderStart) {
                          handlePlaceOrderOptionsQTP("SELL");
                          setplaceOrderOptionsQTPBtnSell(true);
                        } else {
                          handleMsg({
                            msg: "To place an Order, Start the Trading.",
                            logType: "WARNING",
                            timestamp: `${new Date().toLocaleString()}`,
                          });
                          close(false);
                        }

                      }}
                      style={{ background: "#890000" }}
                    >
                      SE
                    </button>
                  ) : (
                    <button
                      type="button"
                      style={{ background: "#890000", cursor: "default" }}
                    >
                      <Oval
                        height="20"
                        width="255"
                        color="white"
                        strokeWidth={3}
                      />
                    </button>
                  )}
                </div>
                <div className="input-box">
                  <button
                    style={{
                      background: "#81430B",
                      color: "white",
                      cursor: "not-allowed",
                      opacity: 0.6,
                    }}
                    disabled
                  >
                    SX
                  </button>
                </div>

              </div>

              <div className="user-details-button">
                <p
                  style={{
                    color: "blue",
                    fontSize: "19px",
                    fontFamily: "Roboto",
                    padding: "10px",
                    display: "none",
                  }}
                  className="sl"
                >
                  Target, SL in Points. For Percentage, Tick Value in %
                </p>
              </div>
            </div>
          </div>
        </div>
      </Draggable>
    </div>
  );
};
