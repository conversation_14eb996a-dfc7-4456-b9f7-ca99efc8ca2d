import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  profileImage: null,
  isLoading: false,
  lastFetched: null,
};

export const profileImageSlice = createSlice({
  name: "profileImage",
  initialState,
  reducers: {
    setProfileImage: (state, action) => {
      state.profileImage = action.payload.profileImage;
      state.lastFetched = Date.now();
    },
    setProfileImageLoading: (state, action) => {
      state.isLoading = action.payload.isLoading;
    },
    clearProfileImage: (state) => {
      state.profileImage = null;
      state.lastFetched = null;
    },
  },
});

export const { setProfileImage, setProfileImageLoading, clearProfileImage } = profileImageSlice.actions;

export default profileImageSlice.reducer;
