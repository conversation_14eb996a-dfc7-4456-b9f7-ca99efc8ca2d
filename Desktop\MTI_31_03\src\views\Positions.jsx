import React, { useState, useRef, useEffect, useMemo } from "react";
import "../styles.css";
import {
  OptimizedMarketIndex,
  OptimizedTopNav,
  OptimizedErrorContainer,
  OptimizedLeftNav,
  OptimizedRightNav,
} from "../components/Layout/OptimizedComponenets";
import { useSelector, useDispatch } from "react-redux";
import { setAllSeq, setAllVis } from "../store/slices";
import useClickOutside from "../hooks/useClickOutside";
import TableHeaderWithFilter from "../components/TableHeaderWithFilter";
import filterIcon from "../assets/newFilter.png";

const styles = `
  .middle-main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .main-table {
    flex: 1;
    overflow: auto;
    height: calc(92vh - 100px);
    position: relative;
  }
  .orderflowtable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }
  .orderflowtable thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #D8E1FF;
  }
  .orderflowtable th {
    font-size: 15px;
    padding: 4px 3px;
    text-align: center;
    border-bottom: 1px solid #ddd;
    white-space: normal;
    vertical-align: middle;
    height: auto;
    line-height: 1.1;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    min-width: 70px; /* Increased minimum width for better appearance */
    word-break: break-word;
    hyphens: auto;
  }
  .orderflowtable td {
    text-align: center;
    vertical-align: middle;
  }
  .orderflowtable tbody tr:nth-child(even),
  .orderflowtable tbody tr:nth-child(even) input,
  .orderflowtable tbody tr:nth-child(even) select {
    background-color: #E8E6E6;
  }
  .orderflowtable tbody tr:nth-child(odd),
  .orderflowtable tbody tr:nth-child(odd) input,
  .orderflowtable tbody tr:nth-child(odd) select {
    background-color: #FFFFFF;
  }
  .filter-icon {
    margin-left: 2px;
    cursor: pointer;
    font-size: 16px;
    vertical-align: middle;
  }
  .tooltip-container {
    position: relative;
    display: inline-block;
    margin: 0 2px;
  }
`;

function Positions() {
  const errorContainerRef = useRef(null);
  const dispatch = useDispatch();
  const { collapsed } = useSelector((state) => state.collapseReducer);
  const [ msgs, setMsgs ] = useState([]);
  const handleClearLogs = () => {
    if (msgs.length === 0) return;
    setMsgs([]);
  };

  const allSeqState = useSelector((state) => state.allSeqReducer);
  const allVisState = useSelector((state) => state.allVisReducer);

  const positionCols = [
    // "Action",
    "User ID",
    "Product",
    "Exchange",
    "Symbol",
    "Net Qty",
    "LTP",
    "P&L",
    "P&L%",
    "Buy Qty",
    "Buy Avg Price",
    "Buy Value",
    "Sell Qty",
    "Sell Avg Price",
    "Sell Value",
    "Carry FWD Qty",
    "Realized Profit",
    "Unrealized Profit",
    "User Alias",
  ];

  const [ PositionColVis, setPositionColVis ] = useState(() => {
    const initialVisibility = {};
    positionCols.forEach(col => {
      initialVisibility[ col ] = true;
    });
    return initialVisibility;
  });

  const [ positionColsSelectedALL, setpositionColsSelectedALL ] = useState(false);
  const [ positionsSeq, setpositionsSeq ] = useState(allSeqState.positionsSeq);


  const positionColSelectALL = () => {
    const newValue = !positionColsSelectedALL;
    setpositionColsSelectedALL(newValue);
    const updatedVisibility = {};
    positionCols.forEach(col => {
      updatedVisibility[ col ] = !newValue;
    });

    setPositionColVis(updatedVisibility);

    if (!newValue) {
      setpositionsSeq(positionCols);
    } else {
      setpositionsSeq([]);
    }
  };

  useEffect(() => {
    dispatch(setAllVis({ ...allVisState, PositionColVis }));
  }, [ PositionColVis ]);

  useEffect(() => {
    dispatch(setAllSeq({ ...allSeqState, PositionSeq: positionsSeq }));
  }, [ positionsSeq ]);

  const { positions: data } = useSelector((state) => state.positionReducer);

  const calculatePnLPercentage = (pos) => {
    const isLong = parseFloat(pos[ "Buy Qty" ]) > 0;
    const isShort = parseFloat(pos[ "Sell Qty" ]) > 0;
    let entryPrice, exitPrice;
    if (isLong) {
      entryPrice = parseFloat(pos[ "Buy Avg Price" ]) || 0;
      exitPrice = parseFloat(pos[ "Sell Avg Price" ] || pos[ "LTP" ]) || 0;
    } else if (isShort) {
      entryPrice = parseFloat(pos[ "Sell Avg Price" ]) || 0;
      exitPrice = parseFloat(pos[ "Buy Avg Price" ] || pos[ "LTP" ]) || 0;
    } else {
      return { value: "0%", color: "black" };
    }

    if (entryPrice === 0 && exitPrice === 0) {
      return { value: "", color: "black" };
    }
    if (entryPrice === 0) {
      return { value: "", color: "black" };
    }

    const pnlPercentage = isLong
      ? ((exitPrice - entryPrice) / entryPrice) * 100
      : ((entryPrice - exitPrice) / entryPrice) * 100;

    const formattedValue = `${pnlPercentage.toFixed(2)}%`;
    return {
      value: formattedValue,
    };
  };

  const mappedPositions = useMemo(() => {
    return data.map((position) => {
      const pnl = calculatePnLPercentage({
        "Buy Qty": position.buyQty,
        "Sell Qty": position.sellQty,
        "Buy Avg Price": position.buyAvg,
        "Sell Avg Price": position.sellAvg,
        LTP: position.LTP,
      });

      return {
        // Action: "",
        "User ID": position.broker_user_id,
        Product: position.productType,
        Exchange: position.exchange,
        Symbol: position.symbol,
        "Net Qty": position.netQty,
        LTP: position.LTP,
        "P&L": position.PNL,
        "P&L%": pnl.value,
        "Buy Qty": position.buyQty,
        "Buy Avg Price": position.buyAvg,
        "Buy Value": position.buyVal?.toFixed(2),
        "Sell Qty": position.sellQty,
        "Sell Avg Price": position.sellAvg,
        "Sell Value": position.sellVal?.toFixed(2),
        "Carry FWD Qty": "",
        "Realized Profit": position.realized_profit,
        "Unrealized Profit": position.unrealized_profit,
        "User Alias": "",
      };
    });
  }, [ data ]);

  const filterPopupRef = useRef(null);
  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  const [ filters, setFilters ] = useState({});
  const [ filterPopup, setFilterPopup ] = useState(null);
  const [ tempFilters, setTempFilters ] = useState({});
  const [ filteredData, setFilteredData ] = useState(mappedPositions);
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 });

  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      const filteredDataResult = mappedPositions.filter((row) =>
        Object.keys(filters).every((col) =>
          filters[ col ]?.length > 0 ? filters[ col ].includes(row[ col ]) : true
        )
      );
      setFilteredData(filteredDataResult);
    } else {
      setFilteredData(mappedPositions);
    }
  }, [ mappedPositions, filters ]);

  const handleApplyFilter = () => {
    const newFilters = { ...filters, [ filterPopup ]: tempFilters[ filterPopup ] || [] };
    setFilters(newFilters);
    setFilterPopup(null);
  };

  const getDynamicUniqueValues = (column) => {
    const isFirstFilter = Object.keys(filters).length === 0 ||
      (Object.keys(filters).length === 1 && filters[ column ]);
    const sourceData = isFirstFilter ? mappedPositions : filteredData;

    return Array.from(new Set(sourceData.map((row) => row[ column ] || "")));
  };

  const handleFilterToggle = (column, event) => {
    const { top, left, height } = event.target.getBoundingClientRect();
    setFilterPopup(filterPopup === column ? null : column);
    setPopupPosition({ top: top + height, left });
    setTempFilters(filters);
  };

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) => selectedOptions.includes(opt));

    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [ column ]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [ column ]: [ ...currentOptions ] }));
    }
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  const hasColumnData = (row, column) => {
    return row[ column ] !== undefined && row[ column ] !== "";
  };

  return (
    <div>
      <style>{styles}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav
            pageCols={positionCols}
            colVis={PositionColVis}
            setColVis={setPositionColVis}
            colsSelectedAll={positionColsSelectedALL}
            setColsSelectedALL={setpositionColsSelectedALL}
            selectAll={positionColSelectALL}
            setSeq={setpositionsSeq}
          />
          <div className="main-table">
            <table className="orderflowtable">
              <thead>
                <tr>
                  {positionsSeq.map((column) => {
                    const hasFilter = filters[ column ] && filters[ column ].length > 0;
                    const selectedItems = filters[ column ]?.length || 0;

                    return (
                      <th
                        key={column}
                        style={{
                          fontSize: "13px",
                          padding: "4px 3px",
                          textAlign: "center",
                          backgroundColor: hasFilter ? "#f0f7ff" : "inherit",
                          borderBottom: hasFilter ? "2px solid #1976d2" : "inherit",
                          height: "auto",
                          minWidth: (() => {
                            // Base width calculation
                            let baseWidth = column === "User ID" || column === "Product" ? 110 : column === "Exchange" ? 115
                              : column === "Net Qty" || column === "Buy Qty" || column === "Sell Qty" ? 95 : column === "LTP" ? 85
                                : column === "P&L" ? 85 : column === "P&L%" ? 95
                                  : 130;

                            // Add extra width for filter badge when filter is applied
                            const filterExtraWidth = hasFilter ? 30 : 0;

                            return `${baseWidth + filterExtraWidth}px`;
                          })(),
                          wordBreak: "break-word",
                          whiteSpace: "normal"
                        }}
                      >
                        <div style={{
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "center",
                          position: "relative",
                          padding: "0",
                          margin: "0",
                          gap: "4px",
                          width: "100%"
                        }}>
                          <TableHeaderWithFilter
                            col={column}
                            columnDisplayNames={{}}
                            hasFilter={hasFilter}
                            selectedItems={selectedItems}
                            handleFilterToggle={handleFilterToggle}
                            filterIcon={filterIcon}
                          />
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody className="tabletbody">
                {filteredData.map((pos, rowIndex) => (
                  <tr key={rowIndex}>
                    {positionsSeq.map((column) =>
                      hasColumnData(pos, column) ? (
                        <td
                          style={{
                            padding: "4px",
                            width: "auto",
                            color:
                              column === "P&L" && pos[ column ] > 0
                                ? "green"
                                : column === "P&L" && pos[ column ] < 0
                                  ? "red"
                                  : column === "P&L%" && parseFloat(pos[ column ]) > 0
                                    ? "green"
                                    : column === "P&L%" && parseFloat(pos[ column ]) < 0
                                      ? "red"
                                      : "black",
                            verticalAlign: "middle"
                          }}
                          key={column}
                        >
                          {pos[ column ]}
                        </td>
                      ) : (
                        <td
                          style={{ padding: "4px", width: "auto", verticalAlign: "middle" }}
                          key={column}
                        ></td>
                      )
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="add_collapse">
            <button className="hiddenbutton button">Add</button>
            <button
              style={{ zIndex: "0" }}
              onClick={() => {
                errorContainerRef.current.toggleCollapse();
              }}
              className="button"
              id="collapse"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>
          <OptimizedErrorContainer ref={errorContainerRef} handleClearLogs={handleClearLogs} />
        </div>
        <OptimizedRightNav />
      </div>
      {filterPopup && filterPopup !== "action" && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "3px 0 3px 3px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            zIndex: 1000,
            minWidth: "150px",
            maxWidth: "200px"
          }}
        >
          <div style={{ paddingRight: "1px" }}>
            <div style={{
              borderBottom: "1px solid #e0e0e0",
              paddingBottom: "1px",
              marginBottom: "1px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            }}>
              <span style={{ fontWeight: "bold", color: "#1976d2" }}>
                Filter: {filterPopup}
              </span>
              <span style={{
                fontSize: "12px",
                color: "#666",
                backgroundColor: "#f5f5f5",
                padding: "2px 6px",
                borderRadius: "4px"
              }}>
                {getDynamicUniqueValues(filterPopup).length} items
              </span>
            </div>

            {getDynamicUniqueValues(filterPopup).length > 10 && (
              <div style={{ marginBottom: "1px", paddingRight: "1px" }}>
                <input
                  type="text"
                  placeholder="Search..."
                  style={{
                    // width: "100%",
                    maxWidth: "100px",
                    padding: "3px 3px",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                    fontSize: "12px"
                  }}
                />
              </div>
            )}

            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "1px 1px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "1px",
                backgroundColor: "#f8f9fa",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[ filterPopup ] &&
                  getDynamicUniqueValues(filterPopup).every((opt) =>
                    tempFilters[ filterPopup ].includes(opt)
                  )
                }
                onChange={() => handleSelectAll(filterPopup)}
                style={{ marginRight: "8px" }}
              />
              <span>Select All</span>
            </label>

            <div
              style={{
                maxHeight: "150px",
                overflowY: "auto",
                margin: "0",
                scrollbarWidth: "thin",
                scrollbarColor: "#888 #f1f1f1",
                border: "1px solid #eee",
                borderRadius: "4px",
                marginRight: "1px"
              }}
            >
              {getDynamicUniqueValues(filterPopup).length > 0 ? (
                getDynamicUniqueValues(filterPopup).map((item) => {
                  const isSelected = tempFilters[ filterPopup ]?.includes(item) || false;
                  return (
                    <div
                      key={item}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "1px 2px",
                        cursor: "pointer",
                        margin: "0",
                        borderBottom: "1px solid #f0f0f0",
                        backgroundColor: isSelected ? "#f0f7ff" : "transparent",
                        transition: "background-color 0.2s"
                      }}
                      onClick={() => handleFilterChange(filterPopup, item)}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => { }}
                        style={{ marginRight: "8px" }}
                      />
                      <span style={{
                        color: isSelected ? "#1976d2" : "#444",
                        fontWeight: isSelected ? "500" : "normal"
                      }}>
                        {item || "(Empty)"}
                      </span>
                    </div>
                  );
                })
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "10px",
                    color: "#666",
                    fontStyle: "italic",
                    minHeight: "40px",
                    justifyContent: "center"
                  }}
                >
                  <span>No options available</span>
                </div>
              )}
            </div>

            <div style={{
              fontSize: "12px",
              color: "#666",
              margin: "1px 0",
              display: "flex",
              justifyContent: "space-between",
              marginRight: "1px"
            }}>
              <span>
                {tempFilters[ filterPopup ]?.length || 0} of {getDynamicUniqueValues(filterPopup).length} selected
              </span>
              {tempFilters[ filterPopup ]?.length > 0 && (
                <span
                  style={{ color: "#1976d2", cursor: "pointer" }}
                  onClick={() => setTempFilters({ ...tempFilters, [ filterPopup ]: [] })}
                >
                  Clear selection
                </span>
              )}
            </div>
          </div>

          <div
            style={{
              marginTop: "3px",
              display: "flex",
              gap: "10px",
              justifyContent: "center",
              paddingRight: "3px"
            }}
          >
            <button
              onClick={handleCancelFilter}
              style={{
                padding: "3px 3px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                background: "#f8f9fa",
                cursor: "pointer",
                color: "#333",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilter}
              style={{
                padding: "3px 3px",
                border: "none",
                borderRadius: "4px",
                background: "#1976d2",
                color: "white",
                cursor: "pointer",
                transition: "all 0.2s",
                fontWeight: "500"
              }}
            >
              Apply Filter
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Positions;