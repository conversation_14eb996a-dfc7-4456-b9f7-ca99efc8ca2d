import React from "react";
import Modal from "react-modal";
import { FaXmark } from "react-icons/fa6";

const MODAL_STYLES = {
  overlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 9999,
  },
  content: {
    color: '#333',
    top: '50%',
    left: '50%',
    right: 'auto',
    bottom: 'auto',
    marginRight: '-50%',
    transform: 'translate(-50%, -50%)',
    width: '60%',
    height: '600px',
    padding: '0',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
    position: 'relative',
    overflow: 'hidden',
  }
};

const HEADER_STYLES = {
  container: {
    backgroundColor: "#4661bd",
    padding: "5px 10px",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center"
  },
  title: {
    fontSize: "20px",
    color: "#ffffff",
    fontWeight: "700",
    fontFamily: "Roboto",
    margin: 0
  },
  closeButton: {
    color: "white",
    fontSize: '40px',
    cursor: 'pointer',
    border: "none",
    background: "none",
    display: "flex",
    alignItems: "center"
  }
};

const CONTENT_STYLES = {
  container: { margin: "10px" },
  text: {
    fontSize: "20px",
    lineHeight: "24px",
    color: "#000",
    fontFamily: "Roboto",
    padding: "10px 15px"
  },
  table: {
    width: "100%",
    border: "0.5px solid #b3b0b0",
    height: "350px",
    borderRadius: "8px",
    marginBottom: "5px"
  },
  thead: {
    position: "sticky",
    top: 0,
    background: "#d8e1ff",
    zIndex: 10
  },
  th: { textAlign: "center", padding: "8px" },
  td: { textAlign: "center", padding: "15px" },
  buttonContainer: {
    display: "flex",
    justifyContent: "flex-end",
    gap: "10px",
    padding: "10px 0",
    marginTop: "-20px"
  }
};

const BUTTON_STYLES = {
  primary: {
    padding: "10px 20px",
    border: "none",
    borderRadius: "5px",
    fontSize: "17px",
    color: "#fff",
    cursor: "pointer",
    backgroundColor: "#4661bd",
    lineHeight: "20px"
  },
  secondary: {
    padding: "10px 20px",
    border: "none",
    borderRadius: "5px",
    fontSize: "17px",
    color: "#000",
    cursor: "pointer",
    backgroundColor: "#d8e1ff",
    lineHeight: "20px"
  }
};

export const ExitPosition = ({ isExitOpen, ExitcloseModal }) => {
  const handleExitCloseModal = () => ExitcloseModal();

  return (
    <Modal
      isOpen={isExitOpen}
      onRequestClose={handleExitCloseModal}
      contentLabel="Exit Position Modal"
      style={MODAL_STYLES}
    >
      <div style={HEADER_STYLES.container}>
        <h2 style={HEADER_STYLES.title}>Exit Positions</h2>
        <button onClick={handleExitCloseModal} style={HEADER_STYLES.closeButton}>
          <FaXmark />
        </button>
      </div>

      <div style={CONTENT_STYLES.container}>
        <p style={{ ...CONTENT_STYLES.text, fontWeight: "700" }}>
          It is always suggested to use Exit Order for exiting any positions.
          If exit order is not available, you can use Exit Positions here.
        </p>

        <table style={CONTENT_STYLES.table}>
          <thead style={CONTENT_STYLES.thead}>
            <tr>
              <th style={{ ...CONTENT_STYLES.th, width: "13%" }}>Exit</th>
              <th style={{ ...CONTENT_STYLES.th, width: "17%" }}>Product</th>
              <th style={{ ...CONTENT_STYLES.th, width: "18%" }}>Exchange</th>
              <th style={{ ...CONTENT_STYLES.th, width: "18%" }}>Symbol</th>
            </tr>
          </thead>
          <tbody style={{ backgroundColor: "#959595" }}>
            <tr>
              <td style={CONTENT_STYLES.td}></td>
              <td style={CONTENT_STYLES.td}></td>
              <td style={CONTENT_STYLES.td}></td>
              <td style={CONTENT_STYLES.td}></td>
            </tr>
          </tbody>
        </table>

        <p style={{ ...CONTENT_STYLES.text, fontWeight: "500" }}>
          Handle Pending LIMIT Orders (If any left open)
        </p>

        <div style={CONTENT_STYLES.buttonContainer}>
          <button style={BUTTON_STYLES.primary}>Exit Position</button>
          <button style={BUTTON_STYLES.secondary} onClick={handleExitCloseModal}>
            Cancel
          </button>
        </div>
      </div>
    </Modal>
  );
};