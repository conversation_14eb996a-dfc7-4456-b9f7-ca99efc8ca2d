import React, { useState, useEffect, useRef } from "react";
import shape from "../assets/shape.png";
import pencil from "../assets/pencil.png";
import minus from "../assets/minus-sign.png";
import plus from "../assets/plus-sign.png";
import Draggable from "react-draggable";
import push from "../assets/push-pin.png";
import { Oval } from "react-loader-spinner";
import { useSelector, useDispatch } from "react-redux";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import Cookies from "universal-cookie";
import { setBrokers } from "../store/slices/broker.js";
import { useFetchPortfolios } from "../hooks/useFetchPortfolios";
import { fetchWithAuth } from "../utils/api";
import useClickOutside from "../hooks/useClickOutside";


const cookies = new Cookies();

export const OptionQuickTradePanel = ({
  handleClose1,
  isOpen1,
  colopen1,
  toggleOpen1,
  setIsOpen1,
  resetPositionoq,
  positionOq,
  handleDragoq,
}) => {
  const mainUser = cookies.get("USERNAME");
  const dispatch = useDispatch();
  const panelRef = useRef(null);

  // Close panel when clicking outside
  useClickOutside(panelRef, () => {
    if (isOpen1) {
      handleClose1();
    }
  });

  const fetchPortfolios = useFetchPortfolios();
  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;

      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.startegy &&
        lastMsg.portfolio === Msg.porttfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };

  const fetchAccountDetails = async () => {
    try {
      const response = await fetchWithAuth(`/api/get_user_data/${mainUser}`, {
        method: "GET",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Something went wrong. Please try again.");
      }

      const responseData = await response.json();
      const pseudoAccounts = responseData?.broker_credentials?.filter(
        (row) => row.broker === "pseudo_account"
      );

      if (!pseudoAccounts || pseudoAccounts.length === 0) {
        throw new Error("Pseudo account not found.");
      }
      const newFilledData = rows.map((account) => {
        const matchingPseudoAccount = pseudoAccounts.find(
          (pseudoAccount) => pseudoAccount.broker_user_id === account.userId
        );

        if (matchingPseudoAccount) {
          return {
            ...account,
            availableMargin: matchingPseudoAccount.available_balance ?? "0.00",
            utilized_Margin: matchingPseudoAccount.utilized_margin ?? "0.00",
          };
        }
        return account;
      });

      dispatch(
        setBrokers({
          brokers: newFilledData,
        })
      );
    } catch (error) {

    }
  };
  const [ executedportfolios, setexecutedportfolios ] = useState([]);


  const fetchExecutedPortfolios = async () => {
    try {
      const response = await fetchWithAuth(`/api/get_executed_portfolios/${mainUser}`, {
        method: "POST",
      });
      if (!response.ok) {
        throw new Error("Failed to fetchWithAuth executed portfolios");
      }
      const { ExecutedPortfolios } = await response.json();
      setexecutedportfolios(ExecutedPortfolios);
    } catch (error) {

    }
  };

  const { portfolios: portfolioDetails } = useSelector(
    (state) => state.portfolioReducer
  );


  const { brokers: rows } = useSelector((state) => state.brokerReducer);

  const { placeOrderStart } = useSelector(
    (state) => state.placeOrderStartReducer
  );

  const [ optionsQTP, setOptionsQTP ] = useState({
    exchange: "",
    stock_symbol: "",
    lots: "",
    variety: "",
    strategy: "",
    userIds: "",
    portfolio_name: "",
    Option_Strategy: "",
  });

  useEffect(() => {
    if (!isOpen1) {
      setplaceOrderOptionsQTPBtn(false);
      setOptionsQTP({
        exchange: "",
        stock_symbol: "",
        lots: "",
        variety: "",
        strategy: "",
        userIds: "",
        portfolio_name: "",
        Option_Strategy: "",
      });
    }
  }, [ isOpen1 ]);
  const [ updateKey, setUpdateKey ] = useState(0);

  const close = () => {
    setOptionsQTP({
      exchange: "",
      stock_symbol: "",
      lots: "",
      variety: "",
      strategy: "",
      userIds: "",
      portfolio_name: "",
      Option_Strategy: "",
    });
    handleClose1();
    resetPositionoq();
    forceUpdate();
  };

  const forceUpdate = () => {
    setUpdateKey((prev) => prev + 1);
  };

  const [ placeOrderOptionsQTPBtn, setplaceOrderOptionsQTPBtn ] = useState(false);

  const placeOrderOptionsQTP = async () => {
    try {
      const currentTime = new Date();
      // const currentHours = currentTime.getHours();
      // const currentMinutes = currentTime.getMinutes();

      // if (
      //   !(
      //     (currentHours === 9 && currentMinutes >= 15) ||
      //     (currentHours > 9 && currentHours < 15) ||
      //     (currentHours === 15 && currentMinutes <= 30)
      //   )
      // ) {
      //   handleMsg({
      //     msg: `Order not placed as current time is outside the allowed time window.`,
      //     logType: "INFO",
      //     timestamp: `${new Date().toLocaleString()}`,
      //   });
      //   setplaceOrderOptionsQTPBtn(false);
      //   close();
      //   return;
      // }
      const marketData = JSON.parse(localStorage.getItem("marketIndexDetails"));

      // Iterate through userIds
      optionsQTP.userIds.forEach(async (userId, index) => {
        const user = rows.find((user) => user.userId === userId);
        if (user && user.inputDisabled) {
          let apiEndpoints = [];
          const matchingPortfolio = portfolioDetails.find(
            (portfolio) =>
              portfolio.portfolio_name === optionsQTP.Option_Strategy
          );

          if (matchingPortfolio) {
            let startTime = matchingPortfolio.start_time || null;
            let endTime = matchingPortfolio.end_time || null;

            // Adjust start and end times
            if (startTime === "00:00:00") startTime = null;
            if (endTime === "00:00:00") endTime = null;

            const currentTimeStr = currentTime.toLocaleTimeString("en-US", {
              hour12: false,
            });

            if (
              (startTime === null || currentTimeStr >= startTime) &&
              (endTime === null || currentTimeStr <= endTime)
            ) {
              let optionType = null;
              for (const leg of matchingPortfolio.legs) {
                if (leg.option_type === "FUT") {
                  optionType = "FUT";
                  break;
                }
              }

              if (user.broker === "fyers") {
                apiEndpoints =
                  optionType === "FUT"
                    ? [
                      `/api/fyers_futures_place_order/fyers/${mainUser}/${optionsQTP.Option_Strategy}/${user.userId}`,
                    ]
                    : [
                      `/api/place_order/fyers/${mainUser}/${optionsQTP.Option_Strategy}/${user.userId}`,
                    ];
              } else if (user.broker === "angelone") {
                apiEndpoints =
                  optionType === "FUT"
                    ? [
                      `/api/angleone_future_place_order/angelone/${mainUser}/${optionsQTP.Option_Strategy}/${user.userId}`,
                    ]
                    : [
                      `/api/angelone_options_place_order/${mainUser}/${optionsQTP.Option_Strategy}/${user.userId}`,
                    ];
              } else if (user.broker === "flattrade") {
                apiEndpoints =
                  optionType === "FUT"
                    ? [
                      `/api/flatrade_future_place_order/flattrade/${mainUser}/${optionsQTP.Option_Strategy}/${user.userId}`,
                    ]
                    : [
                      `/api/flatrade_place_order/${mainUser}/${optionsQTP.Option_Strategy}/${user.userId}`,
                    ];
              } else if (user.broker === "pseudo_account") {
                apiEndpoints = [
                  `/api/pseudo_placeorder/${mainUser}/${optionsQTP.Option_Strategy}/${user.userId}`,
                ];
              }

              for (const apiEndpoint of apiEndpoints) {
                try {
                  const requestBody = {
                    qtp_lots: optionsQTP.lots || 1,
                  };

                  if (user.broker === "pseudo_account") {
                    requestBody.underlying_price = marketData;
                  }

                  const res = await fetchWithAuth(apiEndpoint, {
                    method: "POST",
                    body: JSON.stringify(requestBody),
                  });

                  if (res.ok) {
                    fetchAccountDetails();
                    fetchPortfolios();
                    fetchExecutedPortfolios();
                    const orderPlaceoptionsQTPRes = await res.json();
                    setplaceOrderOptionsQTPBtn(false);
                    close();

                    let legMsgs = orderPlaceoptionsQTPRes.messages;

                    if (!Array.isArray(legMsgs)) {
                      legMsgs = [ legMsgs ];
                    }
                    for (const message of legMsgs) {
                      handleMsg({
                        msg: message.message,
                        logType: "TRADING",
                        timestamp: `${new Date().toLocaleString()}`,
                        user: user.userId,
                        strategy: matchingPortfolio.strategy,
                        portfolio: optionsQTP.Option_Strategy,
                      });
                    }
                  } else {
                    const orderPlaceoptionsQTPRes = await res.json();
                    handleMsg({
                      msg: orderPlaceoptionsQTPRes[ 0 ].message,
                      logType: "MESSAGE",
                      timestamp: `${new Date().toLocaleString()}`,
                      user: user.userId,
                      strategy: matchingPortfolio.strategy,
                      portfolio: optionsQTP.Option_Strategy,
                    });
                  }
                } catch (e) {
                  handleMsg({
                    msg: e.message,
                    logType: "ERROR",
                    timestamp: `${new Date().toLocaleString()}`,
                    user: user.userId,
                    strategy: matchingPortfolio.strategy,
                    portfolio: optionsQTP.Option_Strategy,
                  });
                }
              }
            } else {
              handleMsg({
                msg: `Order not placed for ${user.userId
                  } as current time is outside the allowed time window (Start: ${startTime || "Not specified"
                  }, End: ${endTime || "Not specified"}).`,
                logType: "INFO",
                timestamp: `${new Date().toLocaleString()}`,
                user: user.userId,
                strategy: matchingPortfolio.strategy,
                portfolio: optionsQTP.Option_Strategy,
                color: "red",
              });
              setplaceOrderOptionsQTPBtn(false);
              close();
            }
          }
        } else {
          handleMsg({
            msg: `Login the ${user.userId}, to place an order in this account.`,
            logType: "WARNING",
            timestamp: `${new Date().toLocaleString()}`,
            user: user.userId,
            strategy: portfolioDetails.find(
              (row) => row.portfolio_name === optionsQTP.Option_Strategy
            ).strategy,
            portfolio: optionsQTP.Option_Strategy,
          });
          if (
            optionsQTP.userIds.length === 1 ||
            index === optionsQTP.userIds.length - 1
          ) {
            setplaceOrderOptionsQTPBtn(false);
            close();
          }
        }
      });
    } catch (error) {
      handleMsg({
        msg: error.message,
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
      });
    }
  };

  const { strategies } = useSelector((state) => state.strategyReducer);

  return (
    <div style={{ zIndex: "1000" }}>
      <Draggable
        handle=".handle"
        cancel=".details, select, input"
        defaultPosition={{ x: 0, y: 0 }}
        position={positionOq}
        onDrag={handleDragoq}
      >
        <div
          key={updateKey}
          className="your-modal-button"
          style={{
            display: isOpen1 ? "block" : "none",
            top: "10%",
            left: "25%",
            overlay: {
              backgroundColor: "visible",
              zIndex: 1000,
              pointerEvents: "none",
            },
            content: {
              pointerEvents: "auto",
            },
          }}
        >
          <div ref={panelRef} className="container1 handle">
            <div
              className="title"
              style={{ display: "flex", justifyContent: "flex-start" }}
            >
              <h2
                style={{
                  marginLeft: "-329px",
                  marginTop: "5px",
                  color: "#4661bd",
                  fontFamily: "Roboto",
                  fontSize: "24px",
                }}
              >
                Options Quick Trade Panel
              </h2>
              <div style={{ marginLeft: "43px" }}></div>
              <img src={pencil} style={{ padding: "5px" }} alt="" />
              <img
                src={colopen1 ? minus : plus}
                style={{ cursor: "pointer", padding: "8px" }}
                onClick={toggleOpen1}
                id="colopen1"
                alt=""
              />
              <img src={push} style={{ padding: "5px" }} alt="" />
              <img
                src={shape}
                style={{ cursor: "pointer", padding: "5px" }}
                onClick={close}
                alt=""
              />
            </div>
            <div className="content">
              <form>
                <div className="user-details">
                  <div className="MCX">
                    <select
                      className="details"
                      style={{ color: "GrayText" }}
                      value={optionsQTP.exchange !== "" ? optionsQTP.exchange : "NFO"}
                      onChange={(e) => {
                        setOptionsQTP((prev) => ({
                          ...prev,
                          exchange: e.target.value,
                        }));
                      }}
                      disabled={optionsQTP.Option_Strategy}
                    >
                      <option disabled value="">Exchange</option>
                      <option style={{ color: "black" }}>BFO</option>
                      <option style={{ color: "black" }}>NFO</option>
                      <option style={{ color: "black" }}>MCX</option>
                    </select>
                  </div>
                  <div className="EFS">
                    <input
                      value={
                        optionsQTP.stock_symbol !== ""
                          ? optionsQTP.stock_symbol
                          : ""
                      }
                      onChange={(e) => {
                        setOptionsQTP((prev) => ({
                          ...prev,
                          stock_symbol: e.target.value,
                        }));
                      }}
                      type="text"
                      placeholder="Enter Future Stock"
                      disabled={optionsQTP.Option_Strategy}
                    />
                  </div>
                  <div className="OS">
                    <select
                      className="details"
                      style={{ color: "GrayText" }}
                      value={optionsQTP.Option_Strategy !== "" ? optionsQTP.Option_Strategy : ""}
                      onChange={(e) => {
                        const portfolio = portfolioDetails.filter(
                          (portfolio) => portfolio.portfolio_name === e.target.value
                        );

                        const mappedbrokerIds = portfolio[ 0 ][ "Strategy_accounts_id" ].split(",");

                        const loggedInbrokers = rows.filter((row) => {
                          if (row.inputDisabled && mappedbrokerIds.includes(row.userId)) {
                            return row;
                          }
                        });
                        const loggedInbrokerIds = loggedInbrokers.map((row) => row.userId);
                        const loggedOutbrokers = rows.filter((row) => {
                          if (!row.inputDisabled && mappedbrokerIds.includes(row.userId)) {
                            return row;
                          }
                        });
                        const loggedOutbrokerIds = loggedOutbrokers.map((row) => row.userId);
                        setOptionsQTP((prev) => ({
                          ...prev,
                          userIds: [ ...loggedOutbrokerIds, ...loggedInbrokerIds ],
                          exchange: portfolio[ 0 ].exchange,
                          stock_symbol: portfolio[ 0 ].stock_symbol,
                          lots: Number(portfolio[ 0 ].lots),
                          variety: portfolio[ 0 ].variety,
                          strategy: portfolio[ 0 ].strategy,
                          Option_Strategy: e.target.value,
                        }));
                      }}
                    >
                      <option disabled value="">Portfolio</option>
                      {portfolioDetails
                        .filter(
                          (port) =>
                            !executedportfolios
                              .map((execPort) => execPort.portfolio_name)
                              .includes(port.portfolio_name)
                        )
                        .filter((portfolioItem) => portfolioItem.enabled === true)
                        .filter((portfolioItem) => {
                          const linkedStrategy = strategies.find(
                            (strategy) => strategy.StrategyLabel === portfolioItem.strategy
                          );
                          return linkedStrategy && linkedStrategy.enabled;
                        })
                        .map((portfolioItem, index) => (
                          <option key={index} style={{ color: "black" }}>
                            {portfolioItem.portfolio_name}
                          </option>
                        ))}
                    </select>
                  </div>
                  <div className="Lots">
                    <span
                      className="label"
                      style={{ fontFamily: "Roboto", fontSize: "19px" }}
                    >
                      {" "}
                      Lots{" "}
                    </span>
                    <input
                      type="number"
                      min={1}
                      step={1}
                      placeholder="1"
                      value={optionsQTP.lots ?? ""}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        setOptionsQTP((prev) => ({
                          ...prev,
                          lots:
                            inputValue === ""
                              ? ""
                              : Math.max(Number(inputValue), 1),
                        }));
                      }}
                    />
                  </div>
                  <div className="NRML">
                    <select
                      className="details"
                      style={{ color: "GrayText" }}
                      value={optionsQTP.variety !== "" ? optionsQTP.variety : ""}
                      onChange={(e) => {
                        setOptionsQTP((prev) => ({
                          ...prev,
                          variety: e.target.value,
                        }));
                      }}
                      disabled={optionsQTP.Option_Strategy}
                    >
                      <option disabled value="">Product</option>
                      <option value="NORMAL" style={{ color: "black" }}>
                        NRML
                      </option>
                      <option value="MIS" style={{ color: "black" }}>
                        MIS
                      </option>
                    </select>
                  </div>
                  <div className="DEFAULT">
                    <select
                      value={optionsQTP.strategy !== "" ? optionsQTP.strategy : ""}
                      onChange={(e) => {
                        const linkedList = portfolioDetails.filter(
                          (portfolio) => portfolio.strategy === e.target.value
                        );
                        const brokerIds = linkedList[ 0 ][ "Strategy_accounts_id" ].split(",");
                        setOptionsQTP((prev) => ({
                          ...prev,
                          userIds: brokerIds,
                          strategy: e.target.value,
                        }));
                      }}
                      className="details"
                      style={{ color: "GrayText" }}
                      disabled={optionsQTP.Option_Strategy}
                    >
                      <option disabled value="">Strategy</option>
                      {strategies
                        .filter((row) => row.enabled)
                        .map((row, index) => (
                          <option key={index} value={row.StrategyLabel}>
                            {row.StrategyLabel}
                          </option>
                        ))}
                    </select>
                  </div>
                </div>

                <div
                  className="OP-details"
                  id="OP-details"
                  style={{
                    border: "1px solid #cacaca",
                    padding: "10px",
                    margin: "2px",
                    display: "none",
                  }}
                >
                  <span className="OP-title" style={{ padding: "10px" }}>
                    Optional Parameters
                  </span>
                  <div className="UD-bottom">
                    <div className="input-box">
                      <span className="details">Entry Price</span>
                      <input type="text" defaultValue={optionsQTP?.entry_price ?? ""} readOnly={true} />
                    </div>
                    <div className="input-box">
                      <span className="details">Combt Target</span>
                      <input type="text" defaultValue={optionsQTP?.comb_target ?? ""} readOnly={true} />
                    </div>
                    <div className="input-box">
                      <span className="details">Comb SL</span>
                      <input type="text" defaultValue={optionsQTP?.comb_sl ?? ""} readOnly={true} />
                    </div>
                  </div>

                  <div
                    className="UD"
                    style={{ display: "flex", margin: "5px" }}
                  >
                    <div className="input-box">
                      <span className="details">Leg Target</span>
                      <input type="text" defaultValue={optionsQTP?.leg_target ?? ""} readOnly={true} />
                    </div>
                    <div className="input-box" style={{ marginLeft: "0px" }}>
                      <span className="details">Leg SL</span>
                      <input type="text" defaultValue={optionsQTP?.leg_sl ?? ""} readOnly={true} />
                    </div>
                    <div className="checkbox1">
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          marginLeft: "10px",
                          marginTop: "17px",
                        }}
                      >
                        <input
                          type="checkbox"
                          id="legSlCheckbox"
                          style={{ opacity: "1", accentColor: "green" }}
                          defaultValue={optionsQTP?.move_sl_to_cost ?? false}
                        />
                        <label
                          htmlFor="legSlCheckbox"
                          style={{
                            position: "relative",
                            cursor: "pointer",
                            border: "2px solid green",
                            marginLeft: "-20.8px",
                            height: "20.3px",
                            width: "23.9px",
                          }}
                        ></label>
                        <span
                          style={{
                            marginLeft: "9px",
                            display: "inline-block",
                            fontFamily: "Roboto",
                          }}
                        >
                          Move SL To Cost
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="box2">
                  <select
                    className="detail"
                    style={{ color: "GrayText" }}
                    value={optionsQTP.portfolio_name || ""}
                    onChange={(e) => {
                      setOptionsQTP((prev) => ({
                        ...prev,
                        portfolio_name: e.target.value,
                      }));
                    }}
                  >
                    <option
                      value=""
                      disabled
                      style={{ fontFamily: "Roboto" }}
                    >
                      Executed Portfolios
                    </option>
                    {executedportfolios.map((portfolio, index) => (
                      <option key={index} value={portfolio}>
                        {portfolio.portfolio_name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="user-details-start">
                  <div className="input-box">
                    {!placeOrderOptionsQTPBtn ? (
                      <button
                        disabled={optionsQTP.Option_Strategy === ""}
                        type="button"
                        onClick={() => {
                          if (placeOrderStart) {
                            setplaceOrderOptionsQTPBtn(true);
                            placeOrderOptionsQTP();
                          } else {
                            setIsOpen1(false);
                            handleMsg({
                              msg: "To place an Order, Start the Trading.",
                              logType: "WARNING",
                              timestamp: `${new Date().toLocaleString()}`,
                            });
                          }
                        }}
                        style={
                          optionsQTP.Option_Strategy !== ""
                            ? { background: "#85e657" }
                            : {
                              background: "#ccc",
                              color: "#666",
                              cursor: "not-allowed",
                            }
                        }
                      >
                        ENTRY
                      </button>
                    ) : (
                      <button
                        type="button"
                        style={{ background: "#618F00", cursor: "default" }}
                      >
                        <Oval
                          className
                          height="20"
                          width="255"
                          color="white"
                          strokeWidth={3}
                        />
                      </button>
                    )}
                  </div>
                  <div className="input-box">
                    <button
                      type="button"
                      text="exit"
                      style={{ background: "#ff0000" }}
                      onClick={close}
                    >
                      EXIT
                    </button>
                  </div>
                  <div className="UD-button">
                    <p
                      style={{
                        color: "blue",
                        fontSize: "18px",
                        padding: "2px",
                        display: "none",
                        fontFamily: "roboto",
                      }}
                      className="SL1"
                    >
                      Target, SL in Points. For Percentage, Tick Value in %
                    </p>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </Draggable>
    </div>
  );
};
