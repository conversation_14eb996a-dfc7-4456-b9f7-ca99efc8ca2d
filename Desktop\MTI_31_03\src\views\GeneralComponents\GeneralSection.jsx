import React from "react";
import { GoDatabase } from "react-icons/go";
import { MdRestore } from "react-icons/md";

const GeneralSection = ({ onCancel }) => (
    <div className="general-section" style={{
        padding: "1px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box"
    }}>

        <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "15px",
            marginBottom: "1px"
        }}>
            <div style={{ background: "#f8fafc", padding: "10px", borderRadius: "8px" }}>
                <p style={{ margin: "3px 0", fontSize: "12px" }}>Expiry Date: 30-Jan-2024</p>
                <p style={{ margin: "3px 0", fontSize: "12px" }}>Max Users: 8</p>
                <p style={{ margin: "3px 0", fontSize: "12px" }}>User Type: Paid</p>
                <p style={{ margin: "1px 0", fontSize: "12px" }}>Option Module Exp: 30-Jan-2024</p>
            </div>

            <div>
                <h3 style={{ color: "#4661bd", fontSize: "16px", marginBottom: "3px" }}>Bridge Settings</h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "1px" }}>
                    <label style={{ display: "flex", alignItems: "center", gap: "3px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Run Web Server</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "3px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Force Use LTP from Signal</span>
                    </label>
                    <label style={{ display: "flex", alignItems: "center", gap: "3px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>Keep Running in Notification Area</span>
                    </label>
                </div>
            </div>
        </div>

        <div style={{ marginBottom: "1px" }}>
            <h3 style={{ color: "#4661bd", fontSize: "16px", marginBottom: "5px" }}>Logs Settings</h3>
            <div style={{
                display: "grid",
                gridTemplateColumns: "repeat(7, 1fr)",
                gap: "1px",
                marginBottom: "1px"
            }}>
                {[ "Broker Logs", "Error Logs", "Feed Logs", "Detailed Logs", "Debug Logs", "MTM Logs", "Audit Logs" ].map((log) => (
                    <label key={log} style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                        <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                        <span style={{ fontSize: "12px" }}>{log}</span>
                    </label>
                ))}
            </div>
            <div style={{ display: "flex", gap: "15px", flexWrap: "wrap" }}>
                <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <span style={{ fontSize: "12px" }}>Keep Logs for (Days):</span>
                    <input
                        type="number"
                        defaultValue="1"
                        min="1"
                        style={{
                            width: "50px",
                            padding: "6px",
                            borderRadius: "6px",
                            border: "1px solid #ddd",
                            fontSize: "12px"
                        }}
                    />
                </label>
                <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <span style={{ fontSize: "12px" }}>Max Grid Logs Rows:</span>
                    <input
                        type="number"
                        defaultValue="1"
                        min="1"
                        style={{
                            width: "50px",
                            padding: "6px",
                            borderRadius: "6px",
                            border: "1px solid #ddd",
                            fontSize: "12px"
                        }}
                    />
                </label>
            </div>
        </div>
        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))", gap: "10px",  }}>
            <div>
                <h3 style={{ color: "#4661bd", fontSize: "16px", marginBottom: "1px" }}>Trade Settings</h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "1px" }}>
                    {[
                        "Allow Shortcuts (Shift + F1, F2, F3)",
                        "On Order Exit, Mark Signal as Exit",
                        "Stop Trading on SquareOff",
                        "Trigger Only on Cross Over",
                        "Bid / Ask Required from Feed",
                        "SquareOff Intraday CNC and NRML Orders",
                        "Needle All Strikes for Options",
                        "Ask Confirmation on OTP Orders",
                        "Candle Data Required"
                    ].map((label) => (
                        <label key={label} style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                            <input type="checkbox" style={{ width: "14px", height: "14px" }} />
                            <span style={{ fontSize: "12px" }}>{label}</span>
                        </label>
                    ))}
                    {[
                        { label: "No Duplicate Place Order Seconds", defaultValue: 1 },
                        { label: "MTM Logging Time in Seconds", defaultValue: 1 }
                    ].map(({ label, defaultValue }) => (
                        <label key={label} style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                            <span style={{ fontSize: "12px" }}>{label}:</span>
                            <input
                                type="number"
                                defaultValue={defaultValue}
                                min="1"
                                style={{
                                    width: "50px",
                                    height: "20px",
                                    padding: "6px",
                                    borderRadius: "6px",
                                    border: "1px solid #ddd",
                                    fontSize: "12px"
                                }}
                            />
                        </label>
                    ))}
                </div>
            </div>

            <div style={{ background: "#f8fafc", padding: "10px", borderRadius: "8px", height: "fit-content" }}>
                <h3 style={{ color: "#4661bd", fontSize: "16px", marginBottom: "10px" }}>Backup & Restore</h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
                    <label style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                        <span style={{ fontSize: "12px" }}>Password:</span>
                        <input
                            type="password"
                            style={{
                                width: "100%",
                                padding: "6px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "12px"
                            }}
                        />
                    </label>
                    <div style={{ display: "flex", gap: "10px" }}>
                        {[
                            { text: "Backup", icon: <GoDatabase size={14} /> },
                            { text: "Restore", icon: <MdRestore size={14} /> }
                        ].map(({ text, icon }) => (
                            <button
                                key={text}
                                style={{
                                    flex: 1,
                                    padding: "8px",
                                    background: "#d8e1ff",
                                    color: "#4661bd",
                                    border: "none",
                                    borderRadius: "6px",
                                    cursor: "pointer",
                                    fontSize: "12px",
                                    fontWeight: "600",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    gap: "5px",
                                    transition: "background 0.3s ease"
                                }}
                                onMouseEnter={(e) => e.target.style.background = "#c7d2fe"}
                                onMouseLeave={(e) => e.target.style.background = "#d8e1ff"}
                            >
                                {icon} {text}
                            </button>
                        ))}
                    </div>
                </div>
            </div>
        </div>

        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "10px",
            marginTop: "-65px"
        }}>
            <button style={{
                padding: "10px 20px",
                background: "#4661bd",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "12px",
                fontWeight: "600",
                transition: "background 0.3s ease"
            }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Save Settings
            </button>
            <button
                onClick={onCancel}
                style={{
                    padding: "10px 20px",
                    background: "#fff",
                    color: "#666",
                    border: "1px solid #ddd",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "12px",
                    fontWeight: "600",
                    transition: "all 0.3s ease"
                }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default GeneralSection;
