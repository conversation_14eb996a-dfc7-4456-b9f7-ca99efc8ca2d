import React from "react";
import { Routes, Route } from "react-router-dom";
import UserProfiles from "./views/UserProfiles.jsx";
import Strategies from "./views/Strategies.jsx";
import Equity from "./views/Equity.jsx";
import Portfolio from "./views/F&O/Portfolio.jsx";
import AddPortfolio from "./views/F&O/AddPortfolio.jsx";
import OrderFlow from "./views/OrderFlow.jsx";
import OrderManagement from "./views/OrderManagement.jsx";
import Positions from "./views/Positions.jsx";
import Holdings from "./views/Holdings.jsx";
import TradingView from "./views/GeneralComponents/TradingView.jsx";
import ProtectedRoute from "./components/ProtectedRoute.jsx";
import Register from "./views/Register/Register.jsx";
import Login from "./views/Login/Login.jsx";
import Change_Password from "./components/Change_Password.jsx";
import Option_Chain from "./components/Option_Chain.jsx";
import Subscription from "./components/Subscription.jsx";
import SecurityCode from "./components/SecurityCode.jsx";
import New_Password from "./components/New_Password.jsx";
import PasswordRecovery from "./components/PasswordRecovery.jsx";
import Master_child from "./views/Master_child.jsx";
import Master_accounts from "./views/Master_accounts.jsx";

const AppRoutes = ({ isSubscribed }) => {
    return (
        <Routes basename="/" hashtype="noslash">
            {/* Public Routes */}
            <Route path="/" element={<Login />} />
            <Route path="/Login" element={<Login />} />
            <Route path="/Register" element={<Register />} />
            <Route path="/Subscription" element={<Subscription />} />
            <Route path="/passwordRecovery" element={<PasswordRecovery />} />
            <Route path="/SecurityCode/:username" element={<SecurityCode />} />
            <Route path="/New_Password/:username" element={<New_Password />} />
            <Route path="/Change_Password" element={<Change_Password />} />

            {/* Protected Routes */}
            <Route path="/UserProfiles" element={<ProtectedRoute isSubscribed={isSubscribed} element={<UserProfiles />} />} />
            <Route path="/Strategies" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Strategies />} />} />
            <Route path="/Equity" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Equity />} />} />
            <Route path="/F&O/Portfolio" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Portfolio />} />} />
            <Route path="/F&O/AddPortfolio" element={<ProtectedRoute isSubscribed={isSubscribed} element={<AddPortfolio />} />} />
            <Route path="/Edit-Portfolio/:portfolio" element={<ProtectedRoute isSubscribed={isSubscribed} element={<AddPortfolio />} />} />
            <Route path="/Positions" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Positions />} />} />
            <Route path="/Holdings" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Holdings />} />} />
            <Route path="/TradingView" element={<ProtectedRoute isSubscribed={isSubscribed} element={<TradingView />} />} />
            <Route path="/OrderFlow" element={<ProtectedRoute isSubscribed={isSubscribed} element={<OrderFlow />} />} />
            <Route path="/OrderManagement" element={<ProtectedRoute isSubscribed={isSubscribed} element={<OrderManagement />} />} />
            <Route path="/Option_Chain" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Option_Chain />} />} />
            <Route path="/Master_child" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Master_child />} />} />
            <Route path="/Master_accounts" element={<ProtectedRoute isSubscribed={isSubscribed} element={<Master_accounts />} />} />
        </Routes>
    );
};

export default AppRoutes;
