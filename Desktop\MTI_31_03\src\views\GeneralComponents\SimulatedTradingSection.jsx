import React from "react";

const SimulatedTradingSection = ({
    capital,
    setCapital,
    priceType,
    setPriceType,
    leverage,
    handleLeverageChange,
    selectedUser,
    setSelectedUser
}) => (
    <div className="simulated-trading-section" style={{
        padding: "5px",
        fontFamily: "'Segoe UI', Roboto, sans-serif",
        color: "#333",
        width: "100%",
        boxSizing: "border-box"
    }}>

        <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
            gap: "10px",
            marginBottom: "2px"
        }}>
            <div>
                <label style={{ display: "block", fontSize: "18px", marginBottom: "1px", fontWeight: "600" }}>
                    Capital for Simulated Trading
                </label>
                <input
                    type="number"
                    value={capital}
                    onChange={(e) => setCapital(e.target.value)}
                    style={{
                        width: "50%",
                        padding: "8px",
                        borderRadius: "6px",
                        border: "1px solid #ddd",
                        fontSize: "13px",
                        background: "#fff"
                    }}
                    placeholder="Enter capital amount"
                />
            </div>

            <div>
                <label style={{ display: "block", fontSize: "18px", marginBottom: "2px", fontWeight: "600" }}>
                    Price Type
                </label>
                <select
                    value={priceType}
                    onChange={(e) => setPriceType(e.target.value)}
                    style={{
                        width: "50%",
                        padding: "8px",
                        borderRadius: "6px",
                        border: "1px solid #ddd",
                        fontSize: "13px",
                        background: "#fff"
                    }}
                >
                    <option value="">Select Price Type</option>
                    <option value="BidAsk">BidAsk</option>
                    <option value="LastTradedPrice">LastTradedPrice</option>
                </select>
            </div>
        </div>

        <div style={{ marginBottom: "2px" }}>
            <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "2px" }}>
                Margin Details (Leverage)
            </h3>
            <p style={{ fontSize: "12px", color: "#666", marginBottom: "2px" }}>
                Specify the leverage example 10x, 2.5x, etc. For No need to mention X in text box            </p>
            <p style={{ fontSize: "12px", color: "#666", marginBottom: "2px" }}>
                For options, Simulated Trading will take the full margin in calculation and will not give benefits for Spread Margins
            </p>
            <div style={{
                background: "#f8fafc",
                padding: "20px",
                borderRadius: "8px",
                display: "grid",
                gridTemplateColumns: "1fr repeat(3, 150px)",
                gap: "10px",
                alignItems: "center"
            }}>
                <span style={{ fontSize: "14px", fontWeight: "600" }}></span>
                <span style={{ fontSize: "14px", fontWeight: "600", textAlign: "center" }}>MIS</span>
                <span style={{ fontSize: "14px", fontWeight: "600", textAlign: "center" }}>CO/BO</span>
                <span style={{ fontSize: "14px", fontWeight: "600", textAlign: "center" }}>NRML/Delivery</span>

                {[ "equity", "futures", "options", "mcx" ].map((type) => (
                    <React.Fragment key={type}>
                        <span style={{ fontSize: "14px", fontWeight: "600" }}>
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                        </span>
                        <input
                            type="number"
                            value={leverage[ type ].mis}
                            onChange={(e) => handleLeverageChange(type, "mis", e.target.value)}
                            style={{
                                width: "100%",
                                padding: "8px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                textAlign: "center",
                                background: "#fff"
                            }}
                            min="1"
                        />
                        <input
                            type="number"
                            value={leverage[ type ].coBo}
                            onChange={(e) => handleLeverageChange(type, "coBo", e.target.value)}
                            style={{
                                width: "100%",
                                padding: "8px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                textAlign: "center",
                                background: "#fff"
                            }}
                            min="1"
                        />
                        <input
                            type="number"
                            value={leverage[ type ].nrml}
                            onChange={(e) => handleLeverageChange(type, "nrml", e.target.value)}
                            style={{
                                width: "100%",
                                padding: "8px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                textAlign: "center",
                                background: "#fff"
                            }}
                            min="1"
                        />
                    </React.Fragment>
                ))}
            </div>
        </div>

        <div style={{ marginBottom: "5px" }}>
            <h3 style={{ color: "#4661bd", fontSize: "18px", marginBottom: "5px" }}>
                Select User for Simulated Trading
            </h3>
            <select
                value={selectedUser}
                onChange={(e) => setSelectedUser(e.target.value)}
                style={{
                    width: "300px",
                    padding: "8px",
                    borderRadius: "6px",
                    border: "1px solid #ddd",
                    fontSize: "14px",
                    background: "#fff"
                }}
            >
                <option value="">Select User</option>
                {/* Add user options here as needed */}
            </select>
        </div>

        <div style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "15px"
        }}>
            <button style={{
                padding: "12px 25px",
                background: "#4661bd",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "background 0.3s ease"
            }}
                onMouseEnter={(e) => e.target.style.background = "#3750a4"}
                onMouseLeave={(e) => e.target.style.background = "#4661bd"}
            >
                Save Settings
            </button>
            <button style={{
                padding: "12px 25px",
                background: "#fff",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                transition: "all 0.3s ease"
            }}
                onMouseEnter={(e) => {
                    e.target.style.background = "#f5f5f5";
                    e.target.style.borderColor = "#ccc";
                }}
                onMouseLeave={(e) => {
                    e.target.style.background = "#fff";
                    e.target.style.borderColor = "#ddd";
                }}
            >
                Cancel
            </button>
        </div>
    </div>
);

export default SimulatedTradingSection;