* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.password-recovery-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.password-decoration-container {
  position: relative;
  width: 50%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.password-background-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: cover;
}

.password-illustration-img {
  position: relative;
  z-index: 2;
  max-width: 80%;
  max-height: 80%;
}

.password-right-container {
  width: 50%;
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.password-right-container-box {
  width: 100%;
  max-width: 400px;
  padding: 25px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.password-right-container-box-inner {
  margin-bottom: 20px;
}

.password-right-container-h2 {
  color: black;
  font-size: 32px;
  font-family: "Roboto";
  font-weight: 500;
  word-wrap: break-word;
  text-align: center;
}

.password-right-container-p {
  color: #727272;
  font-size: 16px;
  font-family: "Roboto";
  font-weight: 400;
  word-wrap: break-word;
  text-align: center;
  margin-top: 10px;
}

.password-input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
}

.password-right-container-label {
  color: #333;
  font-size: 15px;
  font-family: "Roboto";
  font-weight: 500;
  word-wrap: break-word;
}

.password-right-container-input {
  width: 100%;
  height: 40px;
  padding: 10px;
  border-radius: 10px;
  border: 1px #d9d9d9 solid;
  font-size: 15px;
  font-family: "Roboto";
  font-weight: 500;
  outline: none;
  transition: border-color 0.3s;
}

.password-right-container-input:focus {
  border-color: #5367fe;
}

.password-error-message {
  color: red;
  font-size: 12px;
  font-family: "Roboto";
  margin-top: 5px;
}

.password-spacing {
  margin-bottom: 30px;
}

.password-button-container {
  width: 100%;
  height: 50px;
  background: #5367fe;
  border-radius: 10px;
  border: 1px #3a5b22 solid;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.password-button-container:hover {
  background: #4357de;
}

.password-go-back-button {
  width: 100%;
  height: 40px;
  background: transparent;
  border: 1px solid #d9d9d9;
  border-radius: 10px;
  color: #333;
  font-size: 16px;
  font-family: "Roboto";
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.password-go-back-button:hover {
  background: #f0f0f0;
}

.password-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.password-popup-message {
  background: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  width: 300px;
}

.password-popup-text {
  margin-left: 70px;
  margin-top: 10px;
  font-family: "Roboto";
  font-size: 16px;
}

.password-popup-button {
  padding: 10px 25px;
  color: white;
  border: none;
  border-radius: 5px;
  margin-left: 125px;
  margin-top: 40px;
  cursor: pointer;
}

.password-popup-button.success {
  background-color: green;
}

.password-popup-button.failure {
  background-color: red;
}

@media (max-width: 768px) {
  .password-recovery-container {
    flex-direction: column;
  }

  .password-decoration-container {
    display: none;
  }

  .password-right-container {
    width: 100%;
    padding: 20px;
  }

  .password-right-container-box {
    max-width: 100%;
  }
}
