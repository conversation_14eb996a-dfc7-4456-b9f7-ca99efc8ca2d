import React, { useState, useRef, useEffect, useImperativeHandle, forwardRef } from "react";
import { useSelector } from "react-redux";
import "./FandOrow.css";
import { Box } from "@mui/material";
import { Delete } from "@mui/icons-material";
import Cookies from "universal-cookie";
import { fetchWithAuth } from "../../utils/api";
import Draggable from "react-draggable";

const cookies = new Cookies();

const ActionBox = ({ text, color, onClick }) => {
  return (
    <Box
      sx={{
        display: "inline-flex",
        justifyContent: "center",
        alignItems: "center",
        width: "32px",
        height: "32px",
        backgroundColor: "white",
        color: color,
        borderRadius: "4px",
        fontSize: "10px",
        fontWeight: "bolder",
        border: "2px solid black",
        textTransform: "uppercase",
        cursor: onClick ? "pointer" : "not-allowed",
        marginTop: "10px",
      }}
      onClick={onClick}
    >
      {text}
    </Box>
  );
};

const Fandorow = forwardRef(
  (
    {
      setlegsEdited,
      editPortfolio,
      portfolio,
      selectedDate,
      stock_symbol,
      setIsPortfolioEdited,
      setgotAllLTP,
      setmargin,
      isPortfolioExecuted,
      order_type,
      isCheckedPortfolio,
      predefinedStrategy
    },
    ref
  ) => {
    const tableRef = useRef();
    const expiryState = useSelector((state) => state.expiryReducer);
    const mainUser = cookies.get("USERNAME");

    const generateDateOptions = (index = 0) => {
      if (!Object.values(expiryState).includes([])) {
        const leg = legs[ index ] || { option_type: "CE" };
        const GetExpirylist = () => {
          if (stock_symbol === "NIFTY") {
            return leg.option_type === "FUT" ? expiryState.FUTNIFTY : expiryState.NIFTY;
          } else if (stock_symbol === "BANKNIFTY") {
            return leg.option_type === "FUT" ? expiryState.FUTBANKNIFTY : expiryState.BANKNIFTY;
          } else if (stock_symbol === "FINNIFTY") {
            return leg.option_type === "FUT" ? expiryState.FUTFINNIFTY : expiryState.FINNIFTY;
          }
        };

        const Expirylist = GetExpirylist();
        if (!Expirylist || Expirylist.length === 0) return null;

        const limitedExpiryList = Expirylist.slice(0, 8);
        return limitedExpiryList.map((expiry) => (
          <option
            selected={
              editPortfolio
                ? legs[ index ]?.[ "expiry_date" ] === expiry
                : legs[ index ]?.[ "expiry_date" ] === expiry
                  ? true
                  : selectedDate === expiry
                    ? true
                    : !legs[ index ]?.[ "expiry_date" ] && expiry === limitedExpiryList[ 0 ]
                      ? true
                      : false
            }
            key={`${stock_symbol}-${expiry}`}
            value={expiry}
          >
            {expiry}
          </option>
        ));
      }
      return null;
    };

    const [ currentNumber, setCurrentNumber ] = useState(1);
    const rowStyle = (index) => ({
      backgroundColor: index % 2 === 0 ? "#f2f2f2" : "#e6e6e6",
    });

    const [ legs, setlegs ] = useState([]);

    const getDefaultLeg = () => ({
      transaction_type: "BUY",
      option_type: "CE",
      ltp: "0",
      lots: 1,
      expiry_date: "",
      strike: "ATM",
      target: "None",
      limit_price: "",
      tgt_value: 0,
      trail_tgt: [ "", "", "", "" ],
      stop_loss: "None",
      sl_value: 0,
      trail_sl: [ "", "" ],
      showPopupSL1: false,
      showPopupSL: false,
      delta: "",
      theta: "",
      vega: "",
      start_time: "",
      wait_sec: "",
      wait_action: "None",
    });

    useEffect(() => {
      if (!editPortfolio && predefinedStrategy) {
        if (predefinedStrategy === "Custom") {
          setlegs([]);
          return;
        }
        let initialLegs = [];
        const defaultLeg = getDefaultLeg();

        switch (predefinedStrategy) {
          case "Short Straddle":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Short Strangle":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM+100" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM-100" },
            ];
            break;
          case "Iron Condor":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM-200" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM-100" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM+100" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM+200" },
            ];
            break;
          case "Long Butterfly":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM-100" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 2, strike: "ATM" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM+100" },
            ];
            break;
          case "Iron Butterfly":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM-100" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM+100" },
            ];
            break;
          case "Call Ratio Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM-200" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 2, strike: "ATM+200" },
            ];
            break;
          case "Put Ratio Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 2, strike: "ATM-200" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM+200" },
            ];
            break;

          // Straddles Category
          case "Long Straddle":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Long Strangle":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM+100" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM-100" },
            ];
            break;

          // Butterfly Category
          case "Short Butterfly":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM-100" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 2, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM+100" },
            ];
            break;

          // Ratio Category
          case "Call Backspread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM-200" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 2, strike: "ATM+200" },
            ];
            break;
          case "Put Backspread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 2, strike: "ATM-200" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM+200" },
            ];
            break;

          // Spreads Category
          case "Bull Call Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM+100" },
            ];
            break;
          case "Bull Put Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM+100" },
            ];
            break;
          case "Bear Call Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM-100" },
            ];
            break;
          case "Bear Put Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM-100" },
            ];
            break;

          // Iron Category
          case "Reverse Iron Butterfly":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM-100" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM+100" },
            ];
            break;
          case "Reverse Iron Condor":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM-200" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM-100" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM+100" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM+200" },
            ];
            break;

          // Calendar Category
          case "Call Calendar Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Put Calendar Spread":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Short Call Calendar":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Short Put Calendar":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM" },
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM" },
            ];
            break;

          // Naked Category
          case "Long Call":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "CE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Short Put":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "PE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Long Put":
            initialLegs = [
              { ...defaultLeg, transaction_type: "BUY", option_type: "PE", lots: 1, strike: "ATM" },
            ];
            break;
          case "Short Call":
            initialLegs = [
              { ...defaultLeg, transaction_type: "SELL", option_type: "CE", lots: 1, strike: "ATM" },
            ];
            break;

          default:
            initialLegs = [];
            break;
        }

        const updatedLegs = initialLegs.map((leg, index) => {
          const Expirylist = getExpiryListForLeg(index);
          if (Expirylist && Expirylist.length > 0) {
            return {
              ...leg,
              expiry_date: selectedDate && Expirylist.includes(selectedDate)
                ? selectedDate
                : Expirylist[ 0 ],
            };
          }
          return leg;
        });

        setlegs(updatedLegs);
        setIsPortfolioEdited(true);
      }
    }, [ predefinedStrategy, editPortfolio, selectedDate, stock_symbol ]);

    const handleDropdownChange = (index, value, field = "expiry_date") => {
      const updatedLegs = [ ...legs ];
      updatedLegs[ index ][ field ] = value;
      if (field === "expiry_date") {
        updatedLegs[ index ].ltp = "0";
      }
      setlegs(updatedLegs);
      setIsPortfolioEdited(true);
    };

    const handleInputChange = (index, value) => {
      const updatedLegs = [ ...legs ];
      updatedLegs[ index ].stop_loss = value;
      setlegs(updatedLegs);
      setIsPortfolioEdited(true);
    };

    function isDateInPast(dateString) {
      function parseDate(dateString) {
        const day = parseInt(dateString.slice(0, 2), 10);
        const monthAbbr = dateString.slice(2, 5);
        const year = parseInt(dateString.slice(5), 10);
        const monthNames = [ "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC" ];
        const month = monthNames.indexOf(monthAbbr);
        if (month === -1) throw new Error("Invalid month abbreviation");
        return new Date(year, month, day);
      }
      const givenDate = parseDate(dateString);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return givenDate < today;
    }

    useEffect(() => {
      if (editPortfolio && portfolio && portfolio.legs) {
        const updatedLegs = portfolio.legs.map((leg) => {
          if (isDateInPast(leg[ "expiry_date" ])) {
            return { ...leg, expiry_date: "" };
          }
          return { ...leg };
        });
        setlegs(updatedLegs);
      }
    }, [ portfolio, editPortfolio ]);

    const getExpiryListForLeg = (index) => {
      if (!Object.values(expiryState).includes([])) {
        const leg = legs[ index ] || { option_type: "CE" };
        if (stock_symbol === "NIFTY") {
          return leg.option_type === "FUT" ? expiryState.FUTNIFTY : expiryState.NIFTY;
        } else if (stock_symbol === "BANKNIFTY") {
          return leg.option_type === "FUT" ? expiryState.FUTBANKNIFTY : expiryState.BANKNIFTY;
        } else if (stock_symbol === "FINNIFTY") {
          return leg.option_type === "FUT" ? expiryState.FUTFINNIFTY : expiryState.FINNIFTY;
        }
      }
      return [];
    };

    const handleAddRow = () => {
      setIsPortfolioEdited(true);
      setlegs((prevLegs) => {
        const newLeg = getDefaultLeg();
        const Expirylist = getExpiryListForLeg(prevLegs.length);
        if (Expirylist && Expirylist.length > 0) {
          newLeg.expiry_date = selectedDate && Expirylist.includes(selectedDate)
            ? selectedDate
            : Expirylist[ 0 ];
        }
        return [ ...prevLegs, newLeg ];
      });

      setTimeout(() => {
        if (tableRef.current) {
          tableRef.current.scrollLeft = 0;
          tableRef.current.scrollTop = tableRef.current.scrollHeight;
        }
      }, 100);
    };

    const handleDelete = (indexToDelete) => {
      setlegs((prevLegs) => {
        if (prevLegs.length <= 1) return prevLegs; // Keep at least 1 leg if you want, or remove this check to allow 0 legs
        return prevLegs.filter((_, index) => index !== indexToDelete);
      });
      setIsPortfolioEdited(true);
    };

    const [ allStrikeValues, setAllStrikeValues ] = useState([]);
    const [ allOptionTypes, setAllOptionTypes ] = useState([]);
    const [ allExpiryDates, setAllExpiryDates ] = useState([]);

    useEffect(() => {
      const strikes = legs.map((leg) => leg.strike).join(",");
      const optionTypes = legs.map((leg) => leg[ "option_type" ]).join(",");
      const expiryDates = legs.map((leg) => leg.expiry_date).join(",");

      if (
        strikes !== allStrikeValues.join(",") ||
        optionTypes !== allOptionTypes.join(",") ||
        expiryDates !== allExpiryDates.join(",")
      ) {
        if (expiryDates[ 0 ] !== undefined) {
          setgotAllLTP(false);
          setAllStrikeValues(legs.map((leg) => leg.strike));
          setAllOptionTypes(legs.map((leg) => leg.option_type));
          setAllExpiryDates(legs.map((leg) => leg.expiry_date));
        }
      }
    }, [ legs, setgotAllLTP ]);

    useImperativeHandle(ref, () => ({
      getLegs() {
        return legs;
      },
      async getLegLTP() {
        await get_leg_ltp();
      },
      getMargin() {
        return margin;
      },
    }));

    const get_leg_ltp = async () => {
      for (let index = 0; index < allStrikeValues.length; index++) {
        if (index === 0) {
          setgotAllLTP(false);
          setmargin("0");
        }
        const atmData = {
          symbol: stock_symbol,
          strike: allStrikeValues[ index ],
          option_type: allOptionTypes[ index ],
          expiry: legs[ index ].expiry_date,
        };

        if (
          atmData.symbol !== "" &&
          atmData.strike !== "" &&
          atmData.option_type !== "" &&
          atmData.expiry !== ""
        ) {
          try {
            const response = await fetchWithAuth(`/api/get_price_details/${mainUser}`, {
              method: "POST",
              body: JSON.stringify(atmData),
            });
            const responseData = await response.json();
            if (!response.ok) {
              throw new Error(responseData.message || "Something bad happened. Please try again");
            }

            setlegs((prevLegs) => {
              const updatedLegs = [ ...prevLegs ];
              if (updatedLegs[ index ] && `${responseData[ "Price" ]}` != null) {
                if (responseData[ "Strike Price" ] !== undefined) {
                  updatedLegs[ index ][ "ltp" ] = `${responseData[ "Strike Price" ]} (${responseData[ "Price" ].toFixed(2)})`;
                } else {
                  updatedLegs[ index ][ "ltp" ] = `${responseData[ "Price" ].toFixed(2)}`;
                }
              }
              return updatedLegs;
            });
          } catch (error) {
            console.error("Error fetching LTP:", error);
          }
        }
        if (index === allStrikeValues.length - 1) {
          setgotAllLTP(true);
          let lotsize =
            stock_symbol === "NIFTY" ? 75 :
              stock_symbol === "BANKNIFTY" ? 30 :
                stock_symbol === "FINNIFTY" ? 25 : 0;
          let margin = 0;

          legs.forEach((leg) => {
            let ltp;
            if (leg[ "option_type" ] === "CE" || leg[ "option_type" ] === "PE") {
              const match = leg.ltp.match(/\(([^)]+)\)/);
              ltp = match ? parseFloat(match[ 1 ]) : 0;
            } else {
              ltp = parseFloat(leg.ltp);
            }
            const lots = parseFloat(leg[ "lots" ]);
            margin += lotsize * lots * ltp;
          });
          setmargin(margin.toFixed(2));
        }
      }
    };

    const togglePopup = (index, leg) => {
      if (leg && leg.target !== "None") {
        setlegs((prevLegs) => {
          const updatedLegs = prevLegs.map((item, i) => {
            if (i === index) {
              return { ...item, showPopupSL1: !item.showPopupSL1, showPopupSL: false };
            }
            return { ...item, showPopupSL1: false, showPopupSL: false };
          });
          return updatedLegs;
        });
      } else {
        setlegs((prevLegs) => prevLegs.map((item) => ({ ...item, showPopupSL1: false, showPopupSL: false })));
      }
    };

    const togglePopupSL = (index, leg) => {
      if (leg && leg.stop_loss !== "None") {
        setlegs((prevLegs) => {
          const updatedLegs = prevLegs.map((item, i) => {
            if (i === index) {
              return { ...item, showPopupSL: !item.showPopupSL, showPopupSL1: false };
            }
            return { ...item, showPopupSL: false, showPopupSL1: false };
          });
          return updatedLegs;
        });
      } else {
        setlegs((prevLegs) => prevLegs.map((item) => ({ ...item, showPopupSL: false, showPopupSL1: false })));
      }
    };

    const popupRef = useRef(null);

    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setlegs((prevLegs) =>
          prevLegs.map((item) => ({ ...item, showPopupSL: false, showPopupSL1: false }))
        );
      }
    };

    useEffect(() => {
      document.addEventListener("click", handleClickOutside);
      return () => document.removeEventListener("click", handleClickOutside);
    }, []);

    const [ popupValues, setPopupValues ] = useState({
      profitReaches: "",
      lockMinimumProfit: "",
      increaseInProfit: "",
      trailProfitBy: "",
    });

    const setPopupValuesForIndex = (index) => {
      if (legs && legs.length > index) {
        const { trail_tgt } = legs[ index ];
        if (trail_tgt && trail_tgt.length > 0) {
          const [ profitReaches, lockMinimumProfit, increaseInProfit, trailProfitBy ] = trail_tgt;
          setPopupValues({ profitReaches, lockMinimumProfit, increaseInProfit, trailProfitBy });
        }
      }
    };

    useEffect(() => {
      setPopupValuesForIndex();
    }, [ legs ]);

    const handleLegInputChange = (field, value) => {
      setPopupValues((prevValues) => ({ ...prevValues, [ field ]: value }));
    };

    const handleInputDelete = (index) => {
      setPopupValues({
        profitReaches: "",
        lockMinimumProfit: "",
        increaseInProfit: "",
        trailProfitBy: "",
      });
      setlegs((prev) => {
        const updatedLegs = [ ...prev ];
        updatedLegs[ index ][ "trail_tgt" ] = [ "", "", "", "" ];
        return updatedLegs;
      });
      setIsPortfolioEdited(true);
    };

    const setPopupValuesForIndexSl = (index) => {
      if (legs && legs.length > index) {
        const { trail_sl } = legs[ index ];
        if (trail_sl && trail_sl.length > 0) {
          const [ increaseInProfit, trailProfit ] = trail_sl;
          setpopupSLValues({ increaseInProfit, trailProfit });
        }
      }
    };

    const [ popupSLValues, setpopupSLValues ] = useState({
      increaseInProfit: "",
      trailProfit: "",
    });

    const handleSLInputChange = (key, value) => {
      setpopupSLValues((prevState) => ({ ...prevState, [ key ]: value }));
    };

    const handleSetTrailTGT = (index) => {
      let profitReachesValue = document.getElementById("trail_tgt_0").value;
      let lockProfitValue = document.getElementById("trail_tgt_1").value;
      let increaseInProfitValue = document.getElementById("trail_tgt_2").value;
      let trailProfitByValue = document.getElementById("trail_tgt_3").value;

      document.getElementById("profitReachesError").innerText = "";
      document.getElementById("lockProfitError").innerText = "";
      document.getElementById("increaseInProfitError").innerText = "";
      document.getElementById("trailProfitByError").innerText = "";

      if ((profitReachesValue && !lockProfitValue) || (!profitReachesValue && lockProfitValue)) {
        if (!profitReachesValue) {
          document.getElementById("profitReachesError").innerText = "Value is required.";
        } else {
          document.getElementById("lockProfitError").innerText = "Value is required.";
        }
        return;
      }

      if ((increaseInProfitValue && !trailProfitByValue) || (!increaseInProfitValue && trailProfitByValue)) {
        if (!increaseInProfitValue) {
          document.getElementById("increaseInProfitError").innerText = "Value is required.";
        } else {
          document.getElementById("trailProfitByError").innerText = "Value is required.";
        }
        return;
      }

      const updatedLegs = [ ...legs ];
      const trail_tgt = [ profitReachesValue, lockProfitValue, increaseInProfitValue, trailProfitByValue ];
      updatedLegs[ index ][ "trail_tgt" ] = trail_tgt;
      updatedLegs[ index ][ "showPopupSL1" ] = false;
      setlegs(updatedLegs);
      setIsPortfolioEdited(true);
    };

    const handleSetTrailSL = (index) => {
      let everyincreaseInProfitValue = document.getElementById("trail_sl_0").value;
      let trailProfitByValue = document.getElementById("trail_sl_1").value;

      if (everyincreaseInProfitValue !== "" && trailProfitByValue === "") {
        document.getElementById("SLincreaseInProfitError").innerText = "";
        document.getElementById("trailSLByError").innerText = "Value is required.";
        return;
      }
      if (trailProfitByValue !== "" && everyincreaseInProfitValue === "") {
        document.getElementById("trailSLByError").innerText = "";
        document.getElementById("SLincreaseInProfitError").innerText = "Value is required.";
        return;
      }

      setlegs((prev) => {
        const updatedLegs = [ ...prev ];
        let trail_sl = [ everyincreaseInProfitValue, trailProfitByValue ];
        updatedLegs[ index ][ "trail_sl" ] = trail_sl;
        updatedLegs[ index ][ "showPopupSL" ] = false;
        return updatedLegs;
      });
      setIsPortfolioEdited(true);
    };

    useEffect(() => {
      const fetchForDelta = async () => {
        try {
          const dataObjects = legs
            .map((leg) => {
              const ltpValue = leg.ltp.split(" ")[ 0 ];
              if (leg.expiry_date && leg.strike && ltpValue > 0 && stock_symbol) {
                return {
                  index_symbol: stock_symbol,
                  expiry_date: leg.expiry_date,
                  strike: leg.strike,
                  ltp: ltpValue,
                };
              }
              return null;
            })
            .filter(Boolean);
        } catch (error) { }
      };
      fetchForDelta();
    }, [ legs, stock_symbol ]);

    const handlePriceChange = (index, newPrice) => {
      setIsPortfolioEdited(true);
      setlegs((prev) => {
        const updatedLegs = [ ...prev ];
        updatedLegs[ index ].limit_price = newPrice;
        return updatedLegs;
      });
    };

    const handleWaitSec = (index, seconds) => {
      setIsPortfolioEdited(true);
      setlegs((prev) => {
        const updatedLegs = [ ...prev ];
        updatedLegs[ index ].wait_sec = seconds;
        return updatedLegs;
      });
    };

    const handleWaitActionChange = (index, action) => {
      setIsPortfolioEdited(true);
      setlegs((prev) => {
        const updatedLegs = [ ...prev ];
        updatedLegs[ index ].wait_action = action;
        return updatedLegs;
      });
    };

    const [ timerValues, setTimerValues ] = useState(Array(legs.length).fill(""));

    const getFormattedDateTime = () => {
      const date = new Date();
      const day = String(date.getDate()).padStart(2, "0");
      const months = [ "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" ];
      const month = months[ date.getMonth() ];
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${day} ${month} ${hours}:${minutes}:${seconds}`;
    };

    const handleTimerChange = (index, e) => {
      const value = e.target.value;
      setTimerValues((prevValues) => {
        const newValues = [ ...prevValues ];
        newValues[ index ] = value;
        return newValues;
      });
      setlegs((prev) => {
        const updatedLegs = [ ...prev ];
        updatedLegs[ index ].start_time = value;
        return updatedLegs;
      });
      setIsPortfolioEdited(true);
    };

    useEffect(() => {
      const initialFormattedTimes = Array(legs.length).fill(getFormattedDateTime());
      setTimerValues(initialFormattedTimes);
    }, [ legs.length ]);

    useEffect(() => {
      setTimerValues(Array(legs.length).fill(""));
    }, [ isCheckedPortfolio, legs.length ]);

    const tooltipStyle = {
      position: "absolute",
      bottom: "100%",
      left: "50%",
      transform: "translateX(-50%)",
      backgroundColor: "black",
      color: "#fff",
      padding: "5px",
      borderRadius: "4px",
      whiteSpace: "nowrap",
      fontSize: "12px",
      zIndex: 100000,
    };

    const [ isFocused, setIsFocused ] = useState(Array(legs.length).fill(false));

    const handleFocus = (index) => {
      const updatedFocusState = [ ...isFocused ];
      updatedFocusState[ index ] = true;
      setIsFocused(updatedFocusState);
    };

    const handleBlur = (index) => {
      const updatedFocusState = [ ...isFocused ];
      updatedFocusState[ index ] = false;
      setIsFocused(updatedFocusState);
    };

    const handleOptionTypeChange = (index, newOptionType) => {
      setlegs((prev) => {
        const updatedLegs = [ ...prev ];
        updatedLegs[ index ][ "option_type" ] = newOptionType;
        if (newOptionType === "FUT") {
          updatedLegs[ index ][ "strike" ] = "ATM";
          updatedLegs[ index ][ "ltp" ] = "0";
        }
        const Expirylist = getExpiryListForLeg(index);
        if (Expirylist && Expirylist.length > 0) {
          if (!editPortfolio && selectedDate && Expirylist.includes(selectedDate)) {
            updatedLegs[ index ][ "expiry_date" ] = selectedDate;
          } else if (!updatedLegs[ index ][ "expiry_date" ] || !Expirylist.includes(updatedLegs[ index ][ "expiry_date" ])) {
            updatedLegs[ index ][ "expiry_date" ] = Expirylist[ 0 ];
          }
        } else {
          updatedLegs[ index ][ "expiry_date" ] = "";
        }
        return updatedLegs;
      });
      setIsPortfolioEdited(true);
    };

    const generateRows = () => {
      return legs.map((leg, index) => (
        <tr key={leg.id || index} style={rowStyle(index)} className="input">
          <td>{currentNumber + index}</td>
          <td>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {legs[ index ][ "transaction_type" ] === "BUY" && (
                <ActionBox
                  text="Buy"
                  color="green"
                  onClick={() => {
                    if (!isPortfolioExecuted) {
                      setIsPortfolioEdited(true);
                      setlegs((prev) => {
                        const updatedLegs = [ ...prev ];
                        updatedLegs[ index ][ "transaction_type" ] = "SELL";
                        return updatedLegs;
                      });
                    }
                  }}
                />
              )}
              {legs[ index ][ "transaction_type" ] === "SELL" && (
                <ActionBox
                  text="Sell"
                  color="red"
                  onClick={() => {
                    if (!isPortfolioExecuted) {
                      setIsPortfolioEdited(true);
                      setlegs((prev) => {
                        const updatedLegs = [ ...prev ];
                        updatedLegs[ index ][ "transaction_type" ] = "BUY";
                        return updatedLegs;
                      });
                    }
                  }}
                />
              )}
              {legs[ index ][ "option_type" ] === "CE" && (
                <ActionBox
                  text="CE"
                  color="blue"
                  onClick={() => {
                    if (!isPortfolioExecuted) {
                      handleOptionTypeChange(index, "PE");
                    }
                  }}
                />
              )}
              {legs[ index ][ "option_type" ] === "PE" && (
                <ActionBox
                  text="PE"
                  color="green"
                  onClick={() => {
                    if (!isPortfolioExecuted) {
                      handleOptionTypeChange(index, "FUT");
                    }
                  }}
                />
              )}
              {legs[ index ][ "option_type" ] === "FUT" && (
                <ActionBox
                  text="FUT"
                  color="orange"
                  onClick={() => {
                    if (!isPortfolioExecuted) {
                      handleOptionTypeChange(index, "CE");
                    }
                  }}
                />
              )}
              <Delete
                onClick={
                  !isPortfolioExecuted
                    ? () => {
                      setIsPortfolioEdited(true);
                      handleDelete(index);
                    }
                    : null
                }
                sx={{
                  color: isPortfolioExecuted ? "gray" : "red",
                  fontSize: "30px",
                  border: "2px solid black",
                  cursor: isPortfolioExecuted ? "not-allowed" : "pointer",
                  marginTop: "5px",
                  transform: "translateY(2px)",
                }}
              />
            </Box>
          </td>
          <td style={{ padding: 0, cursor: "pointer" }}>
            <input
              disabled={isPortfolioExecuted}
              type="text"
              className="number1"
              value={legs[ index ][ "ltp" ]}
              style={{ textAlign: "center", borderRadius: "3px" }}
            />
          </td>
          <td>
            <input type="checkbox" style={{ cursor: "pointer" }} />
          </td>
          <td style={{ padding: 0, cursor: "pointer" }}>
            <input
              type="number"
              max="100"
              disabled={isPortfolioExecuted}
              value={leg.lots}
              onInput={(e) => {
                e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                const value = parseInt(e.target.value);
                if (value <= 0) e.target.value = 1;
                if (value > 100) e.target.value = 100;
              }}
              onChange={(e) => {
                setIsPortfolioEdited(true);
                setlegs((prev) => {
                  const updatedLegs = [ ...prev ];
                  updatedLegs[ index ][ "lots" ] = !(e.target.value > 0)
                    ? ""
                    : e.target.value > 100
                      ? 100
                      : e.target.value;
                  return updatedLegs;
                });
              }}
              className="portfolioLots"
              style={{ textAlign: "center", borderRadius: "3px" }}
            />
          </td>
          {(order_type === "LIMIT" || order_type === "SL_LIMIT") && (
            <td>
              <input
                type="number"
                value={leg.limit_price}
                onInput={(e) => {
                  let value = e.target.value.replace(/[eE+\-]/g, "");
                  e.target.value = value;
                }}
                onChange={(e) => {
                  const newValue = parseFloat(e.target.value);
                  if (newValue < 1) {
                    handlePriceChange(index, "1");
                  } else {
                    handlePriceChange(index, e.target.value);
                  }
                }}
                disabled={isPortfolioExecuted}
                min="1"
                step="any"
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "5px",
                  padding: "8px",
                  width: "70px",
                  height: "38px",
                  marginBottom: "0px",
                }}
              />
            </td>
          )}
          {order_type === "SL_LIMIT" && (
            <td>
              <input
                type="number"
                value={leg.wait_sec}
                onInput={(e) => {
                  let value = e.target.value.replace(/[eE+\-]/g, "");
                  e.target.value = value;
                }}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  handleWaitSec(index, inputValue);
                }}
                disabled={isPortfolioExecuted}
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "5px",
                  padding: "8px",
                  width: "118px",
                  height: "40px",
                  marginBottom: "0px",
                }}
                placeholder="Enter seconds"
              />
            </td>
          )}
          {order_type === "SL_LIMIT" && (
            <td>
              <select
                value={leg.wait_action || "None"}
                onChange={(e) => handleWaitActionChange(index, e.target.value)}
                disabled={isPortfolioExecuted}
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "5px",
                  padding: "8px",
                  width: "100%",
                  backgroundColor: isPortfolioExecuted ? "#f9f9f9" : "#fff",
                }}
              >
                <option value="None">None</option>
                <option value="CancelOrder">CancelOrder</option>
                <option value="ConvertToMarket">ConvertToMarket</option>
              </select>
            </td>
          )}
          <td>
            <select
              disabled={isPortfolioExecuted}
              className="expiry-dropdown"
              style={{ width: "105px" }}
              value={leg[ "expiry_date" ] || ""}
              onChange={(e) => handleDropdownChange(index, e.target.value)}
            >
              <option value="" disabled>Select</option>
              {generateDateOptions(index)}
            </select>
          </td>
          <td>
            <select
              disabled={isPortfolioExecuted || legs[ index ][ "option_type" ] === "FUT"}
              className="custom-dropdown"
              style={{ cursor: "pointer" }}
              value={legs[ index ][ "strike" ]}
              onChange={(e) => {
                setIsPortfolioEdited(true);
                setlegs((prev) => {
                  const updatedLegs = [ ...prev ];
                  updatedLegs[ index ][ "strike" ] = e.target.value;
                  updatedLegs[ index ][ "ltp" ] = "0";
                  return updatedLegs;
                });
              }}
            >
              <option value="" disabled>Select</option>
              {Array.from({ length: 11 }, (_, i) => (i - 5) * 100).map((value) => (
                <option
                  key={value}
                  value={value === 0 ? "ATM" : value > 0 ? `ATM+${value}` : `ATM${value}`}
                >
                  {value === 0 ? "ATM" : value > 0 ? `ATM+${value}` : `ATM${value}`}
                </option>
              ))}
            </select>
          </td>
          <td>
            <select
              className="custom-dropdown"
              style={{ cursor: "pointer" }}
              disabled={isPortfolioExecuted}
              value={leg.target || ""}
              onChange={(e) => {
                setIsPortfolioEdited(true);
                handleDropdownChange(index, e.target.value, "target");
              }}
            >
              <option value="None">None</option>
              <option value="Premium">Premium</option>
              <option value="Underlying">Underlying</option>
              <option value="Strike">Strike</option>
              <option value="Absolute Premium">Absolute Premium</option>
              <option value="Delta">Delta</option>
              <option value="Theta">Theta</option>
            </select>
          </td>
          <td style={{ padding: 0 }}>
            <input
              type="number"
              className="number1"
              onInput={(e) => {
                e.target.value = e.target.value.replace(/[eE+\-]/g, "");
              }}
              value={leg.tgt_value}
              style={{ textAlign: "center", borderRadius: "3px" }}
              onChange={(e) => {
                setIsPortfolioEdited(true);
                setlegs((prev) => {
                  const updatedLegs = [ ...prev ];
                  updatedLegs[ index ][ "tgt_value" ] = e.target.value;
                  return updatedLegs;
                });
              }}
              disabled={!leg.target || leg.target === "None" || isPortfolioExecuted}
            />
          </td>
          <td style={{ padding: 0 }}>
            <input
              type="text"
              className="number1"
              readOnly
              disabled={isPortfolioExecuted}
              style={{ textAlign: "center", borderRadius: "3px" }}
              value={leg[ "trail_tgt" ].every((value) => value === "") ? "" : leg.trail_tgt.join("~")}
            />
            <span
              className="arrow down"
              onClick={(e) => {
                e.stopPropagation();
                togglePopup(index, legs);
                setPopupValuesForIndex(index);
              }}
              style={{ cursor: leg.target !== "None" ? "pointer" : "not-allowed" }}
            ></span>
            {leg.showPopupSL1 && leg.target !== "None" && (
              <Draggable nodeRef={popupRef}>
                <div
                  ref={popupRef}
                  className="popupContainer"
                  style={{
                    position: "fixed",
                    bottom: "6%",
                    right: "10%",
                    transform: "translate(-20%, 10%)",
                    backgroundColor: "#fff",
                    border: "1px solid #ccc",
                    padding: "20px",
                    width: "400px",
                    zIndex: 1000,
                    borderRadius: "5px",
                    boxShadow: "0px 5px 15px rgba(0, 0, 0, 0.3)",
                  }}
                >
                  <div className="popupContent" style={{ border: "1px solid #d3d3d3", padding: "8px", borderRadius: "5px" }}>
                    <h4 style={{ marginLeft: "-245px", fontFamily: "roboto", fontSize: "14" }}>Profit Locking</h4>
                    <div style={{ display: "flex", marginTop: "10px", marginRight: "10px" }}>
                      <div className="input-box">
                        <span className="SLT" style={{ display: "flex", textAlign: "start", color: "#4661bd", fontFamily: "roboto", fontSize: "14" }}>
                          If Profit Reaches
                        </span>
                        <input
                          className="number1"
                          type="number"
                          onInput={(e) => { e.target.value = e.target.value.replace(/[eE+\-]/g, ""); }}
                          disabled={isPortfolioExecuted}
                          value={popupValues.profitReaches}
                          id="trail_tgt_0"
                          style={{ display: "flex", border: "none", width: "160px", borderBottom: "1px solid #000", outline: "none", boxSizing: "border-box", padding: "10px" }}
                          onChange={(e) => { setIsPortfolioEdited(true); handleLegInputChange("profitReaches", e.target.value); }}
                        />
                        <p id="profitReachesError" style={{ color: "red", height: "18px", marginTop: "4px", marginLeft: "-45px" }}></p>
                      </div>
                      <div className="input-box" style={{ width: "30px", height: "5px", marginLeft: "20px" }}>
                        <span className="SLT" style={{ display: "flex", alignItems: "center", color: "#4661bd", fontFamily: "roboto", fontSize: "14" }}>
                          Lock Minimum Profit At
                        </span>
                        <input
                          className="number1"
                          type="number"
                          onInput={(e) => { e.target.value = e.target.value.replace(/[eE+\-]/g, ""); }}
                          disabled={isPortfolioExecuted}
                          value={popupValues.lockMinimumProfit}
                          id="trail_tgt_1"
                          style={{ display: "flex", border: "none", width: "160px", borderBottom: "1px solid #000", outline: "none", boxSizing: "border-box", padding: "10px" }}
                          onChange={(e) => { setIsPortfolioEdited(true); handleLegInputChange("lockMinimumProfit", e.target.value); }}
                        />
                        <p id="lockProfitError" style={{ color: "red", height: "18px", marginTop: "4px" }}></p>
                      </div>
                    </div>
                  </div>
                  <div className="popupContent" style={{ border: "1px solid #d3d3d3", padding: "8px", borderRadius: "5px", marginTop: "10px" }}>
                    <h4 style={{ marginLeft: "-245px", fontFamily: "roboto", fontSize: "14" }}>Profit Trailing</h4>
                    <div style={{ display: "flex", marginTop: "10px", marginRight: "10px" }}>
                      <div className="input-box">
                        <span className="SLT" style={{ display: "flex", color: "#4661bd", fontFamily: "roboto", fontSize: "14", textAlign: "start" }}>
                          Then Every Increase <br /> In Profit By
                        </span>
                        <input
                          className="number1"
                          type="number"
                          onInput={(e) => { e.target.value = e.target.value.replace(/[eE+\-]/g, ""); }}
                          disabled={isPortfolioExecuted}
                          value={popupValues.increaseInProfit}
                          id="trail_tgt_2"
                          style={{ display: "flex", border: "none", width: "160px", borderBottom: "1px solid #000", outline: "none", boxSizing: "border-box", padding: "10px" }}
                          onChange={(e) => { setIsPortfolioEdited(true); handleLegInputChange("increaseInProfit", e.target.value); }}
                        />
                        <p id="increaseInProfitError" style={{ color: "red", height: "18px", marginLeft: "-45px", marginTop: "4px" }}></p>
                      </div>
                      <div className="input-box" style={{ width: "30px", height: "5px", marginLeft: "20px" }}>
                        <span className="SLT" style={{ display: "flex", alignItems: "center", color: "#4661bd", fontFamily: "roboto", fontSize: "14", marginTop: "19px" }}>
                          Trail Profit By
                        </span>
                        <input
                          className="number1"
                          type="number"
                          onInput={(e) => { e.target.value = e.target.value.replace(/[eE+\-]/g, ""); }}
                          disabled={isPortfolioExecuted}
                          value={popupValues.trailProfitBy}
                          id="trail_tgt_3"
                          style={{ display: "flex", border: "none", width: "160px", borderBottom: "1px solid #000", outline: "none", boxSizing: "border-box", padding: "10px" }}
                          onChange={(e) => { setIsPortfolioEdited(true); handleLegInputChange("trailProfitBy", e.target.value); }}
                        />
                        <p id="trailProfitByError" style={{ color: "red", height: "18px", marginTop: "4px" }}></p>
                      </div>
                    </div>
                  </div>
                  <div style={{ display: "flex" }}>
                    <div style={{ fontFamily: "roboto", fontSize: "12px", marginTop: "30px", color: "orange" }}>
                      VALUES SHOULD BE IN RUPEES ONLY
                    </div>
                    <button
                      style={{
                        marginTop: "20px",
                        padding: "4px 8px",
                        marginLeft: "5px",
                        backgroundColor: "#28A745",
                        color: "white",
                        border: "none",
                        borderRadius: "5px",
                        cursor: "pointer",
                        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                        transition: "background-color 0.3s",
                      }}
                      onClick={(e) => { handleSetTrailTGT(index); }}
                    >
                      OK
                    </button>
                    <button
                      style={{
                        marginTop: "20px",
                        padding: "3px 2px",
                        marginLeft: "-5px",
                        backgroundColor: "#DC3545",
                        color: "white",
                        border: "none",
                        borderRadius: "5px",
                        cursor: "pointer",
                        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                        transition: "background-color 0.3s",
                        fontWeight: "normal",
                        width: "80px",
                      }}
                      onClick={() => handleInputDelete(index)}
                    >
                      DELETE
                    </button>
                    <button
                      onClick={togglePopup}
                      style={{
                        marginTop: "20px",
                        padding: "3px 2px",
                        marginLeft: "-5px",
                        backgroundColor: "#007bff",
                        color: "white",
                        border: "none",
                        borderRadius: "5px",
                        cursor: "pointer",
                        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                        transition: "background-color 0.3s",
                        width: "65px",
                      }}
                    >
                      CLOSE
                    </button>
                  </div>
                  <div style={{ fontFamily: "roboto", fontSize: "12px", marginTop: "5px", color: "#4661bd", marginLeft: "-55px" }}>
                    LOCKING AND TRAILING CAN BE USED INDEPENDENTLY
                  </div>
                  <div style={{ fontFamily: "roboto", fontSize: "12px", marginTop: "5px", color: "green", marginLeft: "-10px" }}>
                    TGT/ SL ON PER LOT BASIS IF TICKED WILL BE APPLICABLE HERE
                  </div>
                </div>
              </Draggable>
            )}
          </td>
          <td>
            <select
              className="custom-dropdown"
              style={{ cursor: "pointer" }}
              disabled={isPortfolioExecuted}
              value={leg.stop_loss || ""}
              onChange={(e) => {
                setIsPortfolioEdited(true);
                handleInputChange(index, e.target.value);
              }}
            >
              <option value="None">None</option>
              <option value="Premium">Premium</option>
              <option value="Underlying">Underlying</option>
              <option value="Strike">Strike</option>
              <option value="Absolute Premium">Absolute Premium</option>
              <option value="Delta">Delta</option>
              <option value="Theta">Theta</option>
            </select>
          </td>
          <td style={{ padding: 0 }}>
            <input
              type="number"
              onInput={(e) => { e.target.value = e.target.value.replace(/[eE+\-]/g, ""); }}
              className="number1"
              value={leg.sl_value}
              style={{ textAlign: "center", borderRadius: "3px" }}
              onChange={(e) => {
                setIsPortfolioEdited(true);
                setlegs((prev) => {
                  const updatedLegs = [ ...prev ];
                  updatedLegs[ index ][ "sl_value" ] = e.target.value;
                  return updatedLegs;
                });
              }}
              disabled={!leg.stop_loss || leg.stop_loss === "None" || isPortfolioExecuted}
            />
          </td>
          <td style={{ padding: 0 }}>
            <input
              type="text"
              className="number1"
              style={{ textAlign: "center", borderRadius: "3px" }}
              value={leg.trail_sl.every((value) => value === "") ? "" : leg.trail_sl.join("~")}
              disabled={!leg.stop_loss || leg.stop_loss === "None" || isPortfolioExecuted}
            />
            <span
              className="arrow down"
              onClick={(e) => {
                e.stopPropagation();
                togglePopupSL(index, legs);
                setPopupValuesForIndexSl(index);
              }}
              style={{ cursor: leg.stop_loss !== "None" ? "pointer" : "not-allowed" }}
            ></span>
            {leg.showPopupSL && leg.stop_loss !== "None" && (
              <Draggable nodeRef={popupRef}>
                <div
                  ref={popupRef}
                  className="popupContainer"
                  style={{
                    position: "fixed",
                    bottom: "6%",
                    right: "5%",
                    zIndex: 1000,
                    transform: "translate(-20%, 10%)",
                    backgroundColor: "#fff",
                    border: "1px solid #ccc",
                    padding: "20px",
                    width: "400px",
                    borderRadius: "5px",
                    boxShadow: "0px 5px 15px rgba(0, 0, 0, 0.3)",
                  }}
                >
                  <div className="popupContent" style={{ border: "1px solid #d3d3d3", padding: "8px", borderRadius: "5px" }}>
                    <h4 style={{ marginLeft: "-265px", fontFamily: "roboto", fontSize: "14" }}>SL Trailing</h4>
                    <div style={{ display: "flex", marginTop: "10px", marginRight: "10px" }}>
                      <div className="input-box">
                        <span className="SLT" style={{ display: "flex", textAlign: "start", color: "#4661bd", fontFamily: "roboto", fontSize: "14" }}>
                          For Every Increase <br /> In Profit By
                        </span>
                        <input
                          className="number1"
                          type="number"
                          onInput={(e) => { e.target.value = e.target.value.replace(/[eE+\-]/g, ""); }}
                          disabled={isPortfolioExecuted}
                          value={popupSLValues.increaseInProfit}
                          id="trail_sl_0"
                          style={{ display: "flex", border: "none", width: "160px", borderBottom: "1px solid #000", outline: "none", boxSizing: "border-box", padding: "10px" }}
                          onChange={(e) => { setIsPortfolioEdited(true); handleSLInputChange("increaseInProfit", e.target.value); }}
                        />
                        <p id="SLincreaseInProfitError" style={{ color: "red", height: "18px", marginLeft: "-45px", marginTop: "4px" }}></p>
                      </div>
                      <div className="input-box" style={{ marginTop: "18px", width: "30px", height: "5px", marginLeft: "35px" }}>
                        <span className="SLT" style={{ display: "flex", alignItems: "center", color: "#4661bd", fontFamily: "roboto", fontSize: "14" }}>
                          Trail SL By
                        </span>
                        <input
                          className="number1"
                          type="number"
                          onInput={(e) => { e.target.value = e.target.value.replace(/[eE+\-]/g, ""); }}
                          disabled={isPortfolioExecuted}
                          value={popupSLValues.trailProfit}
                          id="trail_sl_1"
                          style={{ display: "flex", border: "none", width: "150px", borderBottom: "1px solid #000", outline: "none", boxSizing: "border-box", padding: "10px" }}
                          onChange={(e) => { setIsPortfolioEdited(true); handleSLInputChange("trailProfit", e.target.value); }}
                        />
                        <p id="trailSLByError" style={{ color: "red", height: "18px", marginTop: "4px" }}></p>
                      </div>
                    </div>
                  </div>
                  <div style={{ display: "flex" }}>
                    <div style={{ fontFamily: "roboto", fontSize: "12px", marginTop: "30px", color: "orange" }}>
                      VALUES CAN BE IN POINTS OR IN PERCENTAGE
                    </div>
                    <button
                      style={{
                        marginTop: "20px",
                        padding: "4px 8px",
                        marginLeft: "4px",
                        backgroundColor: "#28A745",
                        color: "white",
                        border: "none",
                        borderRadius: "5px",
                        cursor: "pointer",
                        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                        transition: "background-color 0.3s",
                      }}
                      onClick={() => { handleSetTrailSL(index); }}
                    >
                      OK
                    </button>
                    <button
                      onClick={togglePopupSL}
                      style={{
                        marginTop: "20px",
                        padding: "4px 8px",
                        marginLeft: "3px",
                        backgroundColor: "#007bff",
                        color: "white",
                        border: "none",
                        borderRadius: "5px",
                        cursor: "pointer",
                        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                        transition: "background-color 0.3s",
                      }}
                    >
                      CLOSE
                    </button>
                  </div>
                </div>
              </Draggable>
            )}
          </td >
          <td style={{ padding: 0 }}>
            <input
              type="number"
              onInput={(e) => {
                e.target.value = e.target.value.replace(/[eE+\-]/g, "");
                const value = e.target.value;
                const sanitizedValue = value.replace(/[^0-9]/g, "");
                e.target.value = sanitizedValue;
              }}
              className="number1"
              defaultValue={0}
              style={{ textAlign: "center", borderRadius: "3px" }}
              disabled={!leg.stop_loss || leg.stop_loss === "None"}
            />
          </td>
          <td>
            <select className="custom-dropdown" style={{ cursor: "pointer" }}>
              <option value="option1">None</option>
              <option value="option2">Premium</option>
              <option value="option3">Underlying</option>
              <option value="option4">Strike</option>
              <option value="option5">Ads Olute Premium</option>
              <option value="option6">Delta</option>
              <option value="option7">Theta</option>
            </select>
          </td>
          <td style={{ padding: "0" }}>
            <input
              type="text"
              pattern="[A-Za-z]+"
              title="Only letters are allowed"
              onInput={(e) => {
                const value = e.target.value;
                const sanitizedValue = value.replace(/[^A-Za-z]/g, "");
                e.target.value = sanitizedValue;
              }}
            />
          </td>
          <td>
            <select className="custom-dropdown" style={{ cursor: "pointer" }}>
              <option value="option1">None</option>
              <option value="option2">Premium</option>
              <option value="option3">Underlying</option>
              <option value="option4">Strike</option>
              <option value="option5">Ads Olute Premium</option>
              <option value="option6">Delta</option>
              <option value="option7">Theta</option>
            </select>
          </td>
          <td style={{ padding: "0" }}>
            <input
              type="text"
              pattern="[A-Za-z]+"
              title="Only letters are allowed"
              onInput={(e) => {
                const value = e.target.value;
                const sanitizedValue = value.replace(/[^A-Za-z]/g, "");
                e.target.value = sanitizedValue;
              }}
            />
          </td>
          <td style={{ padding: 0, position: "relative", width: "500px" }}>
            <input
              type="text"
              value={legs[ index ][ "start_time" ] ?? ""}
              onChange={(e) => handleTimerChange(index, e)}
              onFocus={() => handleFocus(index)}
              onBlur={() => handleBlur(index)}
              placeholder=""
              style={{ textAlign: "center", padding: "8px" }}
              disabled={isPortfolioExecuted}
            />
            {isFocused[ index ] && (
              <div style={tooltipStyle}>
                {isCheckedPortfolio ? "DD MMM HH:MM:SS" : "HH:MM:SS"}
              </div>
            )}
          </td>
          <td style={{ padding: 0 }}>
            <input type="text" value={legs[ index ][ "delta" ]} style={{ textAlign: "center" }} disabled />
          </td>
          <td style={{ padding: 0 }}>
            <input type="text" disabled value={legs[ index ][ "theta" ]} style={{ textAlign: "center" }} />
          </td>
          <td style={{ padding: 0 }}>
            <input type="text" disabled value={legs[ index ][ "vega" ]} style={{ textAlign: "center" }} />
          </td>
        </tr >
      ));
    };

    return (
      <div>
        <div className="tablecontainer" ref={tableRef}>
          <table className="table">
            <thead className="thead" style={{ position: "sticky", top: "0", zIndex: "20" }}>
              <tr>
                <th>ID</th>
                <th>Action</th>
                <th>LTP</th>
                <th>Idle</th>
                <th>Lots</th>
                {(order_type === "LIMIT" || order_type === "SL_LIMIT") && <th>Price</th>}
                {order_type === "SL_LIMIT" && <th>Wait Seconds</th>}
                {order_type === "SL_LIMIT" && <th>On Wait Action</th>}
                <th>Expiry</th>
                <th>Strike</th>
                <th>Target</th>
                <th>TGT Value</th>
                <th>Trail TGT</th>
                <th>Stoploss</th>
                <th>SL Value</th>
                <th>Trail SL</th>
                <th>SL Wait</th>
                <th>On Target</th>
                <th>TGT portfolio <br /> Name/Count</th>
                <th>On Stoploss</th>
                <th>Sl portfolio <br /> Name/Count</th>
                <th>Start Time</th>
                <th>Delta</th>
                <th>Theta</th>
                <th>Vega</th>
              </tr>
            </thead>
            <tbody className="tabletbody1">{generateRows()}</tbody>
          </table>
        </div>
        <div className="frame-13773">
          <div className="ce-pe">
            <button
              disabled={isPortfolioExecuted}
              className="ce-pe-span2"
              onClick={handleAddRow}
              style={{
                cursor: isPortfolioExecuted ? "not-allowed" : "pointer",
                backgroundColor: isPortfolioExecuted ? "grey" : "",
                color: isPortfolioExecuted ? "black" : "",
              }}
            >
              + CE/PE
            </button>
          </div>
        </div>
      </div>
    );
  }
);

Fandorow.displayName = "Fandorow";

export default Fandorow;