import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Cookies from "universal-cookie";
import { useDispatch, useSelector } from "react-redux";
import { setAuth } from "../../store/slices/auth.js";
import { RotatingLines } from "react-loader-spinner";
import { fetchWithAuth } from "../../utils/api";

import "./Login.css";
import googleIcon from "../../assets/google.png";
import appleIcon from "../../assets/apple.png";

const cookies = new Cookies();

const Login = () => {
  const dispatch = useDispatch();
  const { isAuthenticated } = useSelector((state) => state.authReducer);
  const navigate = useNavigate();

  const [ formData, setFormData ] = useState({ username: "", password: "" });
  const [ showPassword, setShowPassword ] = useState(false);
  const [ isSubmitting, setIsSubmitting ] = useState(false);

  const updateFormData = (field) => (e) =>
    setFormData((prev) => ({
      ...prev,
      [ field ]: field === "username" ? e.target.value.toLowerCase() : e.target.value,
    }));

  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);

  const notify = {
    error: (message, field) =>
      toast.error(field ? `${field.charAt(0).toUpperCase() + field.slice(1)} Error: ${message}` : message),
    success: (message) => toast.success(message),
  };

  const validateForm = () => {
    const errors = [];
    if (!formData.username) errors.push({ field: "username", message: "*Field is required" });
    if (!formData.password) errors.push({ field: "password", message: "*Field is required" });
    errors.forEach(({ field, message }) => notify.error(message, field));
    return errors.length === 0;
  };

  const handleLoginResponse = (responseData) => {
    const cookieOptions = { path: "/" };
    const userData = {
      TOKEN: responseData.access_token,
      USERNAME: responseData.username,
      expiry: responseData.subscription_end_date,
      subscription_type: responseData.subscription_type,
      number_users: responseData.num_of_users,
      session_id: responseData.session_id,
      message: responseData.message,
    };

    Object.entries(userData).forEach(([ key, value ]) => cookies.set(key, value, cookieOptions));
    localStorage.removeItem("subscriptionAlertShown");

    dispatch(setAuth({ isAuthenticated: true }));
    navigate(responseData.subscription_type === "Expired" ? "/Subscription" : "/UserProfiles");
    notify.success("Login successful!");
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      const response = await fetchWithAuth(`api/login`, {
        method: "POST",
        body: JSON.stringify(formData),
      });

      const responseData = await response.json();
      setIsSubmitting(false);

      if (!response.ok) {
        throw new Error(
          response.status === 401
            ? "Invalid username or password. Please try again."
            : "You already have an active session."
        );
      }

      handleLoginResponse(responseData);
    } catch (error) {
      setIsSubmitting(false);
      notify.error(error.message);
    }
  };

  const handleKeyDown = (event) => event.key === "Enter" && handleSubmit();

  return (
    <>
      <ToastContainer />
      <div className="body">
        <div className="right-container">
          <div className="right-container__box">
            <div className="right-container-box">
              <h2 className="right-container__h2">Welcome back!</h2>
              <p className="right-container__p">Enter your credentials to access your account</p>
            </div>

            <div className="input-container">
              <label htmlFor="username" className="right-container__label">
                Username
              </label>
              <input
                type="text"
                className="right-container__input1"
                id="username"
                placeholder="Your username"
                value={formData.username}
                onChange={updateFormData("username")}
                onKeyDown={handleKeyDown}
              />

              <label htmlFor="password" className="right-container__label">
                Password
              </label>
              <div className="password-input-container">
                <input
                  type={showPassword ? "text" : "password"}
                  className="right-container__input2"
                  id="password"
                  placeholder="Your password"
                  value={formData.password}
                  onChange={updateFormData("password")}
                  onKeyDown={handleKeyDown}
                />
                <div className="password-toggle" onClick={togglePasswordVisibility}>
                  {showPassword ? "👁️" : "🔒"}
                </div>
              </div>
              <div className="forgot-password-link">
                <Link to="/passwordRecovery">Forgot Password</Link>
              </div>
            </div>

            <button className="btn" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? (
                <div className="btn-loading">
                  <RotatingLines strokeColor="#fff" strokeWidth="5" animationDuration="0.75" width="24" visible />
                  <span>Logging in...</span>
                </div>
              ) : (
                "Login"
              )}
            </button>

            <div className="line-with-text">
              <div className="grey-line"></div>
              <div className="text-between-lines">or</div>
              <div className="grey-line"></div>
            </div>

            <div className="social-sign-in">
              <div className="SignInWithGoogle">
                <img src={googleIcon} alt="Google" />
                Sign in with Google
              </div>
              <div className="SignInWithApple">
                <img src={appleIcon} alt="Apple" />
                Sign in with Apple
              </div>
            </div>

            <p className="right-container__bottom-text">
              New on our platform? <strong><Link to="/Register">Create account</Link></strong>
            </p>
          </div>
        </div>

        <div className="decoration-container">
          <svg width="400" className="illustration-svg" height="100%" viewBox="0 0 652 656" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d_823_10410)">
              <path d="M325.705 645.84C502.273 645.84 645.41 563.202 645.41 461.262C645.41 359.322 502.273 276.684 325.705 276.684C149.137 276.684 6 359.322 6 461.262C6 563.202 149.137 645.84 325.705 645.84Z" fill="#F5F5F5" />
            </g>
            <g filter="url(#filter1_d_823_10410)">
              <path d="M375.878 590.525C414.063 590.525 445.018 572.656 445.018 550.612C445.018 528.569 414.063 510.699 375.878 510.699C337.692 510.699 306.737 528.569 306.737 550.612C306.737 572.656 337.692 590.525 375.878 590.525Z" fill="#E0E0E0" />
            </g>
            <g filter="url(#filter2_d_823_10410)">
              <path d="M339.944 415.455L155.014 522.242C149.808 525.243 140.729 524.876 134.712 521.4L69.0628 483.493C63.0613 480.017 62.403 474.781 67.6084 471.78L252.539 364.993C257.744 361.992 266.823 362.359 272.84 365.835L338.489 403.742C344.491 407.218 345.149 412.454 339.944 415.455Z" fill="#E0E0E0" />
            </g>
            <path d="M539.084 452.75L553.644 461.216C553.644 461.216 546.295 437.746 550.368 414.153C551.914 405.242 555.144 400.818 556.293 398.445C557.441 396.072 566.214 381.343 558.482 374.561C550.75 367.779 532.608 381.267 533.419 417.628C533.833 436.291 539.069 452.765 539.069 452.765L539.084 452.75Z" fill="#B3CAFF" />
            <path opacity="0.5" d="M539.084 452.75L553.644 461.216C553.644 461.216 546.295 437.746 550.368 414.153C551.914 405.242 555.144 400.818 556.293 398.445C557.441 396.072 566.214 381.343 558.482 374.561C550.75 367.779 532.608 381.267 533.419 417.628C533.833 436.291 539.069 452.765 539.069 452.765L539.084 452.75Z" fill="black" />
            <path d="M545.851 457.343C545.575 457.343 545.33 457.174 545.223 456.914C545.162 456.761 539.666 441.466 539.956 416.94C540.247 392.26 553.215 379.093 553.353 378.971C553.613 378.71 554.026 378.71 554.287 378.971C554.547 379.231 554.547 379.644 554.287 379.905C554.164 380.027 541.564 392.842 541.288 416.955C540.998 441.221 546.433 456.317 546.479 456.47C546.601 456.807 546.433 457.19 546.08 457.312C546.004 457.343 545.927 457.358 545.851 457.358V457.343Z" fill="white" />
            <path d="M550.379 459.348L574.447 473.495C574.447 473.495 585.47 471.872 584.628 461.492C583.801 451.127 579.315 448.677 584.888 444.115C590.461 439.552 591.9 439.261 591.64 434.791C591.38 430.32 588.364 428.376 586.955 426.6C585.547 424.824 581.597 421.119 584.368 415.73C587.139 410.341 590.905 405.135 585.317 402.67C581.979 401.201 576.391 403.038 572.671 406.988C568.95 410.922 565.888 413.77 562.382 414.673C558.861 415.592 551.528 415.669 551.773 421.716C552.018 427.764 553.594 429.876 552.125 432.556C549.354 437.623 537.519 440.348 550.379 459.318V459.348Z" fill="#B3CAFF" />
            <path opacity="0.2" d="M550.379 459.348L574.447 473.495C574.447 473.495 585.47 471.872 584.628 461.492C583.801 451.127 579.315 448.677 584.888 444.115C590.461 439.552 591.9 439.261 591.64 434.791C591.38 430.32 588.364 428.376 586.955 426.6C585.547 424.824 581.597 421.119 584.368 415.73C587.139 410.341 590.905 405.135 585.317 402.67C581.979 401.201 576.391 403.038 572.671 406.988C568.95 410.922 565.888 413.77 562.382 414.673C558.861 415.592 551.528 415.669 551.773 421.716C552.018 427.764 553.594 429.876 552.125 432.556C549.354 437.623 537.519 440.348 550.379 459.318V459.348Z" fill="black" />
            <path d="M560.178 465.778C559.842 465.778 559.551 465.518 559.52 465.166C559.489 464.814 556.871 428.927 581.796 405.365C582.057 405.12 582.485 405.12 582.73 405.395C582.975 405.656 582.975 406.084 582.7 406.329C558.234 429.447 560.791 464.706 560.821 465.074C560.852 465.441 560.576 465.763 560.209 465.793C560.194 465.793 560.178 465.793 560.148 465.793L560.178 465.778Z" fill="white" />
            <path d="M564.495 435.021C564.159 435.021 563.868 434.761 563.837 434.409C563.133 424.993 556.595 420.155 556.519 420.109C556.228 419.895 556.151 419.482 556.366 419.191C556.58 418.9 556.993 418.823 557.284 419.038C557.575 419.252 564.404 424.274 565.138 434.317C565.169 434.685 564.893 434.991 564.526 435.021C564.511 435.021 564.495 435.021 564.48 435.021H564.495Z" fill="white" />
            <path d="M562.72 441.145C562.567 441.145 562.399 441.083 562.276 440.976C562.001 440.731 561.986 440.318 562.231 440.042C562.491 439.751 568.768 432.969 583.282 434.959C583.649 435.005 583.894 435.342 583.848 435.71C583.802 436.077 583.466 436.322 583.098 436.276C569.334 434.393 563.272 440.869 563.21 440.93C563.088 441.068 562.904 441.145 562.72 441.145Z" fill="white" />
            <path d="M97.7885 432.126L90.5927 447.359C90.5927 447.359 83.3357 423.874 66.5865 406.742C60.2635 400.281 55.0887 398.474 52.7922 397.157C50.4957 395.841 34.91 388.706 37.4362 378.739C39.947 368.757 62.5447 369.584 82.509 399.99C92.7514 415.591 97.8038 432.126 97.8038 432.126H97.7885Z" fill="#B3CAFF" />
            <path opacity="0.5" d="M97.7885 432.126L90.5927 447.359C90.5927 447.359 83.3357 423.874 66.5865 406.742C60.2635 400.281 55.0887 398.474 52.7922 397.157C50.4957 395.841 34.91 388.706 37.4362 378.739C39.947 368.757 62.5447 369.584 82.509 399.99C92.7514 415.591 97.8038 432.126 97.8038 432.126H97.7885Z" fill="black" />
            <path d="M94.435 439.873C94.1441 439.873 93.8685 439.674 93.792 439.384C93.746 439.23 89.6583 423.721 75.6495 403.91C61.7327 384.221 44.0955 380.807 43.9271 380.777C43.575 380.715 43.33 380.363 43.3912 380.011C43.4525 379.659 43.7893 379.414 44.1567 379.475C44.3405 379.506 62.4829 382.981 76.7212 403.145C90.8831 423.185 95.0168 438.909 95.0627 439.062C95.1546 439.414 94.9402 439.782 94.5881 439.873C94.5269 439.873 94.4809 439.889 94.4197 439.889L94.435 439.873Z" fill="white" />
            <path d="M92.2299 443.99L80.4411 469.297C80.4411 469.297 70.4436 474.212 65.2535 465.194C60.0481 456.176 62.3599 451.629 55.1795 451.032C47.9991 450.435 46.6518 451.017 44.34 447.174C42.0282 443.347 43.3908 440.04 43.5592 437.774C43.7123 435.508 44.8605 430.226 39.5173 427.363C34.1741 424.5 28.1266 422.341 31.3264 417.151C33.2249 414.043 38.8896 412.39 44.1869 413.523C49.4842 414.656 53.6179 415.253 57.032 414.013C60.4462 412.772 66.5243 408.669 69.77 413.798C73.0004 418.927 72.8933 421.545 75.6338 422.923C80.7932 425.526 92.0768 421.055 92.2605 443.974L92.2299 443.99Z" fill="#B3CAFF" />
            <path opacity="0.2" d="M92.2299 443.99L80.4411 469.297C80.4411 469.297 70.4436 474.212 65.2535 465.194C60.0481 456.176 62.3599 451.629 55.1795 451.032C47.9991 450.435 46.6518 451.017 44.34 447.174C42.0282 443.347 43.3908 440.04 43.5592 437.774C43.7123 435.508 44.8605 430.226 39.5173 427.363C34.1741 424.5 28.1266 422.341 31.3264 417.151C33.2249 414.043 38.8896 412.39 44.1869 413.523C49.4842 414.656 53.6179 415.253 57.032 414.013C60.4462 412.772 66.5243 408.669 69.77 413.798C73.0004 418.927 72.8933 421.545 75.6338 422.923C80.7932 425.526 92.0768 421.055 92.2605 443.974L92.2299 443.99Z" fill="black" />
            <path d="M87.4368 454.952C87.2071 454.952 86.9928 454.829 86.8703 454.615C86.6866 454.309 68.7891 423.811 35.5356 418.652C35.1682 418.59 34.9232 418.253 34.9845 417.901C35.0457 417.534 35.3825 417.289 35.7347 417.35C69.6312 422.602 87.8348 453.635 88.0186 453.957C88.2023 454.278 88.0951 454.676 87.7736 454.86C87.6664 454.921 87.5593 454.952 87.4521 454.952H87.4368Z" fill="white" />
            <path d="M66.4312 432.079C66.2016 432.079 65.9872 431.956 65.8648 431.757C60.7818 423.061 63.5529 415.054 63.6601 414.717C63.7826 414.38 64.15 414.196 64.5022 414.319C64.839 414.441 65.0227 414.824 64.9002 415.161C64.8696 415.237 62.221 422.938 66.9977 431.083C67.1814 431.405 67.0743 431.803 66.7681 431.987C66.6609 432.048 66.5537 432.079 66.4312 432.079Z" fill="white" />
            <path d="M51.7488 443.715C51.5651 443.715 51.3966 443.638 51.2589 443.501C51.0139 443.225 51.0292 442.812 51.3048 442.567C62.1137 432.692 71.1466 434.728 71.5141 434.805C71.8662 434.896 72.0805 435.249 72.004 435.601C71.9274 435.953 71.5753 436.182 71.2079 436.091C71.116 436.075 62.4505 434.177 52.1928 443.531C52.0703 443.654 51.9019 443.7 51.7488 443.7V443.715Z" fill="white" />
            <path d="M92.3971 123.736C92.3971 114.658 97.2351 106.268 105.104 101.736L266.656 8.45164C271.433 5.69582 277.22 8.28323 278.736 13.1365C277.863 9.35493 275.506 6.03265 272.091 3.99641L271.51 3.65958C267.483 1.27121 262.905 0.00047775 258.221 0.00047775C256.797 -0.0148323 255.342 0.337299 253.964 1.13342L92.4124 94.4024C84.5584 98.9342 79.7051 107.324 79.7051 116.403V465.763C79.7051 472.163 86.6405 476.174 92.1828 472.974L92.3971 472.852V123.736Z" fill="#37474F" />
            <path d="M266.427 387.023L104.875 480.292C99.3324 483.492 92.397 479.496 92.397 473.081V123.736C92.397 114.657 97.235 106.267 105.104 101.735L266.656 8.45123C272.198 5.25143 279.134 9.24735 279.134 15.6623V365.023C279.134 374.102 274.296 382.492 266.427 387.023Z" fill="#455A64" />
            <path d="M92.3971 473.096V123.735C92.3971 119.862 93.2851 116.111 94.908 112.743L82.415 105.011C80.6696 108.487 79.7051 112.391 79.7051 116.417V468.702C79.7051 471.519 81.1902 474.137 83.6091 475.576L90.0547 479.419C92.29 480.751 94.8314 481.455 97.4341 481.455H100.619C96.3165 481.394 92.3971 477.934 92.3971 473.127V473.096Z" fill="#263238" />
            <path d="M260.869 42.0427V335.69C260.869 343.712 256.598 351.107 249.647 355.118L106.988 437.486C105.84 438.145 104.63 438.451 103.451 438.466C105.105 442.294 109.897 444.208 113.877 441.896L256.537 359.528C263.472 355.516 267.759 348.106 267.759 340.099V49.3609C267.759 45.2425 264.528 42.2723 260.869 42.0273V42.0427Z" fill="#37474F" />
            <path d="M103.451 438.451C104.63 438.436 105.839 438.129 106.988 437.471L249.647 355.103C256.582 351.092 260.869 343.681 260.869 335.674V42.0425C261.543 42.0884 258.726 41.8588 256.736 43.007L114.076 125.375C107.141 129.386 102.854 136.797 102.854 144.804V435.542C102.854 436.614 103.084 437.609 103.466 438.512C103.466 438.512 103.466 438.481 103.451 438.466V438.451Z" fill="#B3CAFF" />
            <g opacity="0.5">
              <path d="M103.451 438.45C104.63 438.435 105.839 438.128 106.988 437.47L249.647 355.102C256.582 351.091 260.869 343.681 260.869 335.673V42.0415C261.543 42.0874 258.726 41.8578 256.736 43.006L114.076 125.374C107.141 129.385 102.854 136.796 102.854 144.803V435.541C102.854 436.613 103.084 437.608 103.466 438.511C103.466 438.511 103.466 438.48 103.451 438.465V438.45Z" fill="white" />
            </g>
            <path d="M163.162 82.3237C163.162 83.9619 162.014 85.9522 160.59 86.779C159.166 87.5904 158.018 86.9321 158.018 85.2939C158.018 83.6557 159.166 81.6654 160.59 80.8387C162.014 80.0119 163.162 80.6856 163.162 82.3237Z" fill="#263238" />
            <path d="M172.715 76.8112C172.715 78.4493 171.567 80.4396 170.143 81.2664C168.72 82.0778 167.571 81.4195 167.571 79.7813C167.571 78.1431 168.72 76.1528 170.143 75.3261C171.567 74.5146 172.715 75.173 172.715 76.8112Z" fill="#263238" />
            <path d="M211.281 57.506L180.202 75.4494C178.977 76.1537 177.982 75.4035 177.982 73.7653C177.982 72.1271 178.977 70.2287 180.202 69.5091L211.281 51.5657C212.506 50.8614 213.501 51.6116 213.501 53.2498C213.501 54.888 212.506 56.7864 211.281 57.506Z" fill="#263238" />
            <path d="M206.029 398.047C207.882 398.047 209.091 400.022 209.091 403.068C209.091 407.86 206.091 413.632 202.554 415.668L187.688 424.242C186.892 424.701 186.157 424.931 185.468 424.931C183.615 424.931 182.406 422.956 182.406 419.909C182.406 415.117 185.407 409.345 188.943 407.309L203.809 398.736C204.606 398.276 205.34 398.047 206.029 398.047ZM206.029 396.776C205.157 396.776 204.192 397.051 203.182 397.633L188.316 406.207C184.35 408.488 181.135 414.627 181.135 419.909C181.135 423.844 182.911 426.187 185.468 426.187C186.341 426.187 187.305 425.911 188.316 425.329L203.182 416.756C207.147 414.474 210.362 408.335 210.362 403.053C210.362 399.118 208.586 396.776 206.029 396.776Z" fill="#263238" />
            <path d="M123.904 165.381V494.533C123.904 500.488 126.093 504.5 129.446 505.816L129.538 505.847H129.553L143.776 513.655L143.7 500.994L142.123 159.686L127.104 152C125.113 156.257 123.904 160.987 123.904 165.381Z" fill="#E6E6E6" />
            <path d="M144.542 514.941L129.14 506.505C125.328 504.928 123.123 500.473 123.123 494.517V165.382C123.123 161.049 124.287 156.18 126.384 151.679L126.721 150.959L142.858 159.212V159.671L144.435 500.978L144.527 514.941H144.542ZM129.921 505.173L143.011 512.338L142.934 500.978L141.357 160.146L127.471 153.042C125.665 157.145 124.685 161.493 124.685 165.366V494.517C124.685 499.891 126.568 503.841 129.737 505.081L129.936 505.173H129.921Z" fill="#E6E6E6" />
            <path d="M127.088 152.002L142.107 159.687L302.878 66.1581L313.61 59.9116L298.775 51.5523L298.346 51.3074C295.988 49.8682 292.88 49.9601 289.466 51.9351L137.284 139.8C133.181 142.157 129.522 146.72 127.072 152.002H127.088Z" fill="#E0E0E0" />
            <path d="M142.108 160.452C141.986 160.452 141.863 160.421 141.756 160.375L126.737 152.69C126.369 152.506 126.216 152.062 126.385 151.679C129.003 146.061 132.83 141.483 136.918 139.14L289.1 51.2759C292.621 49.2397 296.051 49.01 298.745 50.6482C298.791 50.6788 298.822 50.6942 298.853 50.7248L299.159 50.9085L313.979 59.2525C314.224 59.3903 314.362 59.6352 314.362 59.9108C314.362 60.1864 314.224 60.4467 313.979 60.5845L142.476 160.36C142.353 160.437 142.231 160.467 142.093 160.467L142.108 160.452ZM128.099 151.664L142.077 158.814L312.065 59.9261L297.965 51.9649C297.965 51.9649 297.888 51.919 297.842 51.8883C295.653 50.6329 292.82 50.8932 289.866 52.6079L137.683 140.472C134.024 142.585 130.564 146.642 128.099 151.679V151.664Z" fill="#E0E0E0" />
            <path d="M138.646 173.649V501.315C138.646 506.474 140.285 510.194 142.918 511.925L143.775 512.399C146.057 513.486 148.92 513.241 152.043 511.45L225.608 468.98L304.225 421.656C311.62 417.385 317.637 407.831 317.637 397.987V70.3362C317.637 65.3451 316.105 61.7319 313.625 59.91C313.197 59.6038 312.753 59.3282 312.263 59.1445C310.027 58.1953 307.241 58.4709 304.225 60.2009L152.043 149.55C148.093 151.816 144.556 154.664 142.122 159.686C139.963 164.08 138.646 169.04 138.646 173.664V173.649Z" fill="#FAFAFA" />
            <path d="M304.206 421.657C272.897 440.335 191.432 488.96 151.106 513.241C145.824 516.426 139.103 512.614 139.103 506.444V169.53C139.103 161.738 143.236 154.511 149.973 150.576L304.941 59.7572C310.223 56.6645 316.868 60.4767 316.868 66.5855V399.365C316.868 408.505 312.06 416.972 304.206 421.657Z" fill="#FAFAFA" />
            <path d="M303.825 420.999C279.237 435.666 254.665 450.333 230.092 465.03C215.547 473.726 201.003 482.423 186.458 491.134C179.814 495.115 173.169 499.095 166.54 503.076C163.631 504.821 160.737 506.566 157.828 508.312C155.149 509.919 152.301 512.216 149.331 513.242C144.417 514.941 139.992 511.022 139.87 506.168C139.854 505.388 139.87 504.607 139.87 503.811V194.899C139.87 186.678 139.854 178.441 139.87 170.22C139.885 162.121 143.804 155.109 150.801 150.959C154.353 148.862 157.92 146.78 161.487 144.698C178.175 134.915 194.863 125.131 211.551 115.364C231.332 103.774 251.097 92.1841 270.878 80.5944C282.131 73.9958 293.353 67.3512 304.637 60.8138C308.127 58.7929 312.521 58.9919 314.956 62.6357C315.936 64.1208 316.119 65.7742 316.119 67.4737C316.119 70.4132 316.119 73.3527 316.119 76.277V398.049C316.119 407.464 312.001 415.946 303.841 420.968C302.998 421.488 303.764 422.805 304.606 422.285C313.118 417.049 317.65 408.123 317.65 398.248V90.9746C317.65 82.9675 317.742 74.9756 317.65 66.9684C317.574 59.6962 310.24 55.8227 304.055 59.3746C300.319 61.5334 296.629 63.7227 292.909 65.912C275.762 75.9554 258.615 86.0142 241.467 96.0576C221.518 107.754 201.569 119.451 181.605 131.133C171.026 137.334 160.4 143.488 149.852 149.75C142.518 154.098 138.385 161.646 138.354 170.128C138.354 172.884 138.354 175.639 138.354 178.395V504.852C138.354 505.372 138.339 505.908 138.354 506.429C138.538 512.201 144.08 516.61 149.76 514.711C152.654 513.747 155.379 511.573 157.981 510.011C160.936 508.235 163.906 506.459 166.861 504.668C173.475 500.687 180.105 496.722 186.718 492.757C215.67 475.41 244.652 458.095 273.618 440.794C283.937 434.624 294.272 428.47 304.591 422.315C305.433 421.81 304.667 420.493 303.825 420.999Z" fill="#EBEBEB" />
            <path d="M149.975 150.562L304.943 59.743C310.225 56.6504 316.87 60.4626 316.87 66.5713V81.1924H316.824L139.104 183.801V169.532C139.104 161.739 143.238 154.512 149.975 150.578V150.562Z" fill="#EBEBEB" />
            <path d="M256.453 230.214C272.168 202.996 272.168 173.576 256.453 164.503C240.739 155.43 215.261 170.14 199.546 197.359C183.831 224.577 183.831 253.997 199.546 263.07C215.26 272.143 240.739 257.433 256.453 230.214Z" fill="#B3CAFF" />
            <path opacity="0.5" d="M256.453 230.214C272.168 202.996 272.168 173.576 256.453 164.503C240.739 155.43 215.261 170.14 199.546 197.359C183.831 224.577 183.831 253.997 199.546 263.07C215.26 272.143 240.739 257.433 256.453 230.214Z" fill="white" />
            <path d="M220.77 213.963C219.775 213.963 218.964 213.151 218.964 212.156V200.26C218.964 195.009 222.531 188.824 227.078 186.19C229.589 184.736 232.039 184.567 233.983 185.685C235.927 186.803 236.999 189.023 236.999 191.916V203.812C236.999 204.807 236.188 205.619 235.193 205.619C234.197 205.619 233.386 204.807 233.386 203.812V191.916C233.386 190.385 232.942 189.252 232.176 188.808C231.411 188.364 230.201 188.548 228.885 189.313C225.455 191.288 222.562 196.295 222.562 200.26V212.156C222.562 213.151 221.75 213.963 220.755 213.963H220.77Z" fill="#37474F" />
            <path d="M242.573 196.156C241.868 195.82 240.98 195.881 240.016 196.447L215.948 210.349C214.984 210.9 214.096 211.865 213.392 213.013C212.442 214.559 211.83 216.458 211.83 218.203V239.04C211.83 240.556 212.289 241.658 213.024 242.209C213.162 242.316 213.3 242.393 213.438 242.454C214.127 242.76 214.984 242.684 215.933 242.148L240.001 228.246C242.266 226.93 244.104 223.424 244.104 220.392V199.555C244.104 197.81 243.491 196.616 242.542 196.172L242.573 196.156Z" fill="#B3CAFF" />
            <path d="M230.721 217.867L233.048 224.542L224.888 229.258L226.924 221.021L227.215 219.857C225.837 219.903 224.888 218.832 224.888 216.964C224.888 216.244 225.025 215.494 225.27 214.744C225.929 212.769 227.337 210.84 228.96 209.906C231.211 208.604 233.033 209.661 233.033 212.263C233.033 214.147 232.068 216.305 230.706 217.867V217.913L230.721 217.867Z" fill="#455A64" />
            <path d="M226.924 221.005L227.215 219.842C225.837 219.888 224.888 218.816 224.888 216.948C224.888 216.228 225.025 215.478 225.27 214.728L230.721 217.882L233.048 224.557L226.939 221.02L226.924 221.005Z" fill="#37474F" />
            <path d="M287.354 290.847L168.625 359.391C163.71 362.223 159.729 359.222 159.729 352.67V334.68C159.729 328.128 163.71 320.518 168.625 317.686L287.354 249.143C292.269 246.31 296.249 249.311 296.249 255.864V273.853C296.249 280.406 292.269 288.015 287.354 290.847Z" fill="#B3CAFF" />
            <path d="M189.996 316.062H187.7V335.093H189.996V316.062Z" fill="#FAFAFA" />
            <path d="M196.492 319.859L180.011 329.374L181.159 331.363L197.64 321.848L196.492 319.859Z" fill="#FAFAFA" />
            <path d="M183.579 321.224L182.431 323.212L194.086 329.941L195.234 327.952L183.579 321.224Z" fill="#FAFAFA" />
            <path d="M193.67 314.892L182.012 335.085L184.001 336.234L195.659 316.04L193.67 314.892Z" fill="#FAFAFA" />
            <path d="M229.142 294.75H226.845V313.78H229.142V294.75Z" fill="#FAFAFA" />
            <path d="M235.636 298.533L219.155 308.048L220.304 310.037L236.784 300.521L235.636 298.533Z" fill="#FAFAFA" />
            <path d="M222.74 299.906L221.591 301.895L233.246 308.624L234.394 306.635L222.74 299.906Z" fill="#FAFAFA" />
            <path d="M232.818 293.565L221.159 313.758L223.148 314.907L234.807 294.713L232.818 293.565Z" fill="#FAFAFA" />
            <path d="M268.276 273.424H265.98V292.455H268.276V273.424Z" fill="#FAFAFA" />
            <path d="M274.794 277.206L258.313 286.722L259.461 288.711L275.942 279.195L274.794 277.206Z" fill="#FAFAFA" />
            <path d="M261.892 278.579L260.744 280.567L272.398 287.296L273.546 285.307L261.892 278.579Z" fill="#FAFAFA" />
            <path d="M271.97 272.262L260.312 292.456L262.3 293.604L273.959 273.411L271.97 272.262Z" fill="#FAFAFA" />
            <path d="M290.891 315.618C292.483 315.618 293.187 317.991 293.187 320.349V338.338C293.187 343.804 289.819 350.372 285.823 352.683L167.094 421.227C166.343 421.655 165.654 421.885 165.088 421.885C163.496 421.885 162.792 419.512 162.792 417.154V399.165C162.792 393.699 166.16 387.131 170.156 384.819L288.885 316.276C289.635 315.847 290.324 315.618 290.891 315.618ZM290.891 312.556C289.804 312.556 288.61 312.893 287.354 313.627L168.625 382.171C163.71 385.003 159.729 392.612 159.729 399.165V417.154C159.729 422.038 161.934 424.947 165.088 424.947C166.175 424.947 167.369 424.61 168.625 423.875L287.354 355.332C292.269 352.5 296.249 344.891 296.249 338.338V320.349C296.249 315.465 294.045 312.556 290.891 312.556Z" fill="#B3CAFF" />
            <path d="M199.895 385.756C199.385 386.05 198.881 386.192 198.381 386.18C197.883 386.165 197.417 386.012 196.983 385.721C196.549 385.426 196.177 385.008 195.864 384.467C195.551 383.925 195.374 383.391 195.333 382.864C195.293 382.333 195.384 381.849 195.606 381.412C195.829 380.971 196.179 380.613 196.656 380.338C196.931 380.179 197.23 380.068 197.551 380.004C197.873 379.941 198.203 379.949 198.543 380.029C198.881 380.106 199.213 380.275 199.538 380.537C199.864 380.799 200.169 381.176 200.454 381.67L200.653 382.014L196.577 384.367L196.172 383.665L199.421 381.789C199.249 381.49 199.036 381.259 198.781 381.093C198.529 380.927 198.255 380.839 197.959 380.829C197.665 380.818 197.371 380.898 197.077 381.067C196.754 381.254 196.52 381.496 196.376 381.793C196.234 382.087 196.173 382.397 196.194 382.725C196.215 383.052 196.309 383.362 196.477 383.653L196.748 384.122C196.978 384.521 197.243 384.82 197.541 385.018C197.84 385.212 198.155 385.31 198.486 385.312C198.816 385.311 199.145 385.216 199.473 385.026C199.687 384.903 199.862 384.762 200 384.603C200.138 384.44 200.236 384.262 200.292 384.067C200.346 383.871 200.355 383.661 200.318 383.437L201.23 383.204C201.301 383.518 201.297 383.832 201.219 384.147C201.139 384.459 200.988 384.754 200.766 385.033C200.543 385.308 200.253 385.55 199.895 385.756ZM202.212 379.725L204.18 383.135L203.31 383.638L200.037 377.969L200.878 377.483L201.39 378.369L201.463 378.326C201.43 377.962 201.498 377.614 201.668 377.283C201.836 376.95 202.135 376.66 202.563 376.412C202.947 376.191 203.328 376.076 203.707 376.067C204.084 376.056 204.444 376.163 204.787 376.388C205.129 376.61 205.439 376.963 205.718 377.445L207.797 381.047L206.926 381.55L204.881 378.007C204.624 377.561 204.308 377.281 203.933 377.166C203.557 377.049 203.167 377.107 202.764 377.34C202.485 377.5 202.272 377.704 202.122 377.951C201.976 378.196 201.906 378.471 201.914 378.775C201.922 379.078 202.021 379.395 202.212 379.725ZM208.581 373.036L209.007 373.774L206.07 375.47L205.644 374.732L208.581 373.036ZM205.716 372.88L206.587 372.377L209.706 377.78C209.848 378.026 209.99 378.19 210.133 378.271C210.276 378.349 210.416 378.377 210.554 378.353C210.692 378.326 210.825 378.275 210.953 378.201C211.049 378.146 211.125 378.095 211.181 378.05C211.235 378.002 211.279 377.964 211.311 377.935L211.94 378.615C211.894 378.672 211.824 378.741 211.731 378.825C211.64 378.91 211.513 379 211.35 379.094C211.104 379.236 210.833 379.322 210.535 379.353C210.241 379.382 209.953 379.333 209.673 379.206C209.396 379.077 209.162 378.848 208.971 378.519L205.716 372.88ZM215.548 376.729C215.002 377.045 214.461 377.196 213.926 377.184C213.391 377.167 212.892 377.003 212.426 376.691C211.962 376.376 211.562 375.927 211.227 375.347C210.891 374.766 210.702 374.193 210.658 373.628C210.615 373.059 210.712 372.541 210.95 372.072C211.189 371.599 211.564 371.215 212.076 370.92C212.371 370.75 212.691 370.63 213.036 370.563C213.38 370.495 213.735 370.504 214.099 370.589C214.461 370.671 214.817 370.853 215.166 371.134C215.515 371.414 215.842 371.819 216.148 372.348L216.361 372.717L211.991 375.24L211.557 374.487L215.041 372.476C214.856 372.156 214.627 371.907 214.354 371.73C214.084 371.552 213.79 371.457 213.473 371.447C213.157 371.435 212.842 371.521 212.528 371.702C212.181 371.903 211.93 372.162 211.776 372.481C211.623 372.795 211.558 373.128 211.58 373.479C211.603 373.831 211.704 374.162 211.884 374.475L212.174 374.977C212.421 375.405 212.705 375.725 213.024 375.938C213.345 376.146 213.683 376.251 214.038 376.253C214.392 376.252 214.744 376.15 215.096 375.947C215.325 375.815 215.513 375.664 215.661 375.493C215.809 375.319 215.914 375.127 215.974 374.919C216.032 374.708 216.042 374.482 216.002 374.243L216.98 373.993C217.056 374.33 217.052 374.667 216.968 375.004C216.882 375.339 216.721 375.655 216.483 375.953C216.243 376.249 215.932 376.508 215.548 376.729ZM219.152 374.491L215.879 368.823L216.72 368.337L217.215 369.193L217.274 369.159C217.215 368.819 217.271 368.483 217.441 368.152C217.61 367.821 217.865 367.558 218.205 367.362C218.268 367.325 218.349 367.28 218.447 367.227C218.544 367.174 218.619 367.136 218.671 367.112L219.182 367.998C219.148 368.008 219.074 368.036 218.96 368.082C218.847 368.125 218.732 368.179 218.616 368.246C218.341 368.405 218.128 368.605 217.979 368.846C217.83 369.082 217.751 369.338 217.742 369.612C217.734 369.882 217.804 370.145 217.952 370.401L220.023 373.988L219.152 374.491Z" fill="#B3CAFF" />
            <path d="M465.624 111.183C465.624 113.403 467.201 116.082 469.161 117.2C471.12 118.302 472.697 117.414 472.697 115.194C472.697 112.974 471.12 110.295 469.161 109.177C467.201 108.075 465.624 108.963 465.624 111.183Z" fill="#455A64" />
            <path d="M418.207 125.451C415.497 125.451 412.496 124.563 409.403 122.817L398.181 116.464C397.814 116.249 397.676 115.79 397.89 115.423C398.105 115.055 398.564 114.933 398.931 115.132L410.154 121.485C414.992 124.226 419.493 124.67 422.815 122.726C426.061 120.827 427.867 116.831 427.867 111.457C427.867 105.517 429.934 101.062 433.67 98.8725C437.482 96.6526 442.519 97.0812 447.862 100.113L466.005 110.386C466.372 110.6 466.51 111.059 466.296 111.427C466.081 111.794 465.622 111.917 465.254 111.718L447.112 101.445C442.274 98.7041 437.773 98.2601 434.451 100.204C431.19 102.103 429.398 106.099 429.398 111.473C429.398 117.413 427.331 121.868 423.596 124.058C421.988 124.992 420.166 125.451 418.207 125.451Z" fill="#455A64" />
            <path d="M393.559 113.019C393.559 114.81 394.845 116.984 396.422 117.888C397.999 118.776 399.285 118.056 399.285 116.265C399.285 114.473 397.999 112.299 396.422 111.396C394.845 110.508 393.559 111.228 393.559 113.019Z" fill="#455A64" />
            <path d="M465.624 245.006C465.624 247.226 467.201 249.906 469.161 251.023C471.12 252.126 472.697 251.238 472.697 249.018C472.697 246.798 471.12 244.118 469.161 243.001C467.201 241.898 465.624 242.786 465.624 245.006Z" fill="#455A64" />
            <path d="M465.761 246.677C465.638 246.677 465.501 246.646 465.378 246.585L447.236 236.312C436.626 230.295 427.991 215.628 427.991 203.594C427.991 191.56 419.708 177.98 409.527 172.209L398.167 165.778C397.799 165.564 397.662 165.105 397.876 164.737C398.09 164.37 398.55 164.247 398.917 164.446L410.277 170.877C420.887 176.893 429.522 191.56 429.522 203.594C429.522 215.628 437.805 229.208 447.986 234.98L466.128 245.253C466.496 245.467 466.634 245.927 466.419 246.294C466.281 246.539 466.021 246.677 465.761 246.677Z" fill="#455A64" />
            <path d="M393.559 162.057C393.559 163.848 394.845 166.022 396.422 166.925C397.999 167.813 399.285 167.094 399.285 165.302C399.285 163.511 397.999 161.337 396.422 160.434C394.845 159.546 393.559 160.265 393.559 162.057Z" fill="#455A64" />
            <path d="M574.645 96.745L499.657 54.2749C497.513 53.0348 495.584 53.1266 494.206 54.2749L487.73 58.7607L492.155 64.2264V143.272C492.155 148.799 495.523 155.229 499.657 157.618L572.777 199.016L573.114 204.911L579.943 200.226C581.305 199.2 582.147 197.179 582.147 194.423V111.121C582.147 105.594 578.779 99.164 574.645 96.7756V96.745Z" fill="#B3CAFF" />
            <path opacity="0.3" d="M574.645 96.745L499.657 54.2749C497.513 53.0348 495.584 53.1266 494.206 54.2749L487.73 58.7607L492.155 64.2264V143.272C492.155 148.799 495.523 155.229 499.657 157.618L572.777 199.016L573.114 204.911L579.943 200.226C581.305 199.2 582.147 197.179 582.147 194.423V111.121C582.147 105.594 578.779 99.164 574.645 96.7756V96.745Z" fill="black" />
            <path d="M568.261 204.452L493.272 161.982C489.123 159.593 485.771 153.163 485.771 147.636V64.334C485.771 58.8071 489.139 56.2656 493.272 58.654L568.261 101.124C572.41 103.513 575.763 109.943 575.763 115.47V198.772C575.763 204.299 572.395 206.84 568.261 204.452Z" fill="#B3CAFF" />
            <path d="M552.507 132.372C552.17 132.372 551.864 132.142 551.772 131.806C550.211 125.972 547.669 120.185 544.424 115.087C544.194 114.735 544.301 114.26 544.653 114.031C545.005 113.801 545.48 113.908 545.71 114.26C549.032 119.496 551.635 125.421 553.242 131.407C553.349 131.821 553.104 132.234 552.706 132.341C552.645 132.357 552.569 132.372 552.507 132.372Z" fill="white" />
            <path d="M554.24 147.941C554.24 147.941 554.209 147.941 554.194 147.941C553.765 147.911 553.459 147.543 553.474 147.13C553.535 146.303 553.551 145.461 553.551 144.588C553.551 142.078 553.321 139.444 552.862 136.78C552.785 136.367 553.076 135.969 553.49 135.892C553.918 135.831 554.301 136.107 554.378 136.52C554.852 139.276 555.082 141.986 555.082 144.588C555.082 145.492 555.051 146.38 554.99 147.237C554.959 147.635 554.622 147.957 554.224 147.957L554.24 147.941Z" fill="white" />
            <path d="M507.404 122.526C507.006 122.526 506.685 122.22 506.639 121.822C506.516 120.49 506.47 119.173 506.47 117.887C506.47 110.462 508.476 104.322 512.104 100.617C512.395 100.311 512.885 100.311 513.191 100.617C513.498 100.908 513.498 101.398 513.191 101.704C509.838 105.134 507.986 110.89 507.986 117.902C507.986 119.143 508.047 120.413 508.154 121.699C508.185 122.113 507.879 122.495 507.465 122.526C507.435 122.526 507.419 122.526 507.404 122.526Z" fill="white" />
            <path d="M541.255 110.141C541.041 110.141 540.811 110.049 540.658 109.865C537.443 105.931 533.983 102.899 530.385 100.863C525.302 97.9848 520.526 97.4336 516.591 99.2555C516.208 99.4392 515.749 99.2555 515.58 98.8881C515.397 98.5053 515.58 98.046 515.948 97.8776C520.357 95.8261 525.608 96.4078 531.135 99.5464C534.902 101.674 538.5 104.828 541.837 108.916C542.097 109.238 542.052 109.728 541.73 109.988C541.592 110.11 541.408 110.156 541.24 110.156L541.255 110.141Z" fill="white" />
            <path d="M512.67 123.123C512.257 123.123 511.92 122.802 511.905 122.388C511.889 121.914 511.874 121.439 511.874 120.964C511.874 115.529 513.206 111.089 515.732 108.135C516.008 107.813 516.498 107.767 516.819 108.043C517.141 108.318 517.187 108.793 516.911 109.114C514.63 111.794 513.42 115.882 513.42 120.949C513.42 121.393 513.42 121.852 513.451 122.296C513.466 122.725 513.145 123.077 512.716 123.093C512.716 123.093 512.701 123.093 512.685 123.093L512.67 123.123Z" fill="white" />
            <path d="M518.688 107.354C518.428 107.354 518.183 107.231 518.03 106.986C517.815 106.619 517.922 106.159 518.275 105.93C520.188 104.751 522.393 104.276 524.781 104.506C525.21 104.552 525.516 104.919 525.47 105.348C525.424 105.761 525.042 106.067 524.628 106.037C522.561 105.838 520.709 106.236 519.086 107.231C518.964 107.308 518.826 107.338 518.688 107.338V107.354Z" fill="white" />
            <path d="M552.507 157.785C552.201 157.785 551.925 157.602 551.803 157.311L550.9 155.091C549.078 150.574 548.113 145.905 548.128 141.557C548.128 128.574 540.167 113.554 530.399 108.012C530.032 107.798 529.894 107.339 530.108 106.971C530.323 106.604 530.782 106.481 531.15 106.68C541.361 112.467 549.659 128.084 549.659 141.511C549.659 145.706 550.578 150.191 552.323 154.524L553.227 156.744C553.38 157.142 553.196 157.586 552.798 157.739C552.706 157.77 552.614 157.801 552.507 157.801V157.785Z" fill="white" />
            <path d="M517.357 148.982C517.051 148.982 516.745 148.783 516.637 148.476C512.779 137.943 511.891 126.231 511.876 126.108C511.845 125.68 512.167 125.328 512.58 125.297C512.948 125.266 513.361 125.588 513.407 126.001C513.407 126.108 514.295 137.622 518.077 147.956C518.214 148.354 518.015 148.798 517.617 148.936C517.525 148.966 517.449 148.982 517.357 148.982Z" fill="white" />
            <path d="M548.008 162.946C547.702 162.946 547.411 162.762 547.288 162.44L545.252 156.944C543.093 151.142 541.96 145.324 541.96 140.118V134.101C541.96 126.952 536.832 118.24 530.524 114.673C527.584 113.004 524.859 112.729 522.869 113.892C520.924 115.025 519.837 117.444 519.837 120.705V126.018C519.837 133.811 521.613 142.553 524.951 151.264L527.477 157.847C527.63 158.245 527.431 158.689 527.033 158.843C526.635 158.996 526.191 158.797 526.038 158.399L523.512 151.815C520.098 142.92 518.291 133.994 518.291 126.018V120.705C518.291 116.878 519.638 113.984 522.073 112.56C524.553 111.106 527.814 111.381 531.259 113.341C537.995 117.153 543.476 126.477 543.476 134.101V140.118C543.476 145.14 544.578 150.774 546.676 156.424L548.712 161.92C548.865 162.318 548.651 162.762 548.253 162.9C548.161 162.93 548.069 162.946 547.993 162.946H548.008Z" fill="white" />
            <path d="M541.255 165.136C540.919 165.136 540.612 164.922 540.521 164.585L539.449 160.987C537.428 154.143 536.387 147.56 536.387 141.421V132.112C536.387 128.269 533.631 123.584 530.232 121.671C528.716 120.813 527.339 120.66 526.343 121.242C525.379 121.808 524.843 123.033 524.843 124.717C524.843 135.052 527.997 146.978 533.708 158.308L535.866 162.564C536.05 162.947 535.912 163.406 535.529 163.59C535.147 163.773 534.687 163.62 534.504 163.253L532.345 158.997C526.527 147.468 523.312 135.297 523.312 124.717C523.312 122.482 524.108 120.767 525.578 119.925C527.063 119.068 528.992 119.206 530.998 120.354C534.81 122.513 537.918 127.795 537.918 132.127V141.436C537.918 147.438 538.928 153.868 540.903 160.574L541.975 164.171C542.097 164.569 541.868 164.998 541.454 165.121C541.378 165.136 541.301 165.151 541.24 165.151L541.255 165.136Z" fill="white" />
            <path d="M532.68 148.982C532.328 148.982 532.006 148.737 531.93 148.369L531.225 145.093C530.552 141.954 530.215 138.724 530.215 135.509V126.874C530.215 126.445 530.552 126.108 530.98 126.108C531.409 126.108 531.746 126.445 531.746 126.874V135.509C531.746 138.617 532.083 141.725 532.726 144.771L533.43 148.048C533.522 148.461 533.262 148.874 532.848 148.951C532.787 148.951 532.741 148.966 532.68 148.966V148.982Z" fill="white" />
            <path d="M509.055 132.371C508.673 132.371 508.336 132.08 508.29 131.682L507.846 127.211C507.8 126.798 508.106 126.415 508.535 126.369C508.994 126.339 509.331 126.63 509.377 127.058L509.821 131.529C509.867 131.942 509.561 132.325 509.132 132.371C509.101 132.371 509.086 132.371 509.055 132.371Z" fill="white" />
            <path d="M574.645 238.606L499.657 196.136C497.513 194.896 495.584 194.987 494.206 196.136L487.73 200.622L492.155 206.087V285.133C492.155 290.66 495.523 297.09 499.657 299.479L572.777 340.877L573.114 346.771L579.943 342.087C581.305 341.061 582.147 339.04 582.147 336.284V252.982C582.147 247.455 578.779 241.025 574.645 238.636V238.606Z" fill="#B3CAFF" />
            <path opacity="0.3" d="M574.645 238.606L499.657 196.136C497.513 194.896 495.584 194.987 494.206 196.136L487.73 200.622L492.155 206.087V285.133C492.155 290.66 495.523 297.09 499.657 299.479L572.777 340.877L573.114 346.771L579.943 342.087C581.305 341.061 582.147 339.04 582.147 336.284V252.982C582.147 247.455 578.779 241.025 574.645 238.636V238.606Z" fill="black" />
            <path d="M568.261 346.313L493.272 303.843C489.123 301.454 485.771 295.024 485.771 289.497V206.195C485.771 200.668 489.139 198.126 493.272 200.515L568.261 242.985C572.41 245.373 575.763 251.804 575.763 257.331V340.633C575.763 346.16 572.395 348.701 568.261 346.313Z" fill="#B3CAFF" />
            <path d="M502.459 229.437C502.031 229.437 501.694 229.1 501.694 228.671V223.512C501.694 222.471 502.23 221.521 503.148 221.001C504.082 220.465 505.2 220.45 506.134 220.986L511.554 224.048C511.921 224.262 512.059 224.721 511.844 225.089C511.63 225.456 511.171 225.579 510.803 225.38L505.384 222.318C504.924 222.057 504.388 222.057 503.929 222.318C503.485 222.578 503.24 223.007 503.24 223.512V228.671C503.24 229.1 502.903 229.437 502.475 229.437H502.459Z" fill="white" />
            <path d="M511.171 300.445C511.048 300.445 510.911 300.414 510.788 300.353L505.736 297.49C503.24 296.081 501.694 293.448 501.694 290.616V285.854C501.694 285.426 502.031 285.089 502.459 285.089C502.888 285.089 503.225 285.426 503.225 285.854V290.616C503.225 292.897 504.48 295.01 506.486 296.158L511.538 299.021C511.906 299.235 512.043 299.695 511.829 300.062C511.691 300.307 511.431 300.445 511.171 300.445Z" fill="white" />
            <path d="M556.887 326.012C556.381 326.012 555.876 325.874 555.417 325.614L549.997 322.552C549.63 322.338 549.492 321.879 549.706 321.511C549.92 321.144 550.38 321.021 550.747 321.22L556.167 324.282C556.626 324.543 557.177 324.543 557.621 324.282C558.065 324.022 558.31 323.593 558.31 323.088V317.929C558.31 317.5 558.647 317.163 559.076 317.163C559.505 317.163 559.841 317.5 559.841 317.929V323.088C559.841 324.129 559.305 325.063 558.387 325.599C557.912 325.875 557.392 326.012 556.887 326.012Z" fill="white" />
            <path d="M559.076 261.511C558.647 261.511 558.31 261.174 558.31 260.745V255.984C558.31 253.703 557.055 251.59 555.049 250.442L549.997 247.579C549.63 247.364 549.492 246.905 549.706 246.538C549.92 246.17 550.38 246.048 550.747 246.247L555.799 249.11C558.295 250.518 559.841 253.152 559.841 255.984V260.745C559.841 261.174 559.505 261.511 559.076 261.511Z" fill="white" />
            <path d="M509.257 255.187C509.364 264.235 510.834 272.947 512.947 280.908C518.78 302.847 541.714 316.351 548.022 301.485C550.533 295.56 552.294 287.996 552.294 278.443C552.294 264.97 542.648 248.588 530.768 241.852C518.642 234.993 509.073 241.362 509.257 255.187Z" fill="white" />
            <path d="M530.004 267.451V281.873L523.742 277.188L530.004 267.451Z" fill="#E0E0E0" />
            <path d="M518.348 258.833C518.348 260.119 519.267 261.696 520.415 262.339C521.548 262.982 522.482 262.461 522.482 261.175C522.482 259.889 521.563 258.312 520.415 257.669C519.282 257.026 518.348 257.547 518.348 258.833Z" fill="#E0E0E0" />
            <path d="M537.532 269.825C537.532 271.111 538.45 272.688 539.599 273.331C540.732 273.974 541.665 273.453 541.665 272.167C541.665 270.881 540.747 269.304 539.599 268.661C538.466 268.018 537.532 268.539 537.532 269.825Z" fill="#E0E0E0" />
            <path d="M527.935 288.931L534.931 287.737C534.931 287.737 533.446 295.208 527.935 288.931Z" fill="#E0E0E0" />
            <path d="M415.081 302.389C415.081 302.389 426.242 301.639 432.566 317.148C436.5 326.793 441.292 341.69 444.354 351.58C446.253 357.735 444.293 364.41 439.363 368.544L419.904 384.864L412.938 372.203C412.938 372.203 420.44 362.19 426.227 357.015L412.938 317.132L415.066 302.374L415.081 302.389Z" fill="#FFA8A7" />
            <path d="M420.225 349.758C420.225 349.758 437.954 352.1 442.378 338.429C442.378 338.429 434.785 318.418 431.294 311.774C427.818 305.129 420.684 300.306 415.065 302.404C409.446 304.501 420.209 349.758 420.209 349.758H420.225Z" fill="#B3CAFF" />
            <path opacity="0.4" d="M420.225 349.758C420.225 349.758 437.954 352.1 442.378 338.429C442.378 338.429 434.785 318.418 431.294 311.774C427.818 305.129 420.684 300.306 415.065 302.404C409.446 304.501 420.209 349.758 420.209 349.758H420.225Z" fill="white" />
            <path d="M408.408 529.284C408.408 529.284 399.987 522.073 396.711 521.736C393.435 521.399 387.77 527.003 392.562 536.74C393.894 539.45 399.314 542.374 402.697 547.564C406.081 552.754 409.128 556.643 414.318 555.939C419.508 555.234 420.181 552.295 419.845 548.911C419.508 545.528 417.977 539.725 417.977 539.725L408.423 529.299L408.408 529.284Z" fill="#37474F" />
            <path d="M414.576 554.377C409.233 555.112 406.094 551.086 402.604 545.743C399.113 540.399 393.54 537.368 392.162 534.582C391.565 533.357 391.121 532.193 390.815 531.091C391.014 532.775 391.535 534.643 392.56 536.725C393.892 539.435 399.312 542.359 402.696 547.549C406.079 552.739 409.126 556.628 414.316 555.924C418.297 555.388 419.613 553.535 419.858 551.178C419.169 552.77 417.669 553.949 414.591 554.377H414.576Z" fill="#263238" />
            <path d="M403.906 538.562C403.783 538.562 403.645 538.532 403.523 538.47C403.125 538.256 402.972 537.766 403.186 537.368C403.293 537.169 405.926 532.331 411.117 531.152C411.561 531.045 411.989 531.336 412.096 531.765C412.204 532.209 411.913 532.637 411.484 532.744C407.044 533.755 404.671 538.088 404.64 538.134C404.487 538.409 404.212 538.562 403.921 538.562H403.906Z" fill="#EBEBEB" />
            <path d="M400.751 535.654C400.613 535.654 400.491 535.623 400.368 535.562C399.97 535.347 399.817 534.857 400.032 534.459C400.139 534.26 402.772 529.422 407.962 528.244C408.406 528.136 408.835 528.427 408.942 528.856C409.049 529.285 408.758 529.729 408.33 529.836C403.89 530.846 401.517 535.179 401.486 535.225C401.333 535.5 401.057 535.654 400.767 535.654H400.751Z" fill="#EBEBEB" />
            <path d="M397.169 532.76C397.046 532.76 396.908 532.729 396.786 532.668C396.388 532.453 396.235 531.963 396.449 531.565C396.556 531.366 399.19 526.528 404.364 525.349C404.808 525.242 405.237 525.533 405.344 525.962C405.451 526.391 405.161 526.835 404.732 526.942C400.292 527.952 397.919 532.285 397.888 532.331C397.735 532.606 397.46 532.76 397.169 532.76Z" fill="#EBEBEB" />
            <path d="M363.563 552.939C363.563 552.939 352.279 549.28 348.911 550.29C345.543 551.301 342.251 559.17 350.978 566.81C353.412 568.938 359.95 569.612 365.431 573.332C370.896 577.068 375.52 579.655 380.343 576.823C385.181 573.99 384.783 570.729 383.925 567.193C382.823 562.691 381.95 559.415 381.95 559.415L363.578 552.924L363.563 552.939Z" fill="#37474F" />
            <path d="M379.977 575.184C375.002 578.109 370.24 575.429 364.606 571.571C358.972 567.728 352.22 567.04 349.709 564.85C348.607 563.886 347.704 562.921 346.954 561.957C347.842 563.533 349.128 565.172 350.98 566.795C353.414 568.923 359.952 569.596 365.433 573.317C370.899 577.052 375.522 579.64 380.345 576.807C384.05 574.633 384.586 572.276 383.866 569.841C383.836 571.694 382.84 573.485 379.977 575.169V575.184Z" fill="#263238" />
            <path d="M360.884 563.364C360.884 563.364 360.823 563.364 360.807 563.364C360.363 563.319 360.027 562.92 360.073 562.477C360.103 562.232 360.7 556.414 365.324 553.122C365.691 552.862 366.197 552.954 366.472 553.321C366.732 553.689 366.641 554.194 366.273 554.469C362.277 557.302 361.711 562.584 361.711 562.645C361.665 563.074 361.313 563.38 360.899 563.38L360.884 563.364Z" fill="#EBEBEB" />
            <path d="M365.539 564.896C365.539 564.896 365.478 564.896 365.463 564.896C365.019 564.85 364.682 564.452 364.728 564.008C364.758 563.763 365.356 557.945 369.979 554.653C370.347 554.393 370.852 554.469 371.127 554.852C371.388 555.22 371.296 555.725 370.928 556C366.932 558.833 366.366 564.115 366.366 564.176C366.32 564.605 365.968 564.911 365.555 564.911L365.539 564.896Z" fill="#EBEBEB" />
            <path d="M356.579 561.787C356.579 561.787 356.518 561.787 356.503 561.787C356.059 561.741 355.722 561.343 355.768 560.899C355.798 560.654 356.396 554.837 361.019 551.545C361.387 551.285 361.892 551.376 362.167 551.744C362.428 552.111 362.336 552.617 361.968 552.892C357.972 555.725 357.406 561.007 357.406 561.068C357.36 561.496 357.008 561.803 356.595 561.803L356.579 561.787Z" fill="#EBEBEB" />
            <path d="M418.482 531.443C419.492 520.267 421.681 502.752 422.6 494.561C424.223 479.925 422.738 468.764 421.712 463.466C421.253 461.032 421.115 458.583 421.36 456.118C423.58 433.857 425.019 397.771 423.794 389.84L392.546 396.454C392.546 396.454 396.374 447.085 398.043 463.91C399.727 480.859 406.019 531.581 406.019 531.581C408.637 533.112 413.383 534.796 418.482 531.428V531.443Z" fill="#455A64" />
            <path d="M413.982 542.926C416.692 542.497 417.962 539.71 417.962 539.71C417.962 537.95 418.161 535.01 418.483 531.428C413.385 534.796 408.639 533.112 406.021 531.581L407.123 540.981C408.394 542.42 411.272 543.354 413.966 542.926H413.982Z" fill="#FFA8A7" />
            <path d="M383.328 551.086C384.599 534.169 387.064 501.375 387.401 496.23C387.738 491.04 387.156 483.875 386.329 479.221C385.793 476.189 385.839 473.082 386.375 470.05L400.108 393.852L362.338 393.301L359.873 473.127C359.843 474.153 359.873 475.194 359.996 476.22L368.003 550.627C368.003 550.627 375.811 557.18 383.344 551.102L383.328 551.086Z" fill="#455A64" />
            <path d="M377.037 565.047C379.747 564.787 382.196 562.506 382.579 561.159C382.579 561.159 382.885 557.117 383.345 551.069C375.827 557.147 368.004 550.595 368.004 550.595L369.198 561.756C369.198 561.756 370.974 565.614 377.037 565.047Z" fill="#FFA8A7" />
            <path d="M379.792 307.472L370.881 310.519L361.297 355.852L361.757 396.056C361.757 396.056 368.6 401.507 395.332 401.507C410.336 401.507 420.103 395.857 423.793 389.841L421.389 365.559C421.389 365.559 423.793 341.629 423.793 329.795C423.793 317.96 424.314 308.269 415.082 302.405L406.845 303.078L379.777 307.472H379.792Z" fill="#B3CAFF" />
            <path opacity="0.2" d="M379.792 307.472L370.881 310.519L361.297 355.852L361.757 396.056C361.757 396.056 368.6 401.507 395.332 401.507C410.336 401.507 420.103 395.857 423.793 389.841L421.389 365.559C421.389 365.559 423.793 341.629 423.793 329.795C423.793 317.96 424.314 308.269 415.082 302.405L406.845 303.078L379.777 307.472H379.792Z" fill="white" />
            <path d="M370.115 268.506L365.66 277.34C365.384 277.891 365.66 278.55 366.242 278.749L371.539 280.509L370.13 268.522L370.115 268.506Z" fill="#F28F8F" />
            <path d="M376.288 253.702C376.288 253.702 369.521 257.346 369.521 278.259C369.521 289.757 370.317 297.091 376.288 296.647C382.259 296.203 399.467 293.937 399.467 293.937V253.702H376.288Z" fill="#FFA8A7" />
            <path d="M376.286 296.648C378.383 296.495 381.874 296.112 385.518 295.683V293.555C382.165 295.27 376.194 296.479 375.291 296.648C375.612 296.678 375.934 296.678 376.286 296.648Z" fill="#F28F8F" />
            <path d="M384.494 292.651V305.772H402.453V287.339L384.494 292.651Z" fill="#FFA8A7" />
            <path d="M384.492 300.78C384.492 300.78 390.953 303.076 396.143 302.173C401.333 301.27 402.451 298.116 402.451 298.116C402.451 298.116 404.135 297.994 406.845 303.076C406.845 303.076 403.676 309.415 396.358 309.844C386.774 310.41 379.777 307.47 379.777 307.47C379.777 307.47 382.272 300.122 384.492 300.78Z" fill="#B3CAFF" />
            <path opacity="0.4" d="M384.492 300.78C384.492 300.78 390.953 303.076 396.143 302.173C401.333 301.27 402.451 298.116 402.451 298.116C402.451 298.116 404.135 297.994 406.845 303.076C406.845 303.076 403.676 309.415 396.358 309.844C386.774 310.41 379.777 307.47 379.777 307.47C379.777 307.47 382.272 300.122 384.492 300.78Z" fill="white" />
            <path d="M344.426 339.73L339.603 347.155C339.603 347.155 327.891 341.935 319.67 338.765C311.448 335.596 306.533 331.202 306.533 331.202C306.533 331.202 307.161 324.925 308.11 321.71C309.06 318.51 309.381 315.816 306.533 317.24C303.686 318.663 301.787 324.206 301.634 324.833C301.634 324.833 285.176 308.298 283.124 308.651C281.073 309.003 281.547 311.498 285.972 316.596L290.397 321.695C290.397 321.695 280.307 327.513 281.946 333.836C283.584 340.159 296.888 343.022 296.888 343.022C296.888 343.022 335.806 372.938 344.441 367.548C353.076 362.175 365.263 346.65 365.263 346.65L344.441 339.699L344.426 339.73Z" fill="#FFA8A7" />
            <path d="M374.403 309.355C374.403 309.355 381.583 315.265 376.899 327.482C372.214 339.7 360.272 354.458 360.272 354.458C360.272 354.458 347.733 350.57 342.91 340.434L358.71 315.709C360.991 312.141 364.819 309.86 369.06 309.585C370.545 309.493 372.321 309.401 374.418 309.355H374.403Z" fill="#B3CAFF" />
            <path opacity="0.4" d="M374.403 309.355C374.403 309.355 381.583 315.265 376.899 327.482C372.214 339.7 360.272 354.458 360.272 354.458C360.272 354.458 347.733 350.57 342.91 340.434L358.71 315.709C360.991 312.141 364.819 309.86 369.06 309.585C370.545 309.493 372.321 309.401 374.418 309.355H374.403Z" fill="white" />
            <path d="M373.699 241.394C373.699 241.394 366.595 240.444 364.789 245.344C363.656 248.39 367.269 250.763 367.269 250.763C367.269 250.763 362.691 255.066 369.52 256.398C369.52 256.398 368.953 262.261 375.047 261.465C375.047 261.465 374.25 266.119 380.237 267.742C380.237 267.742 377.221 275.795 382.901 275.903H388.228L386.085 282.103C386.085 282.103 390.066 291.687 402.467 291.687C402.467 291.687 405.621 285.15 407.09 283.236C408.56 281.322 416.231 271.509 416.231 263.731C416.231 255.954 413.23 251.345 409.096 250.319C409.096 250.319 407.32 246.033 403.156 243.216C398.991 240.399 390.188 237.888 385.228 241.332C385.228 241.332 380.834 235.561 377.787 235.561C374.74 235.561 373.73 238.699 373.73 241.409L373.699 241.394Z" fill="#263238" />
            <path d="M382.897 275.901C382.897 275.901 382.377 267.741 389.251 267.741C396.125 267.741 394.502 282.102 386.066 282.102L382.897 275.901Z" fill="#FFA8A7" />
            <defs>
              <filter id="filter0_d_823_10410" x="0" y="274.684" width="651.41" height="381.156" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="3" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_823_10410" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_823_10410" result="shape" />
              </filter>
              <filter id="filter1_d_823_10410" x="300.737" y="508.699" width="150.281" height="91.8267" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="3" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_823_10410" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_823_10410" result="shape" />
              </filter>
              <filter id="filter2_d_823_10410" x="58.0957" y="360.964" width="291.361" height="173.307" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="3" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_823_10410" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_823_10410" result="shape" />
              </filter>
            </defs>
          </svg>
          <svg width="330" className="background-svg" viewBox="0 0 650 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="650" height="12000" fill="#8BC8FF" />
            <mask id="mask0_823_10389" maskUnits="userSpaceOnUse" x="0" y="0" width="650" height="1080">
              <rect width="650" height="1080" fill="#8BC8FF" />
            </mask>
            <g mask="url(#mask0_823_10389)">
              <path d="M681.937 1014.42C680.115 345.448 159.038 80.1603 -102.183 31.1133L-115.916 -68.0273L681.937 -79.6289V1014.42C681.943 1016.77 681.943 1019.11 681.937 1021.46V1014.42Z" fill="#9ED1FF" />
            </g>
          </svg>
        </div>
      </div>
    </>
  );
};

export default Login;