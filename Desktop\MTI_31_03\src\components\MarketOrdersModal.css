/* Modal Base Styles */
.modal-overlay {
  position: fixed; /* Ensures it covers the whole screen */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.market-orders-modal {
  width: 90%;
  max-width: 800px;
  height: auto;
  margin: auto;
  padding: 0;
  position: relative;
  border-radius: 10px;
  background: white;
  display: flex;
  flex-direction: column;
  outline: none;
  border: none;
}

/* Modal Header */
.modal-header {
  color: white;
  background-color: #32406d;
  width: 100%;
  padding: 15px;
  text-align: center;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-weight: bold;
  font-size: 1.1rem;
}

/* Modal Content */
.modal-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  max-height: calc(80vh - 120px);
}

/* Client Info Section */
.client-info {
  display: flex;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.info-row {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: #333;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  font-weight: normal;
}

.value-highlight {
  color: green;
  font-weight: bold;
}

/* Settings Title */
.settings-title {
  color: orange;
  margin-bottom: 10px;
  font-size: 1rem;
}

/* Description Text */
.description {
  margin-bottom: 15px;
  line-height: 1.5;
}

/* Checkbox Styles */
.checkbox-container {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.checkbox-input {
  accent-color: green;
}

.checkbox-label {
  margin-left: 10px;
  font-weight: bold;
}

/* Form Controls */
.settings-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.form-group {
  flex: 1 1 300px;
}

.form-label {
  color: #32406d;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.form-control {
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ccc;
  width: 100%;
}

.form-hint {
  color: purple;
  font-size: 0.8rem;
  margin-top: 5px;
  font-weight: bold;
}

/* Description Boxes */
.description-box {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Chase Limit Row */
.chase-limit-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.chase-limit-input {
  flex: 0 0 200px;
}

.chase-limit-hint {
  margin-left: 15px;
  flex: 1;
}

/* Warning Note */
.warning-note {
  text-align: center;
  padding: 10px;
  margin-top: 10px;
  color: orange;
  font-weight: bold;
  background-color: #fff9e6;
  border-radius: 5px;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top: 1px solid #eee;
}

/* Button Styles */
.btn {
  border: none;
  border-radius: 5px;
  padding: 8px 20px;
  margin: 0 10px;
  cursor: pointer;
  font-weight: bold;
  transition: opacity 0.2s;
}

.btn:hover {
  opacity: 0.9;
}

.btn-save {
  background-color: green;
  color: white;
}

.btn-cancel {
  background-color: red;
  color: white;
}

/* Responsive Adjustments */
@media (max-width: 600px) {
  .market-orders-modal {
    width: 95%;
  }

  .settings-row {
    flex-direction: column;
    gap: 10px;
  }

  .chase-limit-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .chase-limit-hint {
    margin-left: 0;
    margin-top: 5px;
  }
}
