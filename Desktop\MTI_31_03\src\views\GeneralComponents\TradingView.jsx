import React, { useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Cookies from "universal-cookie";
import GeneralSection from "../GeneralComponents/GeneralSection";
import AppearanceSection from "../GeneralComponents/AppearanceSection";
import SimulatedTradingSection from "../GeneralComponents/SimulatedTradingSection";
import QTPChromePluginSection from "../GeneralComponents/QTPChromePluginSection";
import OptionsPortfolioSection from "../GeneralComponents/OptionsPortfolioSection";
import TradingViewSection from "../GeneralComponents/TradingViewSection";
import BrokersSection from "../GeneralComponents/BrokersSection";
import AmiBrokerSection from "../GeneralComponents/AmiBrokerSection";
import {
  OptimizedMarketIndex,
  OptimizedLeftNav,
  OptimizedRightNav,
} from "../../components/Layout/OptimizedComponenets";
import { fetchWithAuth } from "../../utils/api";

const cookies = new Cookies();
const SECTIONS = [
    "General", "Appearance", "Simulated Trading", "QTP Chrome Plugin",
    "Options Portfolio", "Trading View", "Brokers", "AmiBroker"
];
const BROKERS_LIST = [
    { id: "ib", name: "Interactive Brokers" },
    { id: "alpaca", name: "Alpaca" },
    { id: "zerodha", name: "Zerodha" },
    { id: "upstox", name: "Upstox" }
];

const NavigationBar = ({ activeSection, onSectionChange }) => (
    <div style={{
        position: "fixed",
        top: "70px",
        left: "140px",
        width: "73%",
        zIndex: 100,
        padding: "10px 0",
        background: "rgba(255, 255, 255, 0.05)",
        color: "red",
        backdropFilter: "blur(10px)",
        backgroundColor: "#D8E1FF",
        borderRadius: "12px",
        border: "1px solid rgba(255, 255, 255, 0.1)",
    }}>
        <ul style={{
            display: "flex",
            justifyContent: "flex-start",
            listStyle: "none",
            padding: "0 20px",
            gap: "10px",
            maxWidth: "1400px",
            margin: "0 auto",
        }}>
            {SECTIONS.map((item) => (
                <li key={item}>
                    <button
                        style={{
                            padding: "8px 16px",
                            border: "none",
                            borderRadius: "8px",
                            background: activeSection === item ? "linear-gradient(90deg, #3b82f6, #8b5cf6)" : "rgba(255, 255, 255, 0.05)",
                            color: activeSection === item ? "#fff" : "#000",
                            fontWeight: "600",
                            fontSize: "14px",
                            cursor: "pointer",
                            transition: "all 0.3s ease",
                            position: "relative",
                            overflow: "hidden",
                            boxShadow: activeSection === item ? "0 4px 12px rgba(59, 130, 246, 0.3)" : "none",
                        }}
                        onClick={() => onSectionChange(item)}
                        onMouseEnter={(e) => {
                            if (activeSection !== item) {
                                e.target.style.background = "rgba(255, 255, 255, 0.1)";
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (activeSection !== item) {
                                e.target.style.background = "rgba(255, 255, 255, 0.05)";
                            }
                        }}
                    >
                        {item}
                        {activeSection === item && (
                            <span style={{
                                position: "absolute",
                                bottom: "0",
                                left: "50%",
                                transform: "translateX(-50%)",
                                width: "50%",
                                height: "2px",
                                background: "linear-gradient(90deg, #3b82f6, #8b5cf6)",
                                borderRadius: "2px",
                            }}></span>
                        )}
                    </button>
                </li>
            ))}
        </ul>
    </div>
);

function TradingView() {
    const [ activeSection, setActiveSection ] = useState("General");

    const [ isWebhookEnabled, setWebhookEnabled ] = useState(false);
    const [ webhookUrl, setWebhookUrl ] = useState("");
    const [ strategy, setStrategy ] = useState("");
    const [ isPluginEnabled, setPluginEnabled ] = useState(false);

    const [ capital, setCapital ] = useState("");
    const [ priceType, setPriceType ] = useState("");
    const [ leverage, setLeverage ] = useState({
        equity: { mis: 1, coBo: 1, nrml: 1 },
        futures: { mis: 1, coBo: 1, nrml: 1 },
        options: { mis: 1, coBo: 1, nrml: 1 },
        mcx: { mis: 1, coBo: 1, nrml: 1 }
    });
    const [ selectedUser, setSelectedUser ] = useState("");

    const [ quickTradeEnabled, setQuickTradeEnabled ] = useState(false);
    const [ ninjaProvider, setNinjaProvider ] = useState("TrueData");
    const [ qtpApiKey, setQtpApiKey ] = useState("");

    const [ defaultStrategy, setDefaultStrategy ] = useState("NIFTY PUT NIFTY CALL");
    const [ product, setProduct ] = useState("MIS");
    const [ combinedTarget, setCombinedTarget ] = useState("");
    const [ combinedSL, setCombinedSL ] = useState("");
    const [ legTarget, setLegTarget ] = useState("");
    const [ legSL, setLegSL ] = useState("");
    const [ strikeAdjustment, setStrikeAdjustment ] = useState("");

    const [ selectedBroker, setSelectedBroker ] = useState("");
    const [ brokerApiKey, setBrokerApiKey ] = useState("");
    const [ apiSecret, setApiSecret ] = useState("");

    const [ isAmiBrokerEnabled, setAmiBrokerEnabled ] = useState(false);
    const [ aflCode, setAflCode ] = useState("");
    const [ dataSource, setDataSource ] = useState("");

    const mainUser = cookies.get("USERNAME");
    const navigate = useNavigate();
    const { portfolios } = useSelector(state => state.portfolioReducer);
    const { executedPortfolios } = useSelector(state => state.executedPortfolioReducer);

    const filteredPortfolios = portfolios
        .filter(port => !executedPortfolios.some(ep => ep.portfolio_name === port.portfolio_name))
        .filter(port => port.enabled);

    const fetchWebhookUrl = useCallback(async () => {
        try {
            const response = await fetchWithAuth(`/api/create_webhook/${mainUser}`, {
                method: "POST",
            });
            if (response.ok) {
                const data = await response.json();
                setWebhookUrl(data.webhook_url);
                if (filteredPortfolios.length > 0) setStrategy(filteredPortfolios[ 0 ].portfolio_name);
            }
        } catch (error) {
            console.error("Error fetching webhook URL:", error);
        }
    }, [ mainUser, filteredPortfolios ]);

    const handleLeverageChange = useCallback((type, key, value) => {
        setLeverage(prev => ({
            ...prev,
            [ type ]: { ...prev[ type ], [ key ]: value }
        }));
    }, []);

    const handleSave = useCallback(() => {
        console.log("Saving settings...");
        // Add actual save logic here
    }, []);

    const handleCancel = useCallback(() => {
        navigate("/F&O/Portfolio");
    }, [ navigate ]);

    return (
        <div style={{
            display: "flex",
            minHeight: "100vh",
            background: "linear-gradient(135deg, #ffffff, #f5f7fa)",
            fontFamily: "'Segoe UI', Roboto, sans-serif"
        }}>
            <div style={{
                position: "fixed",
                top: "70px",
                left: "0",
                width: "75px",
                height: "90%",
                zIndex: 200,
                background: "#f5f7fa",
            }}>
                <OptimizedLeftNav />
            </div>

            <div style={{
                position: "fixed",
                top: "0",
                left: "0",
                width: "100%",
                zIndex: 150,
                background: "#fff",
                borderBottom: "1px solid #e0e0e0"
            }}>
                <OptimizedMarketIndex />
            </div>
            <div>
                <NavigationBar
                    activeSection={activeSection}
                    onSectionChange={setActiveSection}
                />
            </div>


            <div style={{
                marginLeft: "120px",
                width: "75%",
                paddingTop: "130px",
                paddingBottom: "20px",
                display: "flex",
                justifyContent: "center",
            }}>

                <div style={{
                    width: "100%",
                    background: "#fff",
                    borderRadius: "12px",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.05)",
                    padding: "1px",
                    transition: "all 0.3s ease",
                    overflow: "visible"
                }}>


                    {activeSection === "General" && (
                        <GeneralSection onCancel={handleCancel} />
                    )}

                    {activeSection === "Appearance" && (
                        <AppearanceSection />
                    )}

                    {activeSection === "Simulated Trading" && (
                        <SimulatedTradingSection
                            capital={capital}
                            setCapital={setCapital}
                            priceType={priceType}
                            setPriceType={setPriceType}
                            leverage={leverage}
                            handleLeverageChange={handleLeverageChange}
                            selectedUser={selectedUser}
                            setSelectedUser={setSelectedUser}
                        />
                    )}

                    {activeSection === "QTP Chrome Plugin" && (
                        <QTPChromePluginSection
                            quickTradeEnabled={quickTradeEnabled}
                            setQuickTradeEnabled={setQuickTradeEnabled}
                            ninjaProvider={ninjaProvider}
                            setNinjaProvider={setNinjaProvider}
                            apiKey={qtpApiKey}
                            setApiKey={setQtpApiKey}
                        />
                    )}

                    {activeSection === "Options Portfolio" && (
                        <OptionsPortfolioSection
                            defaultStrategy={defaultStrategy}
                            setDefaultStrategy={setDefaultStrategy}
                            product={product}
                            setProduct={setProduct}
                            combinedTarget={combinedTarget}
                            setCombinedTarget={setCombinedTarget}
                            combinedSL={combinedSL}
                            setCombinedSL={setCombinedSL}
                            legTarget={legTarget}
                            setLegTarget={setLegTarget}
                            legSL={legSL}
                            setLegSL={setLegSL}
                            strikeAdjustment={strikeAdjustment}
                            setStrikeAdjustment={setStrikeAdjustment}
                        />
                    )}

                    {activeSection === "Trading View" && (
                        <TradingViewSection
                            isWebhookEnabled={isWebhookEnabled}
                            setWebhookEnabled={setWebhookEnabled}
                            webhookUrl={webhookUrl}
                            fetchWebhookUrl={fetchWebhookUrl}
                            strategy={strategy}
                            setStrategy={setStrategy}
                            filteredPortfolios={filteredPortfolios}
                            isPluginEnabled={isPluginEnabled}
                            setPluginEnabled={setPluginEnabled}
                            onConfigure={handleSave}
                        />
                    )}

                    {activeSection === "Brokers" && (
                        <BrokersSection
                            selectedBroker={selectedBroker}
                            setSelectedBroker={setSelectedBroker}
                            brokers={BROKERS_LIST}
                            apiKey={brokerApiKey}
                            setApiKey={setBrokerApiKey}
                            apiSecret={apiSecret}
                            setApiSecret={setApiSecret}
                            onSave={handleSave}
                            onCancel={handleCancel}
                        />
                    )}

                    {activeSection === "AmiBroker" && (
                        <AmiBrokerSection
                            isAmiBrokerEnabled={isAmiBrokerEnabled}
                            setAmiBrokerEnabled={setAmiBrokerEnabled}
                            aflCode={aflCode}
                            setAflCode={setAflCode}
                            dataSource={dataSource}
                            setDataSource={setDataSource}
                            onSave={handleSave}
                            onCancel={handleCancel}
                        />
                    )}
                </div>
            </div>

            <div style={{
                position: "fixed",
                top: "80px",
                right: "-1280px",
                width: "100%",
                height: "100%",
                zIndex: 200,
                background: "#f5f7fa",
            }}>
                <OptimizedRightNav />
            </div>
        </div>
    );
}

export default TradingView;
