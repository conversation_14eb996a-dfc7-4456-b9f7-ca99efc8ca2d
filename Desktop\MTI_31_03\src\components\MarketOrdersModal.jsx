import React, { useState, useEffect } from 'react';
import Modal from 'react-modal';
import './MarketOrdersModal.css';

const MarketOrdersModal = ({
    isOpen,
    onClose,
    selectedUserId,
    selectedBroker,
    selectedName,
    onSave
}) => {
    const [ isMarketOrdersDisabled, setIsMarketOrdersDisabled ] = useState(false);
    const [ orderType, setOrderType ] = useState('');
    const [ limitPriceAdjust, setLimitPriceAdjust ] = useState('');
    const [ maxModifications, setMaxModifications ] = useState('');
    const [ modificationTime, setModificationTime ] = useState('');
    const [ forceExecute, setForceExecute ] = useState(false);
    const [ maxChaseLimit, setMaxChaseLimit ] = useState('');

    useEffect(() => {
        if (isOpen) {
            // Here you could fetch saved settings for this user
            // For now just reset the form
        }
    }, [ isOpen, selectedUserId ]);

    const handleSubmit = () => {
        const settings = {
            userId: selectedUserId,
            isMarketOrdersDisabled,
            orderType,
            limitPriceAdjust,
            maxModifications,
            modificationTime,
            forceExecute,
            maxChaseLimit,
        };
        onSave(settings);
        onClose();
    };

    // Restrict input to numbers, decimal, and percentage
    const handleNumericInput = (e) => {
        // Allow numbers, decimal point, percentage, and minus sign
        e.target.value = e.target.value.replace(/[^0-9.%\-]/g, '');
    };

    // Description text based on selected order type
    const getOrderTypeDescription = () => {
        switch (orderType) {
            case 'BidAsk':
                return 'Limit Price will be derived using the current Bid & Ask data by applying some buffer. If Bid/Ask is not available with Broker Feed then LTP will be used with some higher buffer. Limit Price Adjust can also be applied.';
            case 'PriceSpread':
                return 'Limit Price will be derived using the Current LTP by applying the "Limit Price Adjust" mentioned above. Price Adjust can be in Point/%/Negative. Eg: If LTP is 500 and adjust was mentioned as 10% then for Buy Limit Price would be 550.';
            case 'BidAskAgressive':
                return 'Limit Price will be derived using the best Bid & Ask data by applying some nominal buffer. If Bid/Ask is not available from Feed then LTP will be used with slightly higher buffer. "Limit Price Adjust" can also be applied.';
            case 'BidAskkeepOnTop':
                return 'MTI will try to keep you on the Top of the Bid for Buy and Ask for the Sell. However, you can use Limit Price Adjust to adjust the price further.';
            default:
                return '';
        }
    };

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            contentLabel="Market Orders Modal"
            className="market-orders-modal"
            overlayClassName="modal-overlay"
        >
            <div className="modal-header">
                MTI BRIDGE
            </div>

            <div className="modal-content">
                <div className="client-info">
                    <div>
                        <div className="info-row">
                            <span className="label">Client ID:</span>
                            <span className="value value-highlight">{selectedUserId || "-"}</span>
                        </div>
                        <div className="info-row">
                            <span className="label">Broker:</span>
                            <span className="value value-highlight">{selectedBroker || "-"}</span>
                        </div>
                        <div className="info-row">
                            <span className="label">Name:</span>
                            <span className="value value-highlight">{selectedName || "-"}</span>
                        </div>
                    </div>
                </div>

                <h5 className="settings-title">SET IF "MARKET" ORDERS ARE NOT ALLOWED FOR THE USER</h5>

                <p className="description">
                    If this is set, MTI will internally change all MARKET orders to LIMIT orders as per the below settings for the selected user.
                </p>

                <div className="checkbox-container">
                    <input
                        type="checkbox"
                        id="marketOrdersDisabled"
                        checked={isMarketOrdersDisabled}
                        onChange={() => setIsMarketOrdersDisabled(!isMarketOrdersDisabled)}
                        className="checkbox-input"
                    />
                    <label htmlFor="marketOrdersDisabled" className="checkbox-label">
                        Market Orders are not Allowed
                    </label>
                </div>

                {isMarketOrdersDisabled && (
                    <div className="conditional-settings">
                        <div className="settings-row">
                            <div className="form-group">
                                <label htmlFor="orderType" className="form-label">
                                    Limit Price Selection Based on Order Type:
                                </label>
                                <select
                                    id="orderType"
                                    value={orderType}
                                    onChange={(e) => setOrderType(e.target.value)}
                                    className="form-control"
                                >
                                    <option value="" disabled>Select</option>
                                    <option value="BidAsk">BidAsk</option>
                                    <option value="PriceSpread">PriceSpread</option>
                                    <option value="BidAskAgressive">BidAskAgressive</option>
                                    <option value="BidAskkeepOnTop">BidAsk_keepOnTop</option>
                                </select>
                            </div>

                            <div className="form-group">
                                <label htmlFor="limitPriceAdjust" className="form-label">
                                    Limit Price Adjust:
                                </label>
                                <input
                                    type="text"
                                    id="limitPriceAdjust"
                                    value={limitPriceAdjust}
                                    onChange={(e) => setLimitPriceAdjust(e.target.value)}
                                    onInput={handleNumericInput}
                                    className="form-control"
                                />
                                <p className="form-hint">
                                    (Limit Price can be Negative, In Points or % Eg 1, 0.2%, -0.10 etc)
                                </p>
                            </div>
                        </div>

                        {orderType && (
                            <div className="description-box">
                                {getOrderTypeDescription()}
                            </div>
                        )}

                        <div className="settings-row">
                            <div className="form-group">
                                <label htmlFor="maxModifications" className="form-label">
                                    Max Modifications:
                                </label>
                                <input
                                    type="number"
                                    id="maxModifications"
                                    value={maxModifications}
                                    onChange={(e) => setMaxModifications(e.target.value)}
                                    min="0"
                                    className="form-control"
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="modificationTime" className="form-label">
                                    Modification time in seconds:
                                </label>
                                <input
                                    type="number"
                                    id="modificationTime"
                                    value={modificationTime}
                                    onChange={(e) => setModificationTime(e.target.value)}
                                    min="0"
                                    className="form-control"
                                />
                            </div>
                        </div>

                        <div className="checkbox-container">
                            <input
                                type="checkbox"
                                id="forceExecute"
                                checked={forceExecute}
                                onChange={() => setForceExecute(!forceExecute)}
                                className="checkbox-input"
                            />
                            <label htmlFor="forceExecute" className="checkbox-label">
                                Force Execute On that Last Attempt
                            </label>
                        </div>

                        <div className="description-box">
                            After specified time, MTI will check for the Order and if that is still not filled then it will re-modify the order as per above selection. If you don't want modifications then set Max Modifications as 0.
                        </div>

                        <div className="chase-limit-row">
                            <div className="form-group chase-limit-input">
                                <label htmlFor="maxChaseLimit" className="form-label">
                                    Max Chase Limit:
                                </label>
                                <input
                                    type="text"
                                    id="maxChaseLimit"
                                    value={maxChaseLimit}
                                    onChange={(e) => setMaxChaseLimit(e.target.value)}
                                    onInput={handleNumericInput}
                                    className="form-control"
                                />
                            </div>
                            <div className="chase-limit-hint">
                                <p className="form-hint">
                                    (Can be in Points or % Eg 1, 0.2% etc)
                                </p>
                            </div>
                        </div>

                        <div className="description-box">
                            If specified, MTI will stop modifying the Order if the price has moved beyond the chasing limit. This can be very helpful where the market moves very sharply and continuous modification can give fill at the worst price.
                        </div>

                        {/* Final Note */}
                        <div className="warning-note">
                            These settings will be applicable only when the Original Order was MARKET and MTI converts that to LIMIT.
                        </div>
                    </div>
                )}
            </div>

            {/* Footer/Buttons */}
            <div className="modal-footer">
                <button
                    onClick={handleSubmit}
                    className="btn btn-save"
                >
                    Save
                </button>
                <button
                    onClick={onClose}
                    className="btn btn-cancel"
                >
                    Cancel
                </button>
            </div>
        </Modal>
    );
};

export default MarketOrdersModal;