import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  strategies: []
};


export const strategySlice = createSlice({
  name: "strategy",
  initialState,
  reducers: {
    setStrategies: (state, action) => {
      return {
        ...state,
        strategies: action.payload.strategies || [],
      };
    },
  },
});

export const { setStrategies } = strategySlice.actions;

export default strategySlice.reducer;
