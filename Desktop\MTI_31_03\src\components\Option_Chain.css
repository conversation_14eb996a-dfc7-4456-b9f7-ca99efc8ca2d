.under {
  width: 85%;
  height: 37px;
  /* margin-top: -10px; */
  padding: 8px;
  border: 1px solid #000000;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
  margin-left: 1px;
}
.exchange3 {
  width: 100%;
  height: 34px;
  margin-top: 20px;
  padding: 8px;
  border: 1px solid #000000;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
}
.group1 {
  margin-left: 30px;
  margin-top: 19px;
}

.portfolioLots2::-webkit-inner-spin-button,
.portfolioLots2::-webkit-outer-spin-button {
  -webkit-appearance: inner-spin-button;
}

.portfolioLots2 {
  -moz-appearance: textfield;
}
.portfolioLots2::-webkit-inner-spin-button {
  height: 30px;
}

.portfolioLots2::-webkit-outer-spin-button {
  height: 30px;
}
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 15px;
  margin-top: 10px;
  margin-left: 0px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 25px;
  width: 25px;
  left: -1px;
  bottom: -5px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: rgb(90, 208, 90);
}

input:focus + .slider {
  box-shadow: 0 0 1px green;
}

input:checked + .slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
  background-color: green;
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
.switch-container {
  display: flex;
  justify-content: center;
}
.nifty2 {
  color: #4661bd;
  font-family: "Roboto-Bold, sans-serif";
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
}

.orderflowtable-alt {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #b3b0b0;
  margin-top: 20px;
}

.middle-main-container .main-table table.orderflowtable-alt thead th {
  border-right: 0.5px solid #b3b0b0 !important;
  padding: 3px !important;
  width: 30px;
}

.middle-main-container
  .main-table
  table.orderflowtable-alt
  thead
  th5
  div:first-child {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-evenly;
  padding: 2px 33px;
}
.orderflowtable-alt thead {
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 3px;
}
.orderflowtable-alt thead tr {
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 5px;
}

.main-table table.orderflowtable-alt thead th.calls::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 1px;
  background-color: #b3b0b0;
}
.main-table table.orderflowtable-alt thead th.puts::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 1px;
  background-color: #b3b0b0;
}

.main-table table.orderflowtable-alt thead th.calls,
.main-table table.orderflowtable-alt thead th.puts {
  position: sticky;
  z-index: 1;
}

.main-table table.orderflowtable-alt thead th.strike::after {
  display: none;
}

.orderflowtable-alt tbody tr {
  height: 30px;
}

.optionChainTableDiv {
  margin-bottom: 20px;
  width: 100%;
}

.refreshBtn button:hover {
  cursor: pointer !important;
}

.timeDiv {
  color: rgb(0, 38, 91);
  margin-left: 210px;
  height: 20px;
  width: 230px;
  margin-top: 35px;
}
