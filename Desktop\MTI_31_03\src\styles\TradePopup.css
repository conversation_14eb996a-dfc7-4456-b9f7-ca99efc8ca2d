/* TradePopup.css - Styling for the trade popup in Master_accounts.jsx */

.trade-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.trade-popup-container {
  position: fixed;
  top: 10%;
  left: 30%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  border-radius: 8px;
  width: 700px;
  max-width: 90vw;
}

.trade-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eaeaea;
}

.trade-popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.trade-popup-close {
  background: none;
  border: none;
  font-size: 40px;
  cursor: pointer;
  color: #666;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.trade-popup-close:hover {
  background-color: #f0f0f0;
  color: #333;
}

.trade-form {
  background-color: #f8f9fa;
  padding: 5px;
  border-radius: 8px;
}

/* Radio button groups */
.radio-group {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  flex-wrap: wrap;
  align-items: center;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
}

.radio-label input[type="radio"] {
  margin-right: 5px;
  cursor: pointer;
}

/* Buy/Sell radio buttons */
.transaction-type {
  display: flex;
  gap: 15px;
  margin-bottom: 5px;
}

.transaction-type label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-weight: 600;
}

.transaction-type input[type="radio"] {
  margin-right: 5px;
  cursor: pointer;
}

/* Symbol and exchange container */
.symbol-exchange-container {
  display: flex;
  margin-bottom: 5px;
  align-items: flex-start;
  gap: 10px;
}

.exchange-container select {
  height: 36px;
  border-radius: 5px;
  width: 90px;
  border: 1px solid #d9d9d9;
  padding: 0 10px;
  font-size: 14px;
}

.symbol-container {
  position: relative;
  flex-grow: 1;
}

.symbol-input {
  width: 280px;
  padding: 8px 10px;
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid #d9d9d9;
}

.symbol-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  color: #999;
}

.symbol-dropdown {
  border: 1px solid #d9d9d9;
  max-height: 200px;
  overflow-y: auto;
  width: 100%;
  text-align: left;
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: absolute;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.symbol-dropdown-item {
  cursor: pointer;
  padding: 8px 12px;
  text-align: left;
  width: 100%;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  font-size: 14px;
}

.symbol-dropdown-item:hover,
.symbol-dropdown-item.selected {
  background-color: #e6f7ff;
}

/* Order details section */
.order-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

/* Duration and validity section */
.duration-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.duration-label {
  font-size: 14px;
  font-weight: 500;
  margin-right: 5px;
  color: #333;
}

.input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-label {
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
}

.input-field {
  height: 36px;
  width: 80px;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #d9d9d9;
  padding: 0 5px;
}

.input-field:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.input-field.error,
.symbol-input.error,
select.error {
  border-color: #ff4d4f;
  background-color: #fff1f0;
}

.lot-size {
  color: #1890ff;
  font-size: 12px;
  margin-top: 3px;
}

.ltp-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 36px;
  width: 80px;
  background-color: #f0f9ff;
  border-radius: 5px;
  border: 1px solid #d6e8ff;
  padding: 0 8px;
}

.ltp-value {
  font-size: 14px;
  font-weight: bold;
  color: #1890ff;
  flex-grow: 1;
  text-align: center;
}

.ltp-refresh {
  width: 16px;
  height: 16px;
  cursor: pointer;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.ltp-refresh:hover {
  transform: rotate(90deg);
}

/* Warning and error messages */
.validation-messages {
  margin: 0 0 15px 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.warning-message {
  padding: 8px 12px;
  background-color: #fff3cd;
  color: #856404;
  border-radius: 5px;
  border: 1px solid #ffeeba;
  font-size: 13px;
  font-weight: bold;
  text-align: center;
}

.error-message {
  color: #721c24;
  font-size: 13px;
  font-weight: bold;
  background-color: #f8d7da;
  padding: 8px 12px;
  border-radius: 5px;
  border: 1px solid #f5c6cb;
  text-align: center;
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background-color: #5a6268;
}

.submit-btn {
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.submit-btn.buy {
  background-color: #28a745;
}

.submit-btn.buy:hover {
  background-color: #218838;
}

.submit-btn.sell {
  background-color: #dc3545;
}

.submit-btn.sell:hover {
  background-color: #c82333;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Account selection */
.account-selection {
  margin-bottom: 15px;
  margin-left: 200px;
}

.account-select {
  height: 36px;
  border-radius: 5px;
  width: 100%;
  max-width: 300px;
  padding: 0 10px;
  font-size: 14px;
  border: 1px solid #d9d9d9;
}

/* Checkbox styling */
.checkbox-container {
  margin-top: 5px;
  margin-bottom: 15px;
  margin-left: 25px;
  display: flex;
  align-items: center;
}

.checkbox-container-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 5px;
  cursor: pointer;
}

/* Split order styling */
.split-order-container {
  margin-top: -61px;
  margin-left: 450px;
}

.split-order-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
}
