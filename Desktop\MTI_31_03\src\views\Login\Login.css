* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.body {
  display: flex;
  min-height: 100vh;
  font-family: "Roboto", sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

.right-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.right-container__box {
  width: 100%;
  max-width: 400px;
  padding: 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.right-container-box {
  text-align: center;
}

.right-container__h2 {
  font-size: 28px;
  font-weight: 700;
}

.right-container__p {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  opacity: 0.7;
}

/* Input Container */
.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.right-container__label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 1px;
  text-align: left;
}

.right-container__input1 {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  transition: border-color 0.2s;
}
.right-container__input2 {
  width: 100%;
  padding: 12px;
  margin-top: -15px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  transition: border-color 0.2s;
}

.right-container__input1:focus {
  border-color: #007bff;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  font-size: 18px;
}

.forgot-password-link {
  text-align: right;
  margin-top: 10px;
}

.forgot-password-link a {
  font-size: 14px;
  color: #007bff;
  text-decoration: none;
}

.forgot-password-link a:hover {
  text-decoration: underline;
}

/* Button */
.btn {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  background-color: #4a6cf7;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 20px;
}

.btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.btn:hover:not(:disabled) {
  background-color: #3a56d1;
}

.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-loading span {
  margin-left: 8px;
}

/* Divider */
.line-with-text {
  display: flex;
  align-items: center;
  margin: 20px 0;
  text-align: center;
}

.grey-line {
  flex: 1;
  height: 1px;
  background-color: #ccc;
}

.text-between-lines {
  margin: 0 10px;
  font-size: 14px;
  color: #666;
}

/* Social Sign-In */
.social-sign-in {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 20px;
}

.SignInWithGoogle,
.SignInWithApple {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
  transition: border-color 0.2s;
  background-color: #fff;
}

.SignInWithGoogle img,
.SignInWithApple img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.SignInWithGoogle:hover,
.SignInWithApple:hover {
  border-color: #007bff;
}

/* Bottom Text */
.right-container__bottom-text {
  font-size: 14px;
  text-align: center;
  margin-top: 20px;
  color: #333;
}

.right-container__bottom-text strong a {
  color: #4a6cf7;
  text-decoration: none;
}

.right-container__bottom-text strong a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (min-width: 768px) {
  .decoration-container {
    display: block;
  }

  .right-container__box {
    max-width: 450px;
  }
}

@media (max-width: 860px) {
  .body {
    flex-direction: column;
  }

  .right-container {
    padding: 20px;
  }

  .social-sign-in {
    flex-direction: column;
  }
}
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.decoration-container {
  position: relative; /* Allows absolute positioning of SVGs inside */
  width: 50%; /* Takes half the screen on larger devices */
  height: 100vh; /* Full height of the viewport */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* Prevents SVGs from overflowing */
}

.background-svg {
  position: absolute;
  top: 0%;
  left: 23%;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: cover;
}

.illustration-svg {
  position: relative;
  z-index: 2;
  max-width: 80%;
  max-height: 80%;
  margin-left: 100px;
}

.right-container {
  width: 50%; /* Takes the other half of the screen */
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .decoration-container {
    display: none;
  }

  .right-container {
    width: 100%;
    padding: 20px;
  }
}
